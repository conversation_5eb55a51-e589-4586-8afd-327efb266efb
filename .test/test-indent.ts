// 测试缩进处理的示例数据
const testContent = [
  `1.1.除双方另有明确相反书面约定外,本协议及任何《广告服务订单》中的下列术语具有以下特定含义:
(1)"投放平台"是指:由第三方提供的可供展示广告内容的网站、网页、应用软件、程序等广告媒介，如微信朋友圈、微信公众号、腾讯广点通(含QQ广告、腾讯网、天天快报、腾讯视频)、腾讯联盟、百度信息流、小米信息流、聚效360、WPS、InMobi、快手、字节系广告(今日头条、抖音、抖音极速版、西瓜视频、火山小视频、番茄小说、Ohayoo精品游戏、穿山甲等)、小红书等互联网媒体平台，以及相关第三方广告投放服务提供方平台(如广告媒介资源交易平台等)。
(2)"广告账户"是指:乙方为甲方在投放平台创建、存续并可用于广告费消耗的虚拟账户(无论是否以甲方名称或甲方产品或服务命名)。
(3)"广告主"是指:对外宣传、推广其经营的产品或服务的商品经营者或者服务提供者。`,
  
  `1.2.本协议中凡提及的任何法律、法规、条例、规章或其他类似法律文件均应包括该等法律文件后续的修订、重新颁布或整理的内容，以及依据该等法律文件而制定的其他法律法规、规章或其他法律文件。`
];

// 期望的输出格式：
/*
1.1.除双方另有明确相反书面约定外,本协议及任何《广告服务订单》中的下列术语具有以下特定含义:
  (1)"投放平台"是指:由第三方提供的可供展示广告内容的网站、网页、应用软件、程序等广告媒介，如微信朋友圈、微信公众号、腾讯广点通(含QQ广告、腾讯网、天天快报、腾讯视频)、腾讯联盟、百度信息流、小米信息流、聚效360、WPS、InMobi、快手、字节系广告(今日头条、抖音、抖音极速版、西瓜视频、火山小视频、番茄小说、Ohayoo精品游戏、穿山甲等)、小红书等互联网媒体平台，以及相关第三方广告投放服务提供方平台(如广告媒介资源交易平台等)。
  (2)"广告账户"是指:乙方为甲方在投放平台创建、存续并可用于广告费消耗的虚拟账户(无论是否以甲方名称或甲方产品或服务命名)。
  (3)"广告主"是指:对外宣传、推广其经营的产品或服务的商品经营者或者服务提供者。

1.2.本协议中凡提及的任何法律、法规、条例、规章或其他类似法律文件均应包括该等法律文件后续的修订、重新颁布或整理的内容，以及依据该等法律文件而制定的其他法律法规、规章或其他法律文件。
*/

// 复杂嵌套示例
const complexContent = [
  `4.1.甲方就其广告服务需求应当提前以《广告服务订单》的样式或其他乙方认可的形式向乙方提出，并详细载明以下事项:
(1)广告服务类型;
(2)广告服务内容;
(3)广告主名称及联系方式;
(4)推广对象;
(5)投放平台名称。
就广告投放服务，还应载明以下事项:
(1)广告主在投放平台的广告账户名称及编号;
(2)广告费计费方式和投放广告预算(即广告费上限)。
乙方应当于收到后的五个工作日内向甲方提供反馈。`
];

// 期望输出：
/*
4.1.甲方就其广告服务需求应当提前以《广告服务订单》的样式或其他乙方认可的形式向乙方提出，并详细载明以下事项:
  (1)广告服务类型;
  (2)广告服务内容;
  (3)广告主名称及联系方式;
  (4)推广对象;
  (5)投放平台名称。
就广告投放服务，还应载明以下事项:
  (1)广告主在投放平台的广告账户名称及编号;
  (2)广告费计费方式和投放广告预算(即广告费上限)。
乙方应当于收到后的五个工作日内向甲方提供反馈。
*/

export { testContent, complexContent };
