const fs = require('fs');
const path = require('path');

// 模拟 renderSectionContent 函数
function detectIndentLevel(line, previousIndent) {
  // 一级编号：数字.数字. (如 1.1., 2.3.)
  if (line.match(/^\d+\.\d+\./)) {
    return 0;
  }

  // 二级编号：(数字) (如 (1), (2))
  if (line.match(/^\(\d+\)/)) {
    return 2;
  }

  // 三级编号：(字母) (如 (a), (b))
  if (line.match(/^\([a-zA-Z]\)/)) {
    return 4;
  }

  // 四级编号：(中文数字) (如 (一), (二))
  if (line.match(/^\([一二三四五六七八九十百千万]+\)/)) {
    return 4;
  }

  // 五级编号：数字) (如 1), 2))
  if (line.match(/^\d+\)/)) {
    return 6;
  }

  // 六级编号：字母) (如 a), b))
  if (line.match(/^[a-zA-Z]\)/)) {
    return 8;
  }

  // 特殊格式：带引号的编号 (如 "投放平台"是指:)
  if (line.match(/^"[^"]+"\s*[是为]指[:：]/)) {
    return 2;
  }

  // 特殊格式：就...而言、关于...等开头
  if (line.match(/^就.+[而言|方面|事宜]/)) {
    return 2;
  }

  // 检查是否是纯数字开头的条款 (如 "1.甲方应当...")
  if (line.match(/^\d+\./)) {
    return 0;
  }

  // 检查是否是罗马数字 (如 I., II., III.)
  if (line.match(/^[IVX]+\./)) {
    return 0;
  }

  // 检查是否是中文数字开头 (如 一、二、三、)
  if (line.match(/^[一二三四五六七八九十百千万]+[、。]/)) {
    return 0;
  }

  // 智能缩进检测
  return smartIndentDetection(line, previousIndent);
}

function smartIndentDetection(line, previousIndent) {
  // 如果是明显的内容延续（不以编号开头），适当增加缩进
  const isContentContinuation = !line.match(/^[\d\(\)a-zA-Z"一二三四五六七八九十]/);
  
  if (isContentContinuation) {
    // 如果前一行是编号行，内容应该有适当缩进
    if (previousIndent < 8) {
      return Math.min(previousIndent + 2, 8);
    }
  }

  // 检查是否是分号结尾的条款（通常是列表项）
  if (line.endsWith(';') || line.endsWith('；')) {
    return Math.max(previousIndent, 2);
  }

  // 检查是否是句号结尾的完整条款
  if (line.endsWith('.') || line.endsWith('。')) {
    return previousIndent;
  }

  // 默认继承前一行缩进
  return previousIndent;
}

function renderSectionContent(content) {
  let md = '';

  content.forEach((c) => {
    const lines = c.split('\n');

    let previousIndent = 0;
    lines.forEach((line) => {
      const trimmedLine = line.trim();

      if (!trimmedLine) {
        md += '\n';
        previousIndent = 0;
        return;
      }

      // 判断当前行的缩进级别
      const currentIndent = detectIndentLevel(trimmedLine, previousIndent);

      // 应用缩进并添加行
      md += ' '.repeat(currentIndent) + trimmedLine + '\n';
      previousIndent = currentIndent;
    });

    // 在每个content块之间添加空行
    md += '\n';
  });

  return md;
}

// 测试所有JSON文件
function testAllJsonFiles() {
  const refersDir = '../data/refers';
  const files = fs.readdirSync(refersDir).filter(file => file.endsWith('.json'));
  
  console.log(`找到 ${files.length} 个JSON文件，开始测试缩进处理...\n`);
  
  files.forEach((filename, index) => {
    console.log(`\n=== 测试文件 ${index + 1}/${files.length}: ${filename} ===`);
    
    try {
      const filePath = path.join(refersDir, filename);
      const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      // 测试主要内容块
      if (jsonData.main_content_block && jsonData.main_content_block.sections) {
        console.log('📋 主要内容块:');
        jsonData.main_content_block.sections.forEach((section, sectionIndex) => {
          if (section.content && Array.isArray(section.content)) {
            console.log(`\n  章节 ${sectionIndex + 1}: ${section.title || '无标题'}`);
            const rendered = renderSectionContent(section.content);
            
            // 显示前几行作为示例
            const lines = rendered.split('\n').slice(0, 10);
            lines.forEach(line => {
              if (line.trim()) {
                const indentCount = line.length - line.trimStart().length;
                console.log(`    [缩进${indentCount}] ${line.trimStart().substring(0, 80)}${line.trimStart().length > 80 ? '...' : ''}`);
              }
            });
          }
        });
      }
      
      // 测试标准条款块
      if (jsonData.standard_clauses_block && jsonData.standard_clauses_block.sections) {
        console.log('\n📋 标准条款块:');
        jsonData.standard_clauses_block.sections.forEach((section, sectionIndex) => {
          if (section.content && Array.isArray(section.content)) {
            console.log(`\n  章节 ${sectionIndex + 1}: ${section.title || '无标题'}`);
            const rendered = renderSectionContent(section.content);
            
            // 显示前几行作为示例
            const lines = rendered.split('\n').slice(0, 5);
            lines.forEach(line => {
              if (line.trim()) {
                const indentCount = line.length - line.trimStart().length;
                console.log(`    [缩进${indentCount}] ${line.trimStart().substring(0, 80)}${line.trimStart().length > 80 ? '...' : ''}`);
              }
            });
          }
        });
      }
      
      console.log('✅ 处理完成');
      
    } catch (error) {
      console.log(`❌ 处理失败: ${error.message}`);
    }
  });
}

// 运行测试
testAllJsonFiles();
