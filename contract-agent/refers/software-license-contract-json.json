{"template_name": "标准产品许可合同模板", "description": "适用于客户授予标准大数据软件产品使用许可的场景，规定了产品内容、许可期限、费用、双方权利义务、知识产权、保密等条款。", "keywords": ["大数据软件许可", "标准产品", "<PERSON><PERSON><PERSON>", "使用授权", "年度订阅"], "contract_type": "大数据软件产品许可协议", "file_name_prefix": "standard_bigdata_software_license_contract", "llm_group_id_order": ["metadata_block", "main_content_block", "payment_block", "standard_clauses_block", "signature_block", "attachments_block"], "template_version": "20220607", "template_note": "标准产品许可合同模板", "metadata_block": {"llm_group_id": "metadata_block", "contract_title_template": "[__CONTRACT_TITLE_PREFIX__] 大数据软件产品许可协议", "contract_number_placeholder": "合同编号：[__CONTRACT_NUMBER__]", "sign_date_placeholder": "签订时间：[__SIGN_YEAR__]年[__SIGN_MONTH__]月"}, "parties_block": {"llm_group_id": "metadata_block", "party_a": {"role_description": "许可方（甲方）", "name_placeholder": "名称：[__PARTY_A_NAME__]", "address_placeholder": "联系地址：[__PARTY_A_ADDRESS__]", "contact_placeholder": "联系方式：[__PARTY_A_CONTACT__]"}, "party_b": {"role_description": "被许可方（乙方）", "name_fixed": "名称：上海脉策数据科技有限公司", "address_fixed": "联系地址：上海市杨浦区政立路421号10层", "contact_fixed": "联系方式：021-61070586"}, "relationship_definition": "甲方和乙方在本合同中单独称为“一方”，合称“双方”。"}, "preamble_block": {"llm_group_id": "main_content_block", "text_content": "甲乙双方根据《中华人民共和国民法典》及相关法律法规的规定，本着平等自愿的原则，经双方友好协商一致，特签订本《大数据软件产品许可协议》(以下简称“本协议”)，以兹共同遵守。"}, "sections_block": {"sections": [{"section_number": "1", "section_title": "产品内容及许可期限", "llm_group_id": "main_content_block", "subsections": [{"subsection_number": "1.1", "subsection_title": "产品内容", "content_intro_text": "本协议提供的大数据软件产品许可包括以下内容：", "format": "list", "list_content": ["**Datlas软件年度许可**：Datlas软件是由乙方享有著作权，交付并许可甲方使用的大数据软件产品，能够为城市相关行业提供全面强大的数据查询、分析、管理以及智能计算服务。", "**年度数据订阅**：乙方负责定期更新，数据内容包括：[__DATA_SUBSCRIPTION_DETAILS__] (例如：人口数据(规模、结构、通勤)、城市数据(区位配套、办公、交通、商业市场、土地情况等))", "**标准化功能模块**：", "- [__FUNCTIONAL_MODULE_1__] (例如：客户研究，根据生命周期法，制作客户地图)", "- [__FUNCTIONAL_MODULE_2__] (例如：板块研判，板块多维度价值及市场综合评估)", "**备注**：", "1. 详细内容见[__ATTACHMENT_1_REFERENCE__]；", "2. 脉策Datlas软件产品、订阅数据及乙方标准化功能模块，以下统称“乙方产品”。"], "purpose": "明确许可产品的具体内容", "variable_fields": ["数据内容", "功能模块", "附件引用"]}, {"subsection_number": "1.2", "subsection_title": "许可期限", "content": "乙方产品的许可期限为[__LICENSE_PERIOD__] (例如：一年(365天))，自本产品完成在线交付之日起计算。乙方向合同约定或甲方指定的邮箱、手机号码发送开通本产品使用权限的邮件、短信，即视为乙方完成在线交付。", "format": "text", "purpose": "规定许可期限", "variable_fields": ["许可期限"]}]}, {"section_number": "2", "section_title": "合同金额及支付方式", "llm_group_id": "payment_block", "subsections": [{"subsection_number": "2.1", "subsection_title": "费用", "content_intro_text": "作为甲方使用乙方本协议项下产品的对价，甲方应向乙方支付许可费总额人民币[__TOTAL_FEE_AMOUNT__]元(大写：[__TOTAL_FEE_AMOUNT_IN_WORDS__]万元整)(含6%的增值税，“费用”)。具体而言，本许可费包含以下部分：", "format": "list", "list_content": ["<PERSON><PERSON><PERSON>软件年度许可：[__DATLAS_ANNUAL_FEE__]元/年", "年度数据订阅：[__DATA_SUBSCRIPTION_ANNUAL_FEE__]元/年", "标准化功能模块：[__MODULES_ANNUAL_FEE__]元/年"], "purpose": "规定许可费用金额", "variable_fields": ["费用总额", "各项具体费用"]}, {"subsection_number": "2.2", "subsection_title": "支付方式", "content_intro_text": "合同金额由甲方分[__PAYMENT_INSTALLMENTS_NUMBER__]期支付给乙方，具体支付时间和金额约定如下：", "format": "list", "list_content": ["**第一期**：本合同签订后支付合同总金额的60%，即人民币[__INSTALLMENT_1_AMOUNT__]元(大写：[__INSTALLMENT_1_AMOUNT_IN_WORDS__]万元整)", "**第二期**：交付完成后支付合同总金额的40%，即人民币[__INSTALLMENT_2_AMOUNT__]元(大写：[__INSTALLMENT_2_AMOUNT_IN_WORDS__]万元整)"], "additional_content": "在每一期支付条件达成后，乙方向甲方开具合法有效的、与实际支付金额一致的增值税专用发票。甲方在收到发票后[__PAYMENT_DAYS_AFTER_INVOICE__]个工作日内向乙方指定的账户全额支付当期款项。", "purpose": "规定付款方式、时间节点和金额比例", "variable_fields": ["分期数", "每期付款触发条件", "每期付款金额比例", "付款时限"]}, {"subsection_number": "2.3", "subsection_title": "乙方开户信息", "content_intro_text": "乙方开户银行名称、地址和账号为：", "format": "list", "list_content": ["户名：上海脉策数据科技有限公司", "开户银行：上海浦东发展银行杨浦支行", "账号：9812 0154 7400 1146 3", "纳税人名称：上海脉策数据科技有限公司", "纳税人识别号：91310110342309083H"], "purpose": "提供乙方收款账户信息", "variable_fields": []}, {"subsection_number": "2.4", "subsection_title": "甲方开票信息", "content_intro_text": "甲方开票信息：", "format": "list", "list_content": ["名称：[__PARTY_A_INVOICE_NAME__]", "纳税人识别号：[__PARTY_A_TAX_ID__]", "地址：[__PARTY_A_INVOICE_ADDRESS__]", "电话：[__PARTY_A_INVOICE_PHONE__]", "开户行：[__PARTY_A_INVOICE_BANK__]", "账号：[__PARTY_A_INVOICE_ACCOUNT__]"], "purpose": "提供甲方开票所需信息", "variable_fields": ["名称", "纳税人识别号", "地址", "电话", "开户行", "账号"]}]}, {"section_number": "3", "section_title": "双方权利义务", "llm_group_id": "main_content_block", "subsections": [{"subsection_number": "3.1", "subsection_title": "甲方义务", "format": "list", "list_content": ["甲方应按照合同约定及时足额地向乙方支付合同款项。", "甲方不得将本合同项下的乙方产品账号提供给任何第三方，包括但不限于甲方的分公司、子公司、参股公司等关联公司。", "甲方应当在法律允许的范围内使用本合同项下的数据服务，甲方保证向乙方提供的或在使用乙方产品过程中使用的任何数据或信息均不以任何方式或途径侵犯任何第三方的合法权益(包括但不限于知识产权、隐私权等)，亦不会造成任何第三方向乙方追索任何赔偿或补偿等。", "甲方知悉并同意乙方为实现本合同服务内容而开通并使用维护账号登录平台系统。服务完成后，经甲方书面通知，乙方应关闭维护账号的功能。", "乙方提供的技术服务仅供甲方在中国大陆范围内使用，甲方不得违反法律规定向境外（包含中国香港、澳门、台湾）传输从乙方获取的数据。", "若因甲方未能履行本条款约定的义务导致乙方提供服务延后的，乙方的交付期限应相应延长，且不视为乙方违约。"], "purpose": "规定甲方的义务和限制", "variable_fields": []}, {"subsection_number": "3.2", "subsection_title": "乙方义务", "format": "list", "list_content": ["乙方需根据本合同的服务内容向甲方提供平台系统的使用手册及使用培训，并及时响应甲方提出的与产品相关技术问题。", "乙方会尽最大努力保障软件系统性能稳定和运行正常。除本合同另有约定外，若乙方单方原因致使本软件系统发生功能异常或产生其他导致甲方无法正常使用本软件系统的技术性问题，乙方应在收到甲方通知后及时响应并尽快完成功能修复等技术支持工作。但乙方无法保证其所提供的服务毫无瑕疵，也无法随时预见和防范法律、技术以及其他风险，包括但不限于不可抗力、病毒、木马、黑客攻击、系统不稳定、第三方服务瑕疵、政府行为等原因可能导致的服务中断、数据丢失以及其他的损失和风险。"], "purpose": "规定乙方的义务和责任限制", "variable_fields": []}]}, {"section_number": "4", "section_title": "知识产权", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "4.1", "content": "乙方保证其许可甲方使用的乙方产品是其合法拥有知识产权或已获得合法授权的产品，不侵犯任何第三方的知识产权。", "format": "text"}, {"subsection_number": "4.2", "content": "乙方产品（包括但不限于其目标代码、源代码、图形界面、文档资料、数据）的全部知识产权（包括但不限于著作权、专利权、商标权、商业秘密）均归乙方所有，或乙方已获得合法授权。", "format": "text"}, {"subsection_number": "4.3", "content": "甲方承认乙方的知识产权，并承诺不以任何方式侵犯乙方的知识产权。本协议的任何条款均不得被解释为乙方将其任何知识产权转让或许可给甲方，除非本协议明确约定。甲方不得对乙方产品进行复制（为备份目的且仅限一份副本除外）、修改、反编译、反向工程、分解、试图导出源代码或创作衍生作品。", "format": "text"}, {"subsection_number": "4.4", "content": "如第三方就乙方产品向甲方主张知识产权侵权，乙方应负责处理并承担由此产生的全部责任和费用；如因此给甲方造成损失的，乙方应予以赔偿。", "format": "text"}]}, {"section_number": "5", "section_title": "保密条款", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "5.1", "content": "任何一方对于因签署或履行本协议而了解或接触到的对方的商业秘密、技术信息及其他未公开信息（以下简称“保密信息”）均应承担保密义务。非经对方书面同意，任何一方不得向任何第三方泄露、给予或转让该等保密信息。", "format": "text"}, {"subsection_number": "5.2", "content": "乙方产品的源代码、技术文档、数据结构、算法设计等均属于乙方的核心商业秘密，甲方应严格保密，不得以任何形式向任何第三方透露。", "format": "text"}, {"subsection_number": "5.3", "content": "本保密义务在本协议终止后[__CONFIDENTIALITY_PERIOD_YEARS__]年内持续有效，不因本协议的终止而终止。", "format": "text"}, {"subsection_number": "5.4", "content": "法律法规另有规定或为履行本协议所必需的除外。", "format": "text"}]}, {"section_number": "6", "section_title": "违约责任", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "6.1", "content": "任何一方违反本协议的任何约定，均应承担相应的违约责任，并赔偿由此给对方造成的损失（包括但不限于直接损失、间接损失及合理的律师费、诉讼费等）。", "format": "text"}, {"subsection_number": "6.2", "content": "若甲方逾期支付本协议项下任何一期款项，每逾期一日，应按逾期支付金额的[__LATE_PAYMENT_PENALTY_RATE__]%向乙方支付违约金，但该违约金总额不超过逾期支付金额的[__MAX_LATE_PAYMENT_PENALTY_PERCENTAGE__]%。逾期超过[__DAYS_OVERDUE_FOR_TERMINATION__]日的，乙方有权单方解除本协议并要求甲方承担相应损失。", "format": "text"}, {"subsection_number": "6.3", "content": "若甲方违反本协议第3.1条（甲方义务）中关于账号使用、数据使用范围或知识产权保护的约定，或违反第5条（保密条款）的约定，乙方有权立即终止本协议，并要求甲方支付[__IP_INFRINGEMENT_OR_CONFIDENTIALITY_BREACH_PENALTY__]元的违约金；若违约金不足以弥补乙方损失的，乙方有权进一步追偿。", "format": "text"}]}, {"section_number": "7", "section_title": "不可抗力", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "7.1", "content": "任何一方因不可抗力（如战争、自然灾害、政府行为等）导致无法履行或无法完全履行本协议义务的，不承担违约责任。但遇不可抗力的一方应立即通知对方，并在合理期限内提供证明文件，并采取积极措施尽量减少损失。", "format": "text"}]}, {"section_number": "8", "section_title": "法律适用与争议解决", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "8.1", "content": "本协议的订立、效力、解释、履行及争议的解决均适用中华人民共和国法律。", "format": "text"}, {"subsection_number": "8.2", "content": "因本协议引起的或与本协议有关的任何争议，双方应首先通过友好协商解决；协商不成的，任何一方均有权向[__ARBITRATION_BODY_OR_COURT__]（例如：乙方所在地有管辖权的人民法院）提起诉讼或仲裁。", "format": "text"}]}, {"section_number": "9", "section_title": "通知与送达", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "9.1", "content": "本协议项下的所有通知、请求或其它通讯应以书面形式发送至本协议首页所列的双方地址、电子邮箱或传真（若有）。以邮寄方式发出的，邮件寄出后[__DAYS_FOR_MAIL_DELIVERY__]日视为送达；以传真或电子邮件方式发出的，发出当日视为送达。", "format": "text"}]}, {"section_number": "10", "section_title": "其他", "llm_group_id": "standard_clauses_block", "subsections": [{"subsection_number": "10.1", "content": "本协议构成双方之间就本协议标的达成的完整协议，取代先前就此标的进行的所有口头或书面通讯、陈述或许诺。", "format": "text"}, {"subsection_number": "10.2", "content": "本协议的任何条款如被有管辖权的法院裁定为无效或不可执行，该等条款的无效或不可执行不影响本协议其他条款的效力。", "format": "text"}, {"subsection_number": "10.3", "content": "本协议附件（若有）为本协议不可分割的组成部分，与本协议具有同等法律效力。", "format": "text"}, {"subsection_number": "10.4", "content": "本协议一式[__NUMBER_OF_COPIES__]份，甲乙双方各执[__COPIES_PER_PARTY__]份，具有同等法律效力，自双方授权代表签字盖章之日起生效。", "format": "text"}]}]}, "signature_block": {"llm_group_id": "signature_block", "party_a_signature_area": "甲方（盖章）：\n授权代表（签字）：\n日期：[__PARTY_A_SIGN_DATE__]", "party_b_signature_area": "乙方（盖章）：\n授权代表（签字）：\n日期：[__PARTY_B_SIGN_DATE__]"}, "attachments_block": {"llm_group_id": "attachments_block", "attachment_1_title": "附件一：[__ATTACHMENT_1_TITLE__] (例如：《产品内容及报价单》)", "attachment_placeholder_if_any": "（具体内容详见附件）"}}