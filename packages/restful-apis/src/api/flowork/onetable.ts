import { FLOWORK_URL } from '../../config';
import {
  IApiResponse,
  IOnetableApprovalTask,
  IOnetableApprovalTasksQueryPost,
  IOnetableAssignTask,
  IOnetableAssignTasksQueryPost,
  IOnetableDownstreamStat,
  IOnetableDownstreamStatPost,
  IOnetableDownstreamUsers,
  IOnetableDownstreamUsersPost,
  IOnetableExecuteCommandsPost,
  IOnetableExecuteCommandsResult,
  IOnetableGrantedForm,
  IOnetableGrantedFormQueryPost,
  IOnetableInvolvedForm,
  IOnetableInvolvedFormsQueryPost,
  IOnetableInvolvedUsers,
  IOnetableInvolvedUsersQueryPost,
  IOnetableManagedForm,
  IOnetableManagedFormsQueryPost,
  IOnetableManageTask,
  IOnetableManageTasksQueryPost,
  IPaginationQuery,
  IRequestPromise,
  IRequestRequestConfig,
} from '../../interfaces';
import request from '../../request';

const innerUrl = `${FLOWORK_URL}/custom/onetable`;

export const excuteOnetableCommands = (data: IOnetableExecuteCommandsPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/execute_commands`,
  }) as IRequestPromise<IApiResponse<IOnetableExecuteCommandsResult>>;
};

export const queryOnetableDownstreamUsers = (data: IOnetableDownstreamUsersPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_downstream_users`,
  }) as IRequestPromise<IApiResponse<IOnetableDownstreamUsers>>;
};

export const queryOnetableDownstreamStat = (data: IOnetableDownstreamStatPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_downstream_stat`,
  }) as IRequestPromise<IApiResponse<IOnetableDownstreamStat[]>>;
};

export const queryOnetableGrantedForms = (data: IOnetableGrantedFormQueryPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_granted_forms`,
  }) as IRequestPromise<IApiResponse<IOnetableGrantedForm[]>>;
};

export const queryOnetableApprovalTasks = (
  data: IOnetableApprovalTasksQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_approval_tasks`,
  }) as IRequestPromise<IApiResponse<IOnetableApprovalTask[]>>;
};

export const queryOnetableAssignTasks = (
  data: IOnetableAssignTasksQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_assign_tasks`,
  }) as IRequestPromise<IApiResponse<IOnetableAssignTask[]>>;
};

export const queryOnetableManageTasks = (
  data: IOnetableManageTasksQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_manage_tasks`,
  }) as IRequestPromise<IApiResponse<IOnetableManageTask[]>>;
};

export const queryOnetableInvolvedForms = (
  data: IOnetableInvolvedFormsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_involved_forms`,
  }) as IRequestPromise<IApiResponse<IOnetableInvolvedForm[]>>;
};

export const queryOnetableInvolvedUsers = (data: IOnetableInvolvedUsersQueryPost, config?: IRequestRequestConfig) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_involved_users`,
  }) as IRequestPromise<IApiResponse<IOnetableInvolvedUsers>>;
};

export const queryOnetableManagedForms = (
  data: IOnetableManagedFormsQueryPost,
  config?: IRequestRequestConfig<IPaginationQuery>,
) => {
  return request({
    ...(config || {}),
    data,
    method: 'post',
    url: `${innerUrl}/query_managed_forms`,
  }) as IRequestPromise<IApiResponse<IOnetableManagedForm[]>>;
};
