// 获取可用于配置的权限
import { AUTH_URL } from '../../config';
import {
  IApiResponse,
  IRequestPromise,
  IRequestRequestConfig,
  IResourceAuthorizationPut,
  IResourceAuthorizationQuery,
  IResourceAuthorizationResult,
  IResourcePermissionQuery,
  IResourcePermissionResult,
  IResourceQuery,
  IResourceResult,
} from '../../interfaces';
import request from '../../request';

// todo_xiao resouceType改为enum
export const queryResourcePermission = (
  resouceType: string,
  config?: IRequestRequestConfig<IResourcePermissionQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_URL}/rbac/resource/permission/${resouceType}`,
  }) as IRequestPromise<IApiResponse<IResourcePermissionResult>>;
};

export const queryResourceAuthorization = (
  resouceType: string,
  config?: IRequestRequestConfig<IResourceAuthorizationQuery>,
) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_URL}/rbac/${resouceType}/authorization`,
  }) as IRequestPromise<IApiResponse<IResourceAuthorizationResult[]>>;
};

export const putResourceAuthorization = (
  resouceType: string,
  data: IResourceAuthorizationPut,
  config?: IRequestRequestConfig,
) => {
  return request({
    ...(config || {}),
    method: 'put',
    data: data,
    url: `${AUTH_URL}/rbac/${resouceType}/authorization`,
  }) as IRequestPromise<IApiResponse<string>>;
};

export const queryResource = (config?: IRequestRequestConfig<IResourceQuery>) => {
  return request({
    ...(config || {}),
    method: 'get',
    url: `${AUTH_URL}/rbac/resource`,
  }) as IRequestPromise<IApiResponse<IResourceResult[]>>;
};

export const postQueryResource = (data?: IResourceQuery, config?: IRequestRequestConfig<IResourceQuery>) => {
  return request({
    ...(config || {}),
    method: 'post',
    data: data,
    url: `${AUTH_URL}/rbac/resource`,
  }) as IRequestPromise<IApiResponse<IResourceResult[]>>;
};
