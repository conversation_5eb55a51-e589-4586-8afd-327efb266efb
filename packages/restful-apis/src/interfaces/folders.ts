import { IPaginationQuery } from './comm';

export interface IFolderPath {
  path: string;
}

export interface IFolders {
  path: string;
  create_time?: number;
  resources?: any[];
}

export interface IQueryFolders extends IPaginationQuery {
  space: string;
  path?: string;
  project_version_id?: string;
  recursive?: boolean;
  resource_type?: string;
  folder_orderby?: string;
  orderby?: string;
  struct?: boolean;
}

export interface IPostFoldersParams {
  space: string;
  path?: string;
  resources?: Record<string, any>;
  project_version_id?: string;
}

export interface IPatchFoldersParams {
  space: string;
  path?: string;
  new_path?: string;
  select_subfolders?: string[];
  select_resources?: [string, string][];
}

export interface IDeleteFoldersParams {
  space: string;
  path?: string;
  resource_type?: string;
  resource_ids?: string;
  keepme?: boolean;
  project_version_id?: string;
  recursive?: boolean;
}
