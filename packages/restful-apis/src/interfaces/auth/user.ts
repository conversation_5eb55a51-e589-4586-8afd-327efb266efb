import { IPaginationQuery } from '../comm';

export interface IUserPostAdmin {
  app_id: number;
  enable: boolean;
  expire_time: number;
  permission?: number[];
  role?: number[];
  tag?: string[];
}

export interface IUserQuery extends IPaginationQuery {
  download?: boolean;
  basic_info?: boolean;
  enable_status?: 'all' | 'enable' | 'disable';
  name_like?: string;
  role_name_like?: string;
  role_ids?: string;
}

export interface IUserPost {
  email: string;
  phone?: string;
  area_code?: number;
  login_double_check?: boolean;
  password?: string;
  name: string;
  admin: IUserPostAdmin[];
}

export interface IUserPostExcel {
  file: File;
}

export interface IUser {
  id: number;
  name: string;
  expire_time: string;
  app_id: number;
  area_code: string;
  email: string;
  enable: true;
  permission: Record<string, number[]>;
  phone: string;
  role: number[];
  tags: string[];
  uuid: string;
}

export interface IUserPutAdmin {
  app_id: number;
  enable?: boolean;
  expire_time?: number;
  new_permission?: number[];
  del_permission?: number[];
  new_role?: number[];
  del_role?: number[];
  new_tag?: string[];
  del_tag?: string[];
}

export interface IUserPut {
  id: number;
  name?: string;
  phone?: string;
  email?: string;
  admin?: IUserPutAdmin[];
  new_admin?: IUserPutAdmin[];
  old_password?: string;
  password?: string;
}

export interface IUserPutBatch {
  app_id: number;
  ids: number[];
  enable?: boolean;
  expire_time?: number;
  new_permission?: number[];
  del_permission?: number[];
  new_role?: number[];
  del_role?: number[];
  new_tag?: string[];
  del_tag?: string[];
}

export interface IImpersonaterecordApp {
  id: number;
  name: string;
  user_amount: number;
  last_impersonate_time: string;
}

export interface ISendBindMsgByPhone {
  phone: string;
  user_uuid: string;
  area_code?: number;
}

export interface ISendBindMsgByEmail {
  email: string;
  user_uuid: string;
  area_code?: number;
}

export interface IShareResourcePermissionsQuery {
  grantor: string;
}

export interface IShareResourcePermissions {
  resource_id: string;
  resource_type: string;
  sub_type: string;
  condition: object;
  expire_time?: string;
  update_time: number;
  privilege_type: string;
  // 分享者，群组分享时会返回
  grantor?: number;
}
export interface IResourcePermission {
  resource_id: string;
  resource_type: string;
  privilege_type: string;
}
export interface IShareResourcePermissionsPatch {
  new_resources?: IResourcePermission[];
  del_resources?: IResourcePermission[];
}

export interface IImpersonateAppListParams {
  order_by?: string;
}

export interface IUserIdRoleIdQueryItem {
  app_id: number;
  user_id: number[];
}
export interface IUserIdRoleIdQuery {
  role_types?: string[];
  users?: IUserIdRoleIdQueryItem[];
  user_uuids?: string[];
}

export interface IUserIdRoleId {
  app_id: number;
  role_id: number;
  user_id: number;
  user_uuid: string;
}

export interface IUserRoleItem {
  depth: number;
  is_main: boolean;
  path: string;
  name: string;
  role_id: number;
  is_leader: boolean;
  role_type: string;
}

export interface IUserRolesParams {
  with_inherit?: boolean;
  role_types?: string;
  app_id?: number;
  user_id?: number;
  is_leader?: boolean;
  max_depth?: number;
  superior?: boolean;
  is_main?: boolean;
  subordinate?: boolean;
}
