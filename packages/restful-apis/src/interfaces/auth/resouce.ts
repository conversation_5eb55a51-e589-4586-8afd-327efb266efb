export interface IResourcePermissionQuery {
  level: string; // 权限级别
  subtype?: string; // 资源子类, 例如vault的子类graph
  target?: string; // 资源id列表
  private?: boolean; // 是否为私有
  grantee_id?: number; // 获取具体被授权对象的资源权限
  app_id?: number; // 限定grantee的app_id
  permission_detail?: boolean; // 是否返回资源的详细权限
}

export interface IResourcePermissionResult {
  granted: string[]; // 已授权资源id列表
  not_granted: string[]; // 未授权资源id列表
  deleted: string[]; // 已删除资源id列表
}

export interface IResourceAuthorizationQuery {
  app_ids?: string; // 权限级别
  resource_ids: string;
  privilege_type?: string;
  grantee_type?: string;
}

export interface IResourceAuthorizationResult {
  resource_id: string;
  app_id: number;
  grantee: number;
  condition?: Record<string, any>;
  privilege_type: string;
  name: string;
  grantee_type: string;
}

export interface IResourceAuthorizationPutGranteeWithPrivilege {
  grantee_id: number;
  grantee_app_id: number;
  privilege_list: string[];
}

export interface IResourceAuthorizationPut {
  resource_type: string; // 权限级别
  resource_ids?: string;
  grantee_with_privilege: IResourceAuthorizationPutGranteeWithPrivilege[];
}

export interface IResourceQuery {
  aggregate_result?: boolean;
  action?: string;
  resource_info?: string;
  // 多种资源类型以逗号分割
  resource_types?: string;
  user_uuid?: string;
  privs_equal?: string;
  with_condition?: boolean;
  sort_action?: boolean;
  privs_include?: string;
  resource_ids?: string | string[];
  user_id?: number;
  app_id?: number;
  grantee_id?: number; // 获取具体被授权对象的资源权限
}

export interface IResourceResult {
  resource_id: string;
  resource_type: string;
  sub_type: string;
  privilege_type: string;
  privs: string[];
}
