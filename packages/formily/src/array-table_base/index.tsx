/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/rules-of-hooks */
import _ from 'lodash';
import React, { createContext, useContext } from 'react';
import { SortableHandle } from 'react-sortable-hoc';
import { ArrayField } from '@formily/core';
import { JSXComponent, RecordScope, RecordsScope, Schema, useField, useFieldSchema } from '@formily/react';
import { clone, isValid } from '@formily/shared';
import cls from 'classnames';
import { Button, ButtonProps } from '@mdtDesign/button';
import { Icon, IconProps } from '@mdtDesign/icon';
import { usePrefixCls } from '../__builtins__';
import i18n from '../languages';

export interface IArrayTableBaseAdditionProps extends ButtonProps {
  title?: string;
  method?: 'push' | 'unshift';
  defaultValue?: any;
}

export interface IArrayTableBaseContext {
  props: IArrayTableBaseProps;
  field: ArrayField;
  schema: Schema;
}

export interface IArrayTableBaseItemProps {
  index: number;
  record: any;
}

interface IIconExtends {
  index?: number;
  type?: 'icon' | 'button';
}

export interface IArrayTableBaseMixins {
  Addition?: React.FC<React.PropsWithChildren<IArrayTableBaseAdditionProps>>;
  Copy?: React.FC<React.PropsWithChildren<IconProps & IIconExtends>>;
  Remove?: React.FC<React.PropsWithChildren<(IconProps | ButtonProps) & IIconExtends>>;
  MoveUp?: React.FC<React.PropsWithChildren<IconProps & IIconExtends>>;
  MoveDown?: React.FC<React.PropsWithChildren<IconProps & IIconExtends>>;
  SortHandle?: React.FC<React.PropsWithChildren<IconProps & { index?: number }>>;
  Index?: React.FC;
  useArray?: () => IArrayTableBaseContext;
  useIndex?: (index?: number) => number;
  useRecord?: (record?: number) => any;
}

export interface IArrayTableBaseProps {
  disabled?: boolean;
  onAdd?: (index: number) => void;
  onCopy?: (index: number) => void;
  onRemove?: (index: number) => void;
  onMoveDown?: (index: number) => void;
  onMoveUp?: (index: number) => void;
}

const FORMILY_ARRAY_BASE = 'formily-table-base';

type ComposedArrayBase = React.FC<React.PropsWithChildren<IArrayTableBaseProps>> &
  IArrayTableBaseMixins & {
    Item?: React.FC<React.PropsWithChildren<IArrayTableBaseItemProps>>;
    mixin?: <T extends JSXComponent>(target: T) => T & IArrayTableBaseMixins;
  };

const ArrayBaseContext = createContext<IArrayTableBaseContext>(null as any);

const ItemContext = createContext<IArrayTableBaseItemProps>(null as any);

const takeRecord = (val: any) => (_.isFunction(val) ? val() : val);

const useArray = () => {
  return useContext(ArrayBaseContext);
};

const useIndex = (index?: number) => {
  const ctx = useContext(ItemContext);
  return ctx ? ctx.index : index ?? 0;
};

const useRecord = (record?: number) => {
  const ctx = useContext(ItemContext);
  return takeRecord(ctx ? ctx.record : record);
};

const getSchemaDefaultValue = (schema: Schema) => {
  if (schema?.type === 'array') return [];
  if (schema?.type === 'object') return {};
  if (schema?.type === 'void') {
    // eslint-disable-next-line guard-for-in
    for (let key in schema.properties) {
      const value: any = getSchemaDefaultValue(schema.properties[key]);
      if (isValid(value)) return value;
    }
  }
};

const getDefaultValue = (defaultValue: any, schema: Schema) => {
  if (isValid(defaultValue)) return clone(defaultValue);
  if (_.isArray(schema?.items)) return getSchemaDefaultValue(schema.items[0]);
  return getSchemaDefaultValue(schema.items!);
};

export const ArrayTabelBase: ComposedArrayBase = (props) => {
  const field = useField<ArrayField>();
  const schema = useFieldSchema();
  return (
    <RecordsScope getRecords={() => field.value}>
      {/* eslint-disable-next-line react/jsx-no-constructed-context-values */}
      <ArrayBaseContext.Provider value={{ field, schema, props }}>{props.children}</ArrayBaseContext.Provider>
    </RecordsScope>
  );
};

ArrayTabelBase.Item = ({ children, ...props }) => {
  return (
    <ItemContext.Provider value={props}>
      <RecordScope getIndex={() => props.index} getRecord={() => takeRecord(props.record)}>
        {children}
      </RecordScope>
    </ItemContext.Provider>
  );
};

const SortHandle = SortableHandle((props: any) => {
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  return (
    <Icon
      {...props}
      icon={props.icon ?? 'menu'}
      className={cls(`${prefixCls}-sort-handle`, props.className)}
      style={{ ...props.style }}
    />
  );
}) as any;

ArrayTabelBase.SortHandle = (props) => {
  const array = useArray();
  if (!array) return null;
  if (array.field?.pattern !== 'editable') return null;
  return <SortHandle {...props} />;
};

ArrayTabelBase.Index = (props) => {
  const index = useIndex();
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  return (
    <span {...props} className={`${prefixCls}-index`}>
      #{index ?? 0 + 1}.
    </span>
  );
};

ArrayTabelBase.Addition = (props) => {
  const self = useField();
  const array = useArray();
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  if (!array) return null;
  if (array.field?.pattern !== 'editable' && array.field?.pattern !== 'disabled') return null;
  return (
    <div
      className={cls(`${prefixCls}-addition`, props.className)}
      onClick={(e) => {
        if (array.props?.disabled) return;
        const defaultValue = getDefaultValue(props.defaultValue, array.schema);
        if (props.method === 'unshift') {
          array.field?.unshift?.(defaultValue);
          array.props?.onAdd?.(0);
        } else {
          array.field?.push?.(defaultValue);
          array.props?.onAdd?.(array?.field?.value?.length - 1);
        }
        if (props.onClick) {
          props.onClick(e);
        }
      }}
    >
      + {props.title || self.title}
    </div>
  );
};

ArrayTabelBase.Copy = React.forwardRef((props, ref) => {
  const self = useField();
  const array = useArray();
  const index = useIndex(props.index)!;
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  if (!array) return null;
  if (array.field?.pattern !== 'editable') return null;
  return (
    <Icon
      {...props}
      icon={props.icon ?? 'map-3'}
      className={cls(`${prefixCls}-copy`, self?.disabled ? `${prefixCls}-copy-disabled` : '', props.className)}
      ref={ref as any}
      onClick={(e) => {
        if (self?.disabled) return;
        e.stopPropagation();
        if (array.props?.disabled) return;
        const value = clone(array?.field?.value[index]);
        const distIndex = index + 1;
        array.field?.insert?.(distIndex, value);
        array.props?.onCopy?.(distIndex);
        if (props.onClick) {
          props.onClick(e);
        }
      }}
    />
  );
});

ArrayTabelBase.Remove = React.forwardRef((props, ref) => {
  const index = useIndex(props.index)!;
  const self = useField();
  const array = useArray();
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  if (!array) return null;
  if (array.field?.pattern !== 'editable') return null;
  const type = props.type;
  const deleteClick = (e: any) => {
    if (self?.disabled) return;
    e.stopPropagation();
    array.field?.remove?.(index);
    array.props?.onRemove?.(index);
    if (props.onClick) {
      props.onClick(e);
    }
  };
  return type === 'button' ? (
    <Button
      {...(props as ButtonProps)}
      className={cls(
        `${prefixCls}-remove-button`,
        self?.disabled ? `${prefixCls}-remove-disabled` : '',
        props.className,
      )}
      leftIcon="remove"
      type="assist"
      status="danger"
      block
      ref={ref as any}
      onClick={deleteClick}
    >
      {i18n.chain.delete}
    </Button>
  ) : (
    <div>
      <Icon
        {...(props as IconProps)}
        icon={(props as IconProps).icon ?? 'remove'}
        className={cls(`${prefixCls}-remove`, self?.disabled ? `${prefixCls}-remove-disabled` : '', props.className)}
        ref={ref as any}
        onClick={deleteClick}
      />
    </div>
  );
});

ArrayTabelBase.MoveDown = React.forwardRef((props, ref) => {
  const index = useIndex(props.index)!;
  const self = useField();
  const array = useArray();
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  if (!array) return null;
  if (array.field?.pattern !== 'editable') return null;
  return (
    <Icon
      {...props}
      icon={props.icon ?? 'chevron-down-2'}
      className={cls(
        `${prefixCls}-move-down`,
        self?.disabled ? `${prefixCls}-move-down-disabled` : '',
        props.className,
      )}
      ref={ref as any}
      onClick={(e) => {
        if (self?.disabled) return;
        e.stopPropagation();
        array.field?.moveDown?.(index);
        array.props?.onMoveDown?.(index);
        if (props.onClick) {
          props.onClick(e);
        }
      }}
    />
  );
});

ArrayTabelBase.MoveUp = React.forwardRef((props, ref) => {
  const index = useIndex(props.index)!;
  const self = useField();
  const array = useArray();
  const prefixCls = usePrefixCls(FORMILY_ARRAY_BASE);
  if (!array) return null;
  if (array.field?.pattern !== 'editable') return null;
  return (
    <Icon
      {...props}
      icon={props.icon ?? 'chevron-up-2'}
      className={cls(`${prefixCls}-move-up`, self?.disabled ? `${prefixCls}-move-up-disabled` : '', props.className)}
      ref={ref as any}
      onClick={(e) => {
        if (self?.disabled) return;
        e.stopPropagation();
        array?.field?.moveUp(index);
        array?.props?.onMoveUp?.(index);
        if (props.onClick) {
          props.onClick(e);
        }
      }}
    />
  );
});

ArrayTabelBase.useArray = useArray;
ArrayTabelBase.useIndex = useIndex;
ArrayTabelBase.useRecord = useRecord;
ArrayTabelBase.mixin = (target: any) => {
  target.Index = ArrayTabelBase.Index;
  target.SortHandle = ArrayTabelBase.SortHandle;
  target.Addition = ArrayTabelBase.Addition;
  target.Copy = ArrayTabelBase.Copy;
  target.Remove = ArrayTabelBase.Remove;
  target.MoveDown = ArrayTabelBase.MoveDown;
  target.MoveUp = ArrayTabelBase.MoveUp;
  target.useArray = ArrayTabelBase.useArray;
  target.useIndex = ArrayTabelBase.useIndex;
  target.useRecord = ArrayTabelBase.useRecord;
  return target;
};

export default ArrayTabelBase;
