/* stylelint-disable scale-unlimited/declaration-strict-value */
@mdt-prefix: 'mdt';
@array-base-prefix-cls: ~'@{mdt-prefix}-formily-table-base';

@black: #000;
@text-color: fade(@black, 85%);
@disabled-color: fade(@black, 25%);
@hover: #1890ff;

.@{array-base-prefix-cls}-remove,
.@{array-base-prefix-cls}-copy {
  width: 16px;
  margin-top: 2px;
  color: @text-color;
  color: var(--dmc-red-500-color);
  cursor: pointer;
  transition: all 0.25s ease-in-out;

  &-disabled {
    color: @disabled-color;
    cursor: not-allowed !important;
  }
}

.@{array-base-prefix-cls}-remove-button {
  width: 100%;
  margin-bottom: 5px;
}

.@{array-base-prefix-cls}-sort-handle {
  color: #888 !important;
  cursor: move;
}

.@{array-base-prefix-cls}-addition {
  // position: absolute;
  position: absolute;
  top: -6px;
  right: 8px;
  color: @hover;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
}

.@{array-base-prefix-cls}-move-down {
  margin-left: 6px;
  color: @text-color;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.25s ease-in-out;

  &:hover {
    color: @hover;
  }

  &-disabled {
    color: @disabled-color;
    cursor: not-allowed !important;

    &:hover {
      color: @disabled-color;
    }
  }
}

.@{array-base-prefix-cls}-move-up {
  margin-left: 6px;
  color: @text-color;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.25s ease-in-out;

  &:hover {
    color: @hover;
  }

  &-disabled {
    color: @disabled-color;
    cursor: not-allowed !important;

    &:hover {
      color: @disabled-color;
    }
  }
}
