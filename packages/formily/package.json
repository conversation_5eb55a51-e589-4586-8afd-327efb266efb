{"name": "@mdt/formily", "version": "0.9.2", "private": false, "description": "基于 mdt design 的表单渲染器", "keywords": ["mdt", "mdt-design", "formily", "form"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@formily/antd": "2.3.0", "@formily/core": "2.3.0", "@formily/grid": "2.3.0", "@formily/json-schema": "2.3.0", "@formily/react": "2.3.0", "@formily/reactive": "2.3.0", "@formily/shared": "2.3.0", "@i18n-chain/react": "2.0.1", "@monaco-editor/react": "^4.4.5", "antd": "4.20.7", "classnames": "^2.2.6", "dayjs": "1.11.2", "lodash": "^4.17.21", "react-sortable-hoc": "^1.11.0"}}