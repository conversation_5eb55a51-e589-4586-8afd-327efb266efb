/* eslint-disable */

/**
 * 获取url上的参数
 */
export const getParamFromUrl = (name: string, url?: string): string => {
  if (!url) url = window.location.href;
  // name = name.replace(/[[]/, '[').replace(/[\]]/, '\\]');
  const regexS = '[\\?&]' + name + '=([^&#]*)';
  const regex = new RegExp(regexS);
  const results = regex.exec(url);
  return results == null ? '' : results[1];
};

/**
 * 批量获取url参数
 */
export const getParamsFromUrl = (params: string[]): string[] => {
  return params.map((param) => getParamFromUrl(param));
};
