# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [1.17.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.16.3...@mdt/login@1.17.0) (2024-12-23)

### Bug Fixes

- 🐛 修复栅格无法关闭的情况 ([35c4469](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/35c4469d3195f22d2c09be9ed64c4c58cc13065a))

## [1.16.3](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.16.2...@mdt/login@1.16.3) (2024-11-04)

### Features

- ✨ sso 新样式 ([fede183](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fede18371e88326ccb2e602c41261264ec8fefce))

## [1.16.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.16.1...@mdt/login@1.16.2) (2024-10-15)

### Features

- onetable2.0 ([47e9fde](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/47e9fde7078b19b5806cd82b86bd764de6d20514))

## [1.16.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.16.0...@mdt/login@1.16.1) (2024-08-12)

### Features

- ✨ 配置只有一个登录项隐藏 tab, collectorsso 针对配置项的适配工作 ([5fc186c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/5fc186cb5585b3386deeae25813e904fc3292749))

# [1.16.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.15.0...@mdt/login@1.16.0) (2024-08-09)

### Bug Fixes

- 🐛 style lint fix ([72a6ae1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/72a6ae12a7cbe0d7caab832d558bc6d0ecf4b0ca))

# [1.15.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.14.1...@mdt/login@1.15.0) (2024-07-02)

### Features

- ✨ Modify all i18n ([80ff7fa](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80ff7faf39a476757bf7285711f3508c03ecb85b))

## [1.14.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.14.0...@mdt/login@1.14.1) (2024-06-17)

### Features

- ✨ 解决 iOS 验证码输入两次的问题 ([00a0089](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/00a00896878a1e2b56a50c35c38f4924f25c4a77))

# [1.14.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.13.1...@mdt/login@1.14.0) (2024-05-13)

### Features

- ✨ 自定义登录 ([ee27c83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ee27c83a5b349fd8292dfcbe0e910caf8bc570dd))

## [1.13.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.13.0...@mdt/login@1.13.1) (2024-01-15)

### Features

- ✨ 钉钉授权在 mobile 直接跳转 ([fa3e979](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fa3e979edcdf3d29380f158b97bd94c558fbad58))

# [1.13.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.12.0...@mdt/login@1.13.0) (2024-01-02)

### Bug Fixes

- 🐛 滑动认证阻止外部滑动事件 ([2652aee](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/2652aee7736d9d230925bdaff83211b6b235d522))

### Features

- ✨ collector-sso 增加钉钉授权登录 ([ed1b170](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/ed1b170a417647bb0ca0c08121bb5fa7c8193459))

# [1.12.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.11.1...@mdt/login@1.12.0) (2023-11-20)

### Features

- ✨ pwa ([80985fb](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/80985fbd6ca7f56d6b386959368e3ad44f02905b))

## [1.11.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.11.0...@mdt/login@1.11.1) (2023-06-12)

### Features

- ✨ add collector-sso wechat login ([bce6946](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/bce6946ffeb67673a59211197e42e5654e965a14))

# [1.11.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.10.0...@mdt/login@1.11.0) (2023-06-05)

### Bug Fixes

- 🐛 修复移动端弹窗和边缘返回冲突的问题 ([dc981c7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/dc981c750c3c813c43ca7db6b883378058e96d5d))

# [1.10.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.9.2...@mdt/login@1.10.0) (2023-04-17)

### Bug Fixes

- 🐛 适配优化 ([fb2b5ba](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/fb2b5ba02adf4135909d54f5d6f665b7ba4032d1))

### Features

- ✨ login 库适配移动端 ([f472e83](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f472e832286ad1e6c5d0f3929926843e5b5501a7))
- ✨ sso collector-sso 增加自定义配置 ([65c6925](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/65c6925526050e67d0a22e3c34effa26a41374cc))

## [1.9.2](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.9.1...@mdt/login@1.9.2) (2023-03-20)

### Features

- ✨ 私有化补充配置项 ([7ec1737](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7ec1737c04daa7288bace39ca28df0c2330e627f))

## [1.9.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.9.0...@mdt/login@1.9.1) (2023-02-20)

### Bug Fixes

- 🐛 修复 sso 部分问题 ([762733c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/762733c23bd0fbbbcb131ac1c96a678bb7d2d11c))

# [1.9.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.8.0...@mdt/login@1.9.0) (2023-02-13)

### Features

- ✨ [sso, collector-sso]: 增加滑动认证(配置项) ([f4ce9b4](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f4ce9b41d652dec15e3c544f5856404767a2c183))
- ✨ [sso]: 增加选择 app 的页面 ([9aba9e7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/9aba9e72a4a19dd271618415805374dd8f3c5ca4))

# [1.8.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.7.1...@mdt/login@1.8.0) (2023-02-06)

### Features

- ✨ [sso]: 浙政钉接入 ([f858f92](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/f858f92247ecd6b70846db618266b5541f80a1b5))

## [1.7.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.7.0...@mdt/login@1.7.1) (2023-01-06)

### Bug Fixes

- 🐛 [sso]: 修复找回密码验证码点击时输入框有错误提示的问题 ([82aa202](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/82aa202cf6de36f42719e48a451d74f569d6fcd3))

# [1.7.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.6.0...@mdt/login@1.7.0) (2023-01-03)

### Features

- ✨ [Login]: lib 库布局改造 ([3baf0f7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3baf0f7998d207f4d3873f2e571786ef114a6699))

# [1.6.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.5.0...@mdt/login@1.6.0) (2022-12-13)

**Note:** Version bump only for package @mdt/login

# [1.5.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.4.1...@mdt/login@1.5.0) (2022-09-26)

**Note:** Version bump only for package @mdt/login

## [1.4.1](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.4.0...@mdt/login@1.4.1) (2022-09-19)

**Note:** Version bump only for package @mdt/login

# [1.4.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.3.0...@mdt/login@1.4.0) (2022-09-13)

### Features

- ✨ [个人设置]: 手机邮箱和第三方的绑定验证 ([c489c50](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/c489c50978adea72fd6bfa871fcaa5e59d6d1eda))

# [1.3.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.2.0...@mdt/login@1.3.0) (2022-08-29)

### Bug Fixes

- 🐛 修复 sso 的引用问题 ([deeecfc](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/deeecfce5b3d530849ccb4debb2d802c539bbd8c))

### Features

- ✨ sso 改版+忘记密码 ([3ac016f](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/3ac016f0d2ffb614efab0bbbea35c1348271baa9))

# [1.2.0](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/compare/@mdt/login@1.1.0...@mdt/login@1.2.0) (2022-08-12)

### Bug Fixes

- 🐛 fix watermark error ([7a4d33b](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/7a4d33b4f5753197314ec0fbc86ba6a8096d9158))
- 🐛 revert sso loading ([1d03108](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/1d031089328731ffb40ef76076389cf9e5f79511))
- 🐛 sso 项目修改配合和优化交互 ([4dfb9e7](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/4dfb9e70e71c79bfcdcdf35d8be7828f892debaf))
- 🐛 切 app 时闪屏 ([651f557](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/651f557649284d49e632f892d058e1b72a7ab246))
- 🐛 切换 app 时跳 sso 更新 token ([297c72c](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/297c72c70aacd83d327824e6cc4a135dca7054e1))

# 1.1.0 (2022-06-30)

### Features

- ✨ 迁移 sso + 微前端 ([b51cb10](https://gitlab.idatatlas.com:9522/new-datamap/mdt-frontend/commit/b51cb107a0f84fb5729f16cc0118b6cf89d7c91d))
