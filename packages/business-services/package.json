{"name": "@mdt/business-services", "version": "1.35.3", "private": false, "description": "基于restful-api做二次业务封装，统一拦截，统一弹窗", "keywords": ["mdt", "business", "services"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@datlas/dm-rc": "^1.5.0", "@mdt/business-controllers": "^1.17.1", "@mdt/restful-apis": "^1.36.2", "socket.io-client": "^4.5.1"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/socket.io-client": "^3.0.0"}}