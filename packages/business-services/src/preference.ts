import {
  deletePreferences,
  deletePreferencesBatch,
  getPreferences,
  getUserPreferencesBatch,
  postPreferencesSpecs,
  putPreferences,
  putPreferencesBatch,
  putUserPreferencesBatch,
} from '@mdtApis/api/preference';
import {
  IEmptyObj,
  IPreferencesBatchParams,
  IPreferencesBatchQuery,
  IPreferencesBatchRespData,
  IPreferencesData,
  IPreferencesDeleteBatchParams,
  IPreferencesNameQuery,
  IPreferencesPutBatchParams,
  IPreferencesQuery,
  IPreferencesResp,
  IPreferencesSpec,
  IRequestRequestConfig,
  IServerResponse,
} from './interfaces';

// v2
export const getPreferencesAsync = async (query?: IPreferencesQuery, config?: IRequestRequestConfig) => {
  return getPreferences(query, config) as unknown as IServerResponse<IPreferencesResp>;
};

export const putPreferencesAsync = async (
  query: IPreferencesNameQuery,
  data: IPreferencesData,
  config?: IRequestRequestConfig,
) => {
  return putPreferences(query, data, config) as unknown as IServerResponse<IPreferencesResp>;
};

export const putPreferencesBatchAsync = async (
  data: IPreferencesBatchParams,
  query?: IPreferencesQuery,
  config?: IRequestRequestConfig,
) => {
  return putPreferencesBatch(data, query, config) as unknown as IServerResponse<IPreferencesResp>;
};

export const deletePreferencesAsync = async (
  query: IPreferencesNameQuery,
  data: IPreferencesData,
  config?: IRequestRequestConfig,
) => {
  return deletePreferences(query, data, config) as unknown as IServerResponse<IPreferencesResp>;
};

export const postPreferencesSpecsAsync = async (data: IPreferencesSpec, config?: IRequestRequestConfig) => {
  return postPreferencesSpecs(data, config) as unknown as IServerResponse<IPreferencesResp>;
};

export const getUserPreferencesBatchAsync = async (query?: IPreferencesBatchQuery, config?: IRequestRequestConfig) => {
  return getUserPreferencesBatch(query, config) as unknown as IServerResponse<IPreferencesBatchRespData[]>;
};

export const putUserPreferencesBatchAsync = async (
  data: IPreferencesPutBatchParams,
  query?: IPreferencesBatchQuery,
  config?: IRequestRequestConfig,
) => {
  return putUserPreferencesBatch(data, query, config) as unknown as IServerResponse<IEmptyObj>;
};

export const deletePreferencesBatchAsync = async (
  data: IPreferencesDeleteBatchParams,
  query?: IPreferencesNameQuery,
  config?: IRequestRequestConfig,
) => {
  return deletePreferencesBatch(data, query, config) as unknown as IServerResponse<IEmptyObj>;
};
