import { queryResource, queryResourcePermission } from '@mdtApis/api/auth';
import { IRequestRequestConfig, IResourcePermissionResult, IResourceResult, IServerResponse } from '../interfaces';

export const queryResourcePermissionAsync = async (resourceType: string, config?: IRequestRequestConfig) => {
  return queryResourcePermission(resourceType, config) as unknown as IServerResponse<IResourcePermissionResult>;
};

export const queryResourceAsync = async (config?: IRequestRequestConfig) => {
  return queryResource(config) as unknown as IServerResponse<IResourceResult[]>;
};
