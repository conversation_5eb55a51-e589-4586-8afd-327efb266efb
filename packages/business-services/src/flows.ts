import {
  deleteBatchFlow,
  deleteFlow,
  getFlowAllSupportNodes,
  getFlowRun,
  getFlowRunResult,
  getFlowSupportNode,
  patchFlow,
  postFlow,
  putFlow,
  queryFlows,
  queryFlowsRuns,
  runFlowOnce,
  temporaryExecuteFlow,
} from '@mdtApis/api/flows';
import { getTotalArgs } from './_util/totalUtil';
import {
  IFlow,
  IFlowPatch,
  IFlowPost,
  IFlowRevision,
  IFlowRun,
  IFlowRunPost,
  IFlowRunResult,
  IFlowsQuery,
  IFlowsRunsQuery,
  IFlowSupportNode,
  IId,
  IIds,
  IRequestRequestConfig,
  IRequestSettledResult,
  IServerPaginationResponse,
  IServerResponse,
  ITemporaryExecuteFlow,
  ITemporaryExecuteFlowResult,
} from './interfaces';

// 获取全部flow支持的node
export const getFlowAllSupportNodesAsync = async (config?: IRequestRequestConfig) => {
  return getFlowAllSupportNodes(config) as unknown as IServerResponse<IFlowSupportNode[]>;
};

// 获取某一个node
export const getFlowSupportNodeAsync = async (nodeType: string, config?: IRequestRequestConfig) => {
  return getFlowSupportNode(nodeType, config) as unknown as IServerResponse<IFlowSupportNode>;
};

// 查询流程列表
export const queryFlowsAsync = async (config?: IRequestRequestConfig<IFlowsQuery>) => {
  return queryFlows(config) as unknown as IServerResponse<IFlow[]>;
};

// 分页查询流程列表
export const queryFlowsPaginationAsync = async (config?: IRequestRequestConfig<IFlowsQuery>) => {
  const cnf = { ...(config || {}), needMetadata: true };
  return queryFlows(cnf) as unknown as IServerPaginationResponse<IFlow[]>;
};

export const queryFlowsPaginationTotalAsync: typeof queryFlowsPaginationAsync = (...args) => {
  return queryFlowsPaginationAsync(...getTotalArgs(args));
};

// 新建流程
export const postFlowAsync = async (data: IFlowPost, config?: IRequestRequestConfig) => {
  return postFlow(data, config) as unknown as IServerResponse<IFlow>;
};

// 替换一个流程
export const putFlowAsync = async (flowId: string, data: IFlowPost, config: IRequestRequestConfig<IFlowRevision>) => {
  return putFlow(flowId, data, config) as unknown as IServerResponse<IFlow>;
};

// 更新一个流程
export const patchFlowAsync = async (
  flowId: string,
  data: IFlowPatch,
  config: IRequestRequestConfig<IFlowRevision>,
) => {
  return patchFlow(flowId, data, config) as unknown as IServerResponse<IFlow>;
};

// 删除一个流程
export const deleteFlowAsync = async (flowId: string, config: IRequestRequestConfig<IFlowRevision>) => {
  return deleteFlow(flowId, config) as unknown as IServerResponse<IId>;
};

// 删除一个流程
export const deleteBatchFlowAsync = async (ids: string, config: IRequestRequestConfig) => {
  return deleteBatchFlow(ids, config) as unknown as IServerResponse<IIds>;
};

// 手动运行一次流
export const runFlowOnceAsync = async (flowId: string, data: IFlowRunPost, config?: IRequestRequestConfig) => {
  return runFlowOnce(flowId, data, config) as unknown as IServerResponse<IFlowRun>;
};

// 临时执行一次FLOW
export const temporaryExecuteFlowAsync = async (data: ITemporaryExecuteFlow, config?: IRequestRequestConfig) => {
  return temporaryExecuteFlow(data, config) as unknown as IServerResponse<ITemporaryExecuteFlowResult>;
};

// 查询某个运行记录
export const getFlowRunAsync = async (flowRunId: string, config?: IRequestRequestConfig) => {
  return getFlowRun(flowRunId, config) as unknown as IServerResponse<IFlowRun>;
};

// 查询某个运行记录结果
export const getFlowRunResultAsync = async (flowRunId: string, config?: IRequestRequestConfig) => {
  return getFlowRunResult(flowRunId, config) as unknown as IServerResponse<IFlowRunResult>;
};

// 查询全部运行记录
export const queryFlowsRunsAsync = async (config?: IRequestRequestConfig<IFlowsRunsQuery>) => {
  return queryFlowsRuns(config) as unknown as IServerResponse<IFlowRun[]>;
};

// 得到初始Flows的信息
export const getFlowsDataAsync = async (config?: IRequestRequestConfig) => {
  return Promise.allSettled([getFlowAllSupportNodesAsync(config), queryFlowsAsync(config)]) as Promise<
    [IRequestSettledResult<IFlowSupportNode[]>, IRequestSettledResult<IFlow[]>]
  >;
};
