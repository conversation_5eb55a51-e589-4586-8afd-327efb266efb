import {
  deleteFile,
  getFile,
  getFileUrl,
  patchFile,
  postAckFile,
  postFile,
  putFileContent,
  queryFiles,
} from '@mdtApis/api/files';
import {
  IFile,
  IFileDeleteQuery,
  IFileGetQuery,
  IFilePatch,
  IFilePost,
  IFilePostQuery,
  IFilesQuery,
  IFileUrlQuery,
  IId,
  IRequestRequestConfig,
  IServerResponse,
} from './interfaces';

// 查询文件元数据
export const queryFilesAsync = async (config?: IRequestRequestConfig<IFilesQuery>) => {
  return queryFiles(config) as unknown as IServerResponse<IFile[]>;
};

// 新建文件上传
export const postFileAsync = async (data: IFilePost, config?: IRequestRequestConfig<IFilePostQuery>) => {
  return postFile(data, config) as unknown as IServerResponse<IFile>;
};

// 获取文件元数据
export const getFileAsync = async (fileId: string, config?: IRequestRequestConfig<IFileGetQuery>) => {
  return getFile(fileId, config) as unknown as IServerResponse<IFile>;
};

// 修改文件元数据
export const patchFileAsync = async (fileId: string, data: IFilePatch, config?: IRequestRequestConfig) => {
  return patchFile(fileId, data, config) as unknown as IServerResponse<IFile>;
};

// 修改文件元数据
export const deleteFileAsync = async (fileId: string, config?: IRequestRequestConfig<IFileDeleteQuery>) => {
  return deleteFile(fileId, config) as unknown as IServerResponse<IId>;
};

// 确认文件上传完成
export const postAckFileAsync = async (fileId: string, ack_token: string, config?: IRequestRequestConfig) => {
  return postAckFile(fileId, { ack_token }, config) as unknown as IServerResponse<IFile>;
};

// 获取下载数据包
export const getFileUrlAsync = async (fileId: string, config?: IRequestRequestConfig<IFileUrlQuery>) => {
  return getFileUrl(fileId, config) as unknown as IServerResponse<IFile>;
};

// 获取更新文件的url，后续手动上传到该url
export const putFileContentAsync = async (fileId: string, config?: IRequestRequestConfig) => {
  return putFileContent(fileId, config) as unknown as IServerResponse<IFile>;
};
