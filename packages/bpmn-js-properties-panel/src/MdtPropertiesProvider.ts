import { Group, ListGroup } from '@bpmn-io/properties-panel';
import { WorkflowTmplProps } from './properties/WorkflowTmplProps';
import { ExtensionPropertiesProps } from './shared/ExtensionPropertiesProps';
import {
  AfterSubmitProps,
  CallActivityProps,
  CandidateStarterProps,
  ConditionEventProps,
  ConditionProps,
  DataObjectRefProps,
  EventTypeProps,
  FormSpecProps,
  GlobalVariablesProps,
  MultiInstanceProps,
  ParallelGateWayProps,
  ScriptProps,
  ServiceProps,
  StandardLoopProps,
  TaskTypeProps,
  TimerProps,
  UserAssignmentProps,
  UserTaskDetailProps,
} from './properties';

const LOW_PRIORITY = 600;
// 会根据顺序来展示
const MDT_GROUPS = [
  FormSpecGroup,
  ServiceGroup,
  ScriptGroup,
  ConditionGroup,
  ParallelGateWayGroup,
  ConditionEventGroup,
  UserTaskDetailGroup,
  TaskTypeGroup,
  CallActivityGroup,
  UserAssignmentGroup,
  WorkflowTmplGroup,
  AfterSubmitGroup,
  CandidateStarterGroup,
  DataObjectRefPropsGroup,
  GlobalVariablesGroup,
  EventTypeGroup,
  TimerGroup,
  MultiInstanceGroup,
  StandardLoopGroup,
  ExtensionPropertiesGroup,
];

export default class MdtPropertiesProvider {
  public static $inject: string[];
  private _injector: any;

  public constructor(propertiesPanel: any, injector: any) {
    propertiesPanel.registerProvider(LOW_PRIORITY, this);
    this._injector = injector;
  }

  public getGroups(element: any) {
    return (groups: any[]) => {
      // eslint-disable-next-line no-param-reassign
      groups = groups.concat(this._getGroups(element));
      return groups;
    };
  }

  private _getGroups(element: any) {
    const groups = MDT_GROUPS.map((createGroup) => createGroup(element, this._injector));
    return groups.filter((group) => group !== null);
  }
}

MdtPropertiesProvider.$inject = ['propertiesPanel', 'injector'];

function UserAssignmentGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('User assignment'),
    id: 'Mdt__UserAssignment',
    component: Group,
    entries: [...UserAssignmentProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function UserTaskDetailGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Detail page'),
    id: 'Mdt__UserTaskDetail',
    component: Group,
    entries: [...UserTaskDetailProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function CandidateStarterGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('CandidateStarter'),
    id: 'Mdt__CandidateStarter',
    component: Group,
    entries: [...CandidateStarterProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function ParallelGateWayGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Setting'),
    id: 'Mdt__ParallelGateWay',
    component: Group,
    entries: [...ParallelGateWayProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function DataObjectRefPropsGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('DataObjectRef'),
    id: 'Mdt__DataObjectRef',
    component: Group,
    entries: [...DataObjectRefProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function GlobalVariablesGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Global Variables'),
    id: 'Mdt__GlobalVariables',
    component: Group,
    entries: [...GlobalVariablesProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function EventTypeGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Event type'),
    id: 'Mdt__EventType',
    component: Group,
    entries: [...EventTypeProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function TaskTypeGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Task type'),
    id: 'Mdt__TaskType',
    component: Group,
    entries: [...TaskTypeProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function ConditionGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('Condition'),
    id: 'Mdt__Condition',
    component: Group,
    entries: [...ConditionProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function ConditionEventGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('Condition'),
    id: 'Mdt__ConditionEventGroup',
    component: Group,
    entries: [...ConditionEventProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function ScriptGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Script'),
    id: 'Mdt__Script',
    component: Group,
    entries: [...ScriptProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function ServiceGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Service'),
    id: 'Mdt__Service',
    component: Group,
    entries: [...ServiceProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function FormSpecGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('Form Spec'),
    id: 'Mdt__FormSpec',
    component: Group,
    entries: [...FormSpecProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function ExtensionPropertiesGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('Extension properties'),
    id: 'Mdt__ExtensionProperties',
    component: ListGroup,
    ...ExtensionPropertiesProps({ element, injector }),
  };

  if (group.items) {
    return group;
  }

  return null;
}

function WorkflowTmplGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('workflowTmpl'),
    id: 'Mdt__WorkflowTmpl',
    component: Group,
    entries: [...WorkflowTmplProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function TimerGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('Timer'),
    id: 'timer',
    component: Group,
    entries: [...TimerProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }
  return null;
}

function AfterSubmitGroup(element: any, injector: any) {
  const translate = injector.get('translate');

  const group = {
    label: translate('After Submit'),
    id: 'Mdt__AfterSubmit',
    component: Group,
    entries: [...AfterSubmitProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }

  return null;
}

function MultiInstanceGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('multiInstance'),
    id: 'multiInstance',
    component: Group,
    entries: [...MultiInstanceProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }
  return null;
}

function StandardLoopGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('standardLoop'),
    id: 'standardLoop',
    component: Group,
    entries: [...StandardLoopProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }
  return null;
}

function CallActivityGroup(element: any, injector: any) {
  const translate = injector.get('translate');
  const group = {
    label: translate('callActivity'),
    id: 'CallActivity',
    component: Group,
    entries: [...CallActivityProps({ element })],
  };

  if (group.entries.length) {
    return group;
  }
  return null;
}
