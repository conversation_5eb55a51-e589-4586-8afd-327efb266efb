export * from './datapkg';
export * from './album';
export * from './app';
export * from './article';
export * from './dataset';
export * from './entity';
export * from './statistic';
export * from './taggroup';
export * from './ticket';
export * from './job';
export * from './user';
export * from './role';
export * from './resource';
export * from './project';
export * from './page';
export * from './graph';
export * from './map';
export * from './flow';
export * from './workflow-spec';
export * from './workflow';
export * from './events';
export * from './flowork-task';
export * from './folders';
export * from './one-table';

export abstract class BaseBffService {
  protected productName: string;

  public constructor(productName: string) {
    this.productName = productName;
  }
}
