import _ from 'lodash';
import { gql } from '@apollo/client';
import { ApolloService, bffResponseData, bffResponseErrorType } from '../ApolloService';
import { IResponsenData } from '../interfaces';
import {
  ICitizenModel,
  ICustomDownStreamOrgsModel,
  IQueryCitizensOptions,
  IQueryDownstreamOrgsOptions,
} from './interface';

// 查询居民信息
export const getCitizensQuery = (productName: string, idNoAttr: string, respData: string) => `
  ${productName}CitizenInfo(id_no: ${idNoAttr}) {
    ${bffResponseErrorType}
    ... on CitizenResponse {
      ${bffResponseData} {
        ${respData}
      }
    }
  }
`;
export const queryCitizenInfo = (
  productName: string,
  options: IQueryCitizensOptions,
): Promise<IResponsenData<ICitizenModel>> => {
  const { idNo, respData, cancelToken } = options;
  const queryName = `${productName}CitizenInfo`;
  const query = gql(`
    query ${_.snakeCase(queryName)}($idNo: String){
      ${getCitizensQuery(productName, '$idNo', respData)}
    }
  `);
  return ApolloService.query({
    context: { cancelToken },
    query,
    variables: {
      idNo,
    },
  });
};

// 查询下级部门(可以包含子部门)
export const getDownstreamOrgsQuery = (productName: string, dataAttr: string, respData: string) => `
  ${productName}DownstreamOrgs(data: ${dataAttr}) {
    ${bffResponseErrorType}
    ... on CustomDownStreamOrgsResponse {
      ${bffResponseData} {
        ${respData}
      }
    }
  }
`;
export const queryDownstreamOrgs = (
  productName: string,
  options: IQueryDownstreamOrgsOptions,
): Promise<IResponsenData<ICustomDownStreamOrgsModel>> => {
  const { data, respData, cancelToken } = options;
  const queryName = `${productName}DownstreamOrgs`;
  const query = gql(`
    query ${_.snakeCase(queryName)}($data: DownstreamOrgsQuery!){
      ${getDownstreamOrgsQuery(productName, '$data', respData)}
    }
  `);
  return ApolloService.query({
    context: { cancelToken },
    query,
    variables: {
      data,
    },
  });
};
