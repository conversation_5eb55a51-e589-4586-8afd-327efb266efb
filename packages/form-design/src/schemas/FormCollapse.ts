import { ISchema } from '@formily/react';

export const FormCollapse: ISchema & { CollapsePanel?: ISchema } = {
  type: 'object',
  properties: {
    accordion: {
      type: 'boolean',
      'x-decorator': 'FormItem',
      'x-component': 'Switch',
    },
  },
};

FormCollapse.CollapsePanel = {
  type: 'object',
  properties: {
    header: {
      type: 'boolean',
      'x-decorator': 'FormItem',
      'x-component': 'Input',
    },
    extra: {
      type: 'boolean',
      'x-decorator': 'FormItem',
      'x-component': 'Input',
    },
  },
};
