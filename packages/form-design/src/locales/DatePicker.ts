import { createLocales } from '@designable/core';

export const DatePicker = {
  'zh-CN': {
    title: '日期选择',
    settings: {
      'x-component-props': {
        format: '格式',
        picker: {
          title: '选择器类型',
          dataSource: ['日期', '周', '月份', '年', '季度'],
        },
        showNow: '显示此刻',
        showTime: '时间选择',
        showToday: '显示今天',
      },
    },
  },
  'en-US': {
    title: 'DatePicker',
    settings: {
      'x-component-props': {
        format: 'Format',
        picker: {
          title: 'Picker Type',
          dataSource: ['Date', 'Week', 'Month', 'Year', 'Quarter'],
        },
        showNow: 'Show Now',
        showTime: 'Show Time',
        showToday: 'Show Today',
      },
    },
  },
};

export const DateRangePicker = createLocales(DatePicker, {
  'zh-CN': {
    title: '日期范围',
  },
  'en-US': {
    title: 'DateRange',
  },
});
