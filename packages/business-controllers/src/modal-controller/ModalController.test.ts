import _ from 'lodash';
import { ModalController } from './ModalController';

const controllers: ModalController[] = [];
let controller: ModalController;

describe('ModalControllerTest', () => {
  describe('测试默认状态下行为', () => {
    beforeAll(() => {
      controller = new ModalController(true);
      controllers.push(controller);
    });

    test('实例应该被定义', () => {
      expect(controller).toBeDefined();
    });

    test('默认状态', () => {
      expect(controller.getVisibleValue()).toEqual(true);
    });
  });

  describe('测试changeVisible', () => {
    beforeEach(() => {
      controller = new ModalController(true);
      controllers.push(controller);
    });

    test('changeVisible(true)', () => {
      controller.changeVisible(true);
      expect(controller.getVisibleValue()).toEqual(true);
    });

    test('changeVisible(false)', () => {
      controller.changeVisible(false);
      expect(controller.getVisibleValue()).toEqual(false);
    });
  });
});

afterAll(() => {
  _.forEach(controllers, (it) => {
    it.destroy();
  });
  controllers.length = 0;
});
