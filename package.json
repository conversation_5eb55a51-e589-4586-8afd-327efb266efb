{"name": "datlas_bff", "version": "1.30.16", "description": "", "author": "MDT", "private": true, "license": "SIC", "scripts": {"postinstall": "patch-package", "dev": "cross-env SERVICE_GRAPHQL_DEBUG=true nest start --watch", "dev:mock": "cross-env SERVICE_GRAPHQL_DEBUG=true SERVICE_GRAPHQL_MOCKS=true nest start --watch", "doc": "cross-env BUILD_DOC=true nest start", "build": "yarn g && rimraf build && nest build", "build:local": "yarn build", "build:pri": "yarn g && rimraf build && nest build --webpack --webpackPath ./webpack.config.js", "postbuild:pri": "rimraf ./build/tsconfig.tsbuildinfo ./build/main.js.LICENSE.txt", "start": "pm2 start ecosystem.json", "stop": "pm2 stop ecosystem.json", "restart": "pm2 restart ecosystem.json", "prelocal_start": "yarn build", "local_start": "yarn stop && yarn start", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "test": "jest", "posttest": "node ./.scripts/test-tip.js", "test:watch": "jest --watch", "test:cov": "jest --coverage --passWithNoTests", "posttest:cov": "node ./.scripts/test-tip.js", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "rv": "standard-version --release-as minor && yarn rv:push", "rv:p": "standard-version --release-as patch && yarn rv:push", "rv:m": "standard-version --release-as major && yarn rv:push", "rv:push": "git push --follow-tags origin main", "g": "node ./.scripts/generate-product-config.js", "gm": "node ./.scripts/generate-module.js"}, "dependencies": {"@nestjs/axios": "^0.0.3", "@nestjs/common": "^8.2.3", "@nestjs/config": "^1.1.5", "@nestjs/core": "^8.2.3", "@nestjs/graphql": "^9.1.2", "@nestjs/platform-express": "^8.2.3", "@nestjs/swagger": "^5.2.1", "@nestjs/terminus": "^8.0.1", "@opentelemetry/api": "^1.7.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.46.0", "@opentelemetry/exporter-trace-otlp-http": "^0.46.0", "@opentelemetry/instrumentation-dataloader": "^0.5.3", "@opentelemetry/instrumentation-express": "^0.34.0", "@opentelemetry/instrumentation-graphql": "^0.36.0", "@opentelemetry/instrumentation-http": "^0.46.0", "@opentelemetry/instrumentation-nestjs-core": "^0.33.3", "@opentelemetry/instrumentation-router": "^0.33.3", "@opentelemetry/instrumentation-winston": "^0.33.0", "@opentelemetry/resources": "^1.12.0", "@opentelemetry/sdk-node": "^0.46.0", "@opentelemetry/sdk-trace-base": "^1.19.0", "@opentelemetry/semantic-conventions": "^1.19.0", "@sentry/node": "^6.16.0", "@types/uuid": "^8.3.3", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "ajv-keywords": "^5.1.0", "apollo-server-express": "^3.5.0", "class-transformer": "0.4.0", "class-validator": "^0.13.2", "consul": "^0.40.0", "crypto-js": "^4.2.0", "dataloader": "^2.0.0", "dayjs": "^1.10.7", "eslint-plugin-unused-imports": "^2.0.0", "express-prom-bundle": "^6.5.0", "graphql": "^15.8.0", "graphql-tools": "^8.2.0", "graphql-type-json": "^0.3.2", "helmet": "^4.6.0", "isolated-vm": "^4.7.2", "lodash": "^4.17.21", "openai": "^4.6.0", "pm2": "^5.1.2", "prom-client": "^14.0.1", "r2curl": "^0.2.4", "reflect-metadata": "^0.1.13", "response-time": "^2.3.2", "rimraf": "^3.0.2", "rxjs": "^7.5.7", "sm-crypto": "^0.3.13", "uuid": "^8.3.2", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.5.5", "winston-transport": "^4.4.0"}, "devDependencies": {"@commitlint/cli": "^15.0.0", "@commitlint/config-conventional": "^15.0.0", "@nestjs/cli": "^8.1.5", "@nestjs/schematics": "^8.0.5", "@nestjs/testing": "^8.2.3", "@types/consul": "^0.40.0", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.13", "@types/jest": "^27.0.3", "@types/lodash": "^4.14.177", "@types/node": "^16.0.0", "@types/sm-crypto": "^0.3.4", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.6.0", "@typescript-eslint/parser": "^5.6.0", "cross-env": "^7.0.3", "eslint": "^8.4.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-simple-import-sort": "^7.0.0", "fork-ts-checker-webpack-plugin": "^6.5.0", "husky": "4.3.8", "jest": "^27.4.3", "lint-staged": "^12.1.2", "node-loader": "^2.0.0", "patch-package": "^6.5.1", "prettier": "^2.5.1", "standard-version": "^9.5.0", "supertest": "^6.1.6", "swagger-ui-express": "^5.0.1", "terser-webpack-plugin": "^5.2.5", "ts-jest": "^27.1.1", "ts-loader": "^9.2.6", "ts-node": "^10.4.0", "tsconfig-paths": "^3.12.0", "tsconfig-paths-webpack-plugin": "^3.5.2", "typescript": "^4.5.2", "webpack": "^5.65.0"}}