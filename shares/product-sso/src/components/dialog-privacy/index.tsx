import { Component } from 'react';
import { Button } from '@mdtLogin/components/button';
import './index.less';

interface IProps {
  onClick: () => void;
  onCancel: () => void;
  title: string;
  desc: string;
  buttonOkLabel: string;
  buttonCancelLabel: string;
}

const IconTitle = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M18.4268 3.00575L28.9937 13.5738C30.3357 14.9168 30.3337 17.0868 28.9937 18.4268L18.4268 28.9937C17.0837 30.3357 14.9138 30.3337 13.5738 28.9937L3.00575 18.4268C1.66375 17.0848 1.66575 14.9148 3.00575 13.5738L13.5738 3.00575C14.9158 1.66375 17.0858 1.66575 18.4268 3.00575ZM14.4444 13.203V23.4636C14.4444 23.6401 14.5831 23.7778 14.7541 23.7778H17.2459C17.4219 23.7778 17.5556 23.6371 17.5556 23.4636V13.203C17.5556 13.0266 17.4169 12.8889 17.2459 12.8889H14.7541C14.5781 12.8889 14.4444 13.0295 14.4444 13.203ZM16 11.3333C16.8591 11.3333 17.5556 10.6369 17.5556 9.77778C17.5556 8.91867 16.8591 8.22222 16 8.22222C15.1409 8.22222 14.4444 8.91867 14.4444 9.77778C14.4444 10.6369 15.1409 11.3333 16 11.3333Z"
      fill="#4285F4"
    />
  </svg>
);

class DialogPrivacy extends Component<IProps> {
  public _onClick = () => {
    this.props.onClick();
  };
  public _onCancel = () => {
    this.props.onCancel();
  };

  public render() {
    return (
      <div>
        <div className="mdt-sso-title">
          <IconTitle />
          <div>
            {this.props.title}
            <div className="mdt-sso-desc">{this.props.desc}</div>
          </div>
        </div>

        <Button className="mdt-sso-footer-ok-btn" onClick={this._onClick}>
          {this.props.buttonOkLabel}
        </Button>
        <Button className="mdt-sso-footer-cancel-btn" onClick={this._onCancel}>
          {this.props.buttonCancelLabel}
        </Button>
      </div>
    );
  }
}

export default DialogPrivacy;
