import { Component } from 'react';
import { Button } from '@mdtLogin/components/button';
import './index.less';

interface IProps {
  onClick: () => void;
  title: string;
  desc: string;
  buttonOkLabel: string;
}

const IconTitle = () => (
  <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.6637 1.64828L29.2964 25.8189C30.216 27.5785 29.3752 29.0001 27.4215 29.0001H2.58071C0.625927 29.0001 -0.212403 27.5758 0.705832 25.8189L13.3385 1.64828C14.2581 -0.111319 15.7455 -0.108625 16.6637 1.64828ZM15.0011 19.6676C15.9798 19.6676 16.7742 18.8629 16.7742 17.8715V8.89125C16.7742 7.89983 15.9798 7.09519 15.0011 7.09519C14.0223 7.09519 13.228 7.89983 13.228 8.89125V17.8715C13.228 18.8629 14.0223 19.6676 15.0011 19.6676ZM15.0011 25.0557C15.9798 25.0557 16.7742 24.2511 16.7742 23.2597C16.7742 22.2683 15.9798 21.4636 15.0011 21.4636C14.0223 21.4636 13.228 22.2683 13.228 23.2597C13.228 24.2511 14.0223 25.0557 15.0011 25.0557Z"
      fill="#f85960"
    />
  </svg>
);

class DialogPasswordError extends Component<IProps> {
  public _onClick = () => {
    this.props.onClick();
  };

  public render() {
    return (
      <div>
        <div className="mdt-password-error">
          <IconTitle />
          <div>
            {this.props.title}
            <div className="mdt-password-error-desc">{this.props.desc}</div>
          </div>
        </div>

        <Button className="mdt-password-error-btn" onClick={this._onClick}>
          {this.props.buttonOkLabel}
        </Button>
      </div>
    );
  }
}

export default DialogPasswordError;
