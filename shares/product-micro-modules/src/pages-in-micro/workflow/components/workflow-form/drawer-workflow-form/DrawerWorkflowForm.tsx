import { FC } from 'react';
import { ModalWithBtnsCompDrawer } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { DrawerWorkflowFormController } from './DrawerWorkflowFormController';
import './index.less';

interface IProps {
  controller: DrawerWorkflowFormController;
}
export const DrawerWorkflowForm: FC<IProps> = ({ controller }) => {
  return <ModalWithBtnsCompDrawer controller={controller} />;
};
