import { createGraphConfig } from '@antv/xflow';

export const graphConfig = createGraphConfig((config) => {
  /** 设置画布配置项，会覆盖XFlow默认画布配置项 */
  config.setX6Config({
    grid: {
      size: 20,
      visible: true,
    },
    background: { color: '#f6f7f9' },
    scaling: { min: 0.2, max: 3 },
    /** 画布滚轮缩放 */
    mousewheel: {
      enabled: true,
      /** 将鼠标位置作为中心缩放 */
      zoomAtMousePosition: true,
    },
  });
});
