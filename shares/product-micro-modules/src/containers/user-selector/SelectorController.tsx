import _ from 'lodash';
import React from 'react';
import { BehaviorSubject, Observable } from 'rxjs';
import { IPaginationQuery } from '@mdtApis/interfaces';
import { ILabelValue } from '@mdtBsComm/interfaces';
import i18n from '../../languages';
import { CardCurdWithSimpleSearchController } from '../card-curd-with-simple-search';
import { BreadMenu, OptionCard } from './Selector';
import { IUserSelectorControllerOptions } from './UserSelectorController';
import { IUserSelectorModel } from './UserSelectorModel';

export enum SelectorTypeEnum {
  USER = 'user',
  ROLE = 'role',
  ORGANIZATION = 'organization',
  GROUP = 'group',
}

export const selectorTypeMap = () => ({
  [SelectorTypeEnum.USER]: i18n.chain.proMicroModules.chooseUser.user,
  [SelectorTypeEnum.ROLE]: i18n.chain.proMicroModules.chooseUser.role,
  [SelectorTypeEnum.ORGANIZATION]: i18n.chain.proMicroModules.chooseUser.org,
  [SelectorTypeEnum.GROUP]: i18n.chain.proMicroModules.chooseUser.group,
});

export const SelectorTypeIconMap: Record<string, string> = {
  [SelectorTypeEnum.USER]: 'user',
  [SelectorTypeEnum.ROLE]: 'collabrate',
  [SelectorTypeEnum.ORGANIZATION]: 'team',
  [SelectorTypeEnum.GROUP]: 'un-group',
};

const BREAD_MENU_FIRST_ITEM_KEY = 'bread_menu_first_item_key'; // 用户类型
const BREAD_MENU_SECOND_ITEM_KEY = 'bread_menu_second_item_key'; // 选择类型

export interface ISelectorItem {
  id: string;
  name: string;
  type: SelectorTypeEnum;
}

export interface IUserOrgsItem {
  userId: string;
  orgId?: string;
}

export type IFilterParams = IPaginationQuery & {
  name?: string;
  parent?: ISelectorItem;
  disabledUserIds?: string[];
  disabledRoleIds?: string[];
  disableRole: boolean;
  disableOrg: boolean;
  disableGroup: boolean;
};

type IQueryOptionsMap = Partial<
  Record<SelectorTypeEnum, (params: IFilterParams, hideUser?: boolean) => Observable<[number, ISelectorItem[]]>>
>;

interface ISelectorControllerOptions extends IUserSelectorControllerOptions {
  // 当前选中的值
  value?: ISelectorItem[];
  // 禁止选择的用户id
  disabledUserIds?: string[];
  // 禁止选择的角色id(角色、机构、群组的id都放在这里)
  disabledRoleIds?: string[];
  // 指定只展示一种类型
  uniqueType?: SelectorTypeEnum;
}

class SelectorController {
  private Model: IUserSelectorModel;
  private optionsController: CardCurdWithSimpleSearchController<ISelectorItem, SelectorController>;
  private selectedType$ = new BehaviorSubject<SelectorTypeEnum | undefined>(undefined);
  private selectMe$ = new BehaviorSubject<boolean>(false);
  private breadItems$ = new BehaviorSubject<ILabelValue[]>([
    { label: i18n.chain.proMicroModules.chooseUser.userResource, value: BREAD_MENU_FIRST_ITEM_KEY },
  ]);
  private showMe$ = new BehaviorSubject<boolean>(true);
  private queryFirstPageOptionsMap: IQueryOptionsMap = {
    [SelectorTypeEnum.USER]: (params?: IFilterParams) =>
      this.Model.queryUserList({
        disableRole: false,
        disableOrg: false,
        disableGroup: false,
        ...params,
      }),
    [SelectorTypeEnum.ROLE]: (params?: IFilterParams) => this.Model.queryRoleList(params),
    [SelectorTypeEnum.ORGANIZATION]: (params?: IFilterParams, hideUser?: boolean) =>
      this.Model.queryOrganizationList(params, hideUser),
    [SelectorTypeEnum.GROUP]: (params?: IFilterParams) => this.Model.queryGroupList(params),
  };
  private meOption: ISelectorItem;
  // 不展示用户的情况下：群组、角色没有下一级按钮，机构列表不能出现用户（在接口级别过滤用户）
  private hideUser = false;
  private disableTopMenu = false;
  private renderFooterExtra: (() => React.ReactNode) | undefined;

  public constructor({
    app,
    Model,
    renderFooterExtra,
    showMe = true,
    disableOrg = false,
    disableRole = false,
    disableGroup = false,
    disabledRoleIds = [],
    disabledUserIds = [],
    value,
    uniqueType,
  }: ISelectorControllerOptions) {
    this.Model = Model;
    this.optionsController = new CardCurdWithSimpleSearchController<ISelectorItem>({
      dataListCompCardCurdControllerOptions: {
        dataListCompControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: (clickedItem?: ISelectorItem) => {
              this.optionsController.getSingleFilterValue();
              const type = this.selectedType$.getValue()!;
              const params: IFilterParams = {
                name: this.optionsController.getSingleFilterValue() || undefined,
                parent: clickedItem,
                disableOrg,
                disableRole,
                disableGroup,
                disabledRoleIds,
                disabledUserIds,
              };
              const request = this.queryFirstPageOptionsMap[type]!;
              return request(params, this.hideUser);
            },
          },
          compOptions: () => this.initListOptions(),
        },
        cardItemViewController: () => this,
        curdOptions: { enableAllSelect: true },
      },
      headerOptions: () => {
        return {
          inputPlaceholder: i18n.chain.comPlaceholder.input,
          renderExtendOthers: () => <BreadMenu controller={this} />,
        };
      },
    });

    this.showMe$.next(showMe);
    this.renderFooterExtra = renderFooterExtra;

    const userKey = this.Model.getUserKey();
    const id = userKey === 'uuid' ? app.getUserUuid() : app.getUserId();
    this.meOption = {
      id: `${id}`,
      name: app.getUserName(),
      type: SelectorTypeEnum.USER,
    };

    if (uniqueType) {
      this.hideUser = uniqueType !== SelectorTypeEnum.USER;
      this.handleClickType(uniqueType);
      this.disableTopMenu = true;
    }
    value && this.optionsController.addToSelectedDataList(value);
    this.optionsController.listenFrontFilter();
    this.listenOptionsChange();
  }

  public destroy() {
    this.optionsController.destroy();
    this.selectedType$.complete();
    this.selectMe$.complete();
    this.breadItems$.complete();
    this.queryFirstPageOptionsMap = {};
    this.meOption = null!;
    this.Model = null!;
  }

  public getSelectMe$() {
    return this.selectMe$;
  }

  public getRenderFooterExtra() {
    return this.renderFooterExtra;
  }

  public getShowMe$() {
    return this.showMe$;
  }

  public showUser() {
    return !this.hideUser;
  }

  public changeSelectMe = (val: boolean) => {
    this.selectMe$.next(val);
    if (val) {
      this.optionsController.addToSelectedDataList(this.meOption);
    } else {
      this.optionsController.removeFromSelectedDataList(this.meOption);
    }
  };

  public getBreadItems$() {
    return this.breadItems$;
  }

  public getOptionsController() {
    return this.optionsController;
  }

  public getTypeList() {
    const types = _.values(SelectorTypeEnum);
    return _.map(types, (type) => ({
      label: selectorTypeMap()[type],
      value: type,
    }));
  }

  public getSelectedType$() {
    return this.selectedType$;
  }

  public handleClickType = (type: SelectorTypeEnum) => {
    this.selectedType$.next(type);
    this.breadItems$.next(
      _.concat(this.breadItems$.getValue(), { value: BREAD_MENU_SECOND_ITEM_KEY, label: selectorTypeMap()[type] }),
    );
    this.optionsController.loadDataList();
  };

  public handleClickBreadMenu = (menu: ILabelValue, index: number) => {
    this.optionsController.changeSingleFilter('');
    if (menu.value === BREAD_MENU_FIRST_ITEM_KEY) {
      if (this.disableTopMenu) return;
      this.selectedType$.next(undefined);
    } else if (menu.value === BREAD_MENU_SECOND_ITEM_KEY) {
      this.optionsController.loadDataList();
    } else {
      this.optionsController.loadDataList({
        id: _.parseInt(menu.value),
        name: menu.label,
        type: SelectorTypeEnum.ORGANIZATION,
      });
    }
    this.breadItems$.next(_.slice(this.breadItems$.getValue(), 0, index + 1));
  };

  public getValues$() {
    return this.optionsController.getSelectedDataList$();
  }

  public removeSelectedItem = (val: ISelectorItem) => {
    this.optionsController.removeFromSelectedDataList(val);
  };

  public handleClickOption(item: ISelectorItem, selected: boolean) {
    selected
      ? this.optionsController.removeFromSelectedDataList(item)
      : this.optionsController.addToSelectedDataList(item);
  }

  public handleClear = () => {
    this.optionsController.changeSelectedDataList([]);
  };

  public handleClickNextLevel(item: ISelectorItem) {
    this.optionsController.changeSingleFilter('');
    this.optionsController.loadDataList(item);
    this.breadItems$.next([
      ...this.breadItems$.getValue(),
      {
        label: item.name,
        value: `${item.id}`,
      },
    ]);
  }

  private initListOptions() {
    return {
      itemGap: 0,
      itemHeight: 30,
      itemWidth: '100%',
      useVirtual: true,
      itemKey: 'value',
      CardItemView: OptionCard,
    };
  }

  private listenOptionsChange() {
    this.optionsController.getSelectedDataList$().subscribe((list) => {
      const me = _.find(list, ['id', this.meOption.id]);
      if (me) {
        !this.selectMe$.getValue() && this.selectMe$.next(true);
      } else {
        this.selectMe$.getValue() && this.selectMe$.next(false);
      }
    });
  }
}

export { SelectorController };
