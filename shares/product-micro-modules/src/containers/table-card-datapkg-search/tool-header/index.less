.module_table-card-datapkg-search {
  .pkg-search_tool-header {
    position: relative;
    flex-shrink: 0;
    margin-bottom: 16px;
    padding: 0 20px;
  }

  .pkg-search_open-btn,
  .pkg-search_close-btn {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 30px;
    border: 1px solid var(--dmc-split-btn-color);
  }

  .pkg-search_open-btn {
    right: 0;
    border-right: none;
    border-radius: 4px 0 0 4px;
  }

  .pkg-search_close-btn {
    right: -16px;
    border-left: none;
    border-radius: 0 4px 4px 0;
  }

  .pkg-search_tool-right {
    display: flex;
    flex: 1;
    justify-content: flex-end;
  }

  .pkg-search_tool-title-wrap {
    display: flex;
    align-items: center;
    color: var(--dmc-text-4);
    font-weight: normal;
    font-size: 14px;
    line-height: 20px;
  }

  .pkg-search_tool-title {
    color: var(--dmc-text-8);
    font-weight: 500;
  }

  .pkg-search_tool-title-dot {
    width: 2px;
    height: 2px;
    margin: 0 8px;
    background: var(--dmc-text-4);
    border-radius: 50%;
  }

  .pkg-search_tool-input {
    width: 320px;
    margin-right: 12px;

    .dmc-icon {
      color: var(--dmc-text-4);
    }
  }

  .pkg-search_tool-date-range {
    margin-right: 6px;
  }

  .pkg-search_tool-filter {
    min-width: 32px;
    margin-right: 2px;
  }

  .pkg-search_tool-shape {
    min-width: 32px;
    margin-right: 2px;
  }

  .search-type {
    width: 100px;
    margin-right: 5px;
  }
}
