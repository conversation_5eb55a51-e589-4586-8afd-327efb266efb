.module_menu-pkg {
  flex: 0 0 250px;
  width: 250px;
  // min-height: calc(100vh - 55px);
  border-right: 1px solid var(--dmc-split-page-color);

  .menu-datapkg_list {
    // height: calc(100vh - 120px);
    padding: 0 10px 0 15px;
  }

  .menu-datapkg_selected {
    background-color: var(--dmc-tree-bg-hover-color);
    border-radius: 4px;
  }

  .menu-datapkg_pkg-row {
    display: flex;
    align-items: center;
    padding: 0 5px;

    &:hover {
      .menu-datapkg_selected;
    }
  }

  .menu-datapkg_name {
    max-width: 140px;
    margin: 0 5px;
  }

  .menu-datapkg_tool-bar {
    display: flex;
    padding: 14px;
  }

  .menu-datapkg_search-input {
    width: 190px;
    margin-right: 6px;
  }

  .menu-datapkg_filter-btn {
    background-color: var(--dmc-icon-bg-color);
  }

  .menu-datapkg_condition-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 0 20px 4px;

    .dmc-tag {
      margin: 2px;
    }
  }

  .menu-datapkg_condition-list-prefix {
    color: var(--dmc-text-4);
  }

  .menu-datapkg_condition-list-clear-btn {
    margin-left: 10px;
  }
}
