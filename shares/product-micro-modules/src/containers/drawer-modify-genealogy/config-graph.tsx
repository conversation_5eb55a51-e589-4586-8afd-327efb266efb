import _ from 'lodash';
import { createHookConfig, DisposableCollection, NsGraph } from '@antv/xflow';
import { splitKey } from '@mdtBsComm/utils/stringUtil';
import { PkgCenterNode } from './react-node/pkg-center-node';
import { PkgNode } from './react-node/pkg-node';
import { PkgPanelNode } from './react-node/pkg-panel-node';
import { DrawerModifyGenealogyController } from './DrawerModifyGenealogyController';
import { PKG_CENTER_RENDER_ID, PKG_PANEL_RENDER_ID, PKG_RENDER_ID, ROOT_PORT } from './util';

const PORT_ATTR = 'port-group';

export const useGraphHookConfig = createHookConfig((config, proxy) => {
  const controller: DrawerModifyGenealogyController<any> = proxy.getValue();
  // eslint-disable-next-line sonarjs/cognitive-complexity
  config.setRegisterHook((hooks) => {
    const disposableList = [
      // 注册增加 react Node Render
      hooks.reactNodeRender.registerHook({
        name: 'add react node',
        handler: async (renderMap) => {
          renderMap.set(PKG_PANEL_RENDER_ID, PkgPanelNode);
          renderMap.set(PKG_CENTER_RENDER_ID, PkgCenterNode);
          renderMap.set(PKG_RENDER_ID, PkgNode);
        },
      }),

      // 注册修改graphOptions配置的钩子
      hooks.graphOptions.registerHook({
        name: 'custom-x6-options',
        after: 'dag-extension-x6-options',
        handler: async (options) => {
          options.grid = true;
          options.keyboard = {
            enabled: true,
          };
          options.scaling!.min = 0.5;
          options.scaling!.max = 1.2;
          const graphOptions = {
            connecting: {
              snap: { radius: 50 },
              // 是否触发交互事件
              validateMagnet({ magnet }: any) {
                return magnet.getAttribute(PORT_ATTR) !== NsGraph.AnchorGroup.TOP;
              },
              // 显示可用的链接桩
              validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }: any) {
                // 没有起点的返回false
                if (!sourceMagnet) {
                  return false;
                }
                // 不允许连接到自己
                if (sourceView === targetView) {
                  return false;
                }
                // 只能从上游节点的输出链接桩创建连接
                if (sourceMagnet.getAttribute(PORT_ATTR) === NsGraph.AnchorGroup.TOP) {
                  return false;
                }
                if (!targetMagnet) {
                  return false;
                }
                // 只能连接到下游节点的输入桩
                if (targetMagnet.getAttribute(PORT_ATTR) !== NsGraph.AnchorGroup.TOP) {
                  return false;
                }
                // 判断目标链接桩是否可连接
                const targetPortId = targetMagnet.getAttribute('port');
                const sourcePortId = sourceMagnet.getAttribute('port');
                // 输入桩、输出桩必须有一个是根节点
                const isTargetRoot = _.includes(targetPortId, ROOT_PORT);
                if (!isTargetRoot && !_.includes(sourcePortId, ROOT_PORT)) {
                  return false;
                }

                // 确保输入、输出节点最多连一根线
                const nodeId = isTargetRoot ? splitKey(sourcePortId)[0] : splitKey(targetPortId)[0];
                if (controller.checkNodeConnected(nodeId)) {
                  return false;
                }

                const targetNode = targetView.cell;
                const port = targetNode.getPort(targetPortId);

                return !!port;
              },
            },
          };
          options.connecting = { ...options.connecting, ...graphOptions.connecting };
        },
      }),
    ];
    const toDispose = new DisposableCollection();
    toDispose.pushAll(disposableList);
    return toDispose;
  });
});
