.xflow-pkg-panel-node {
  z-index: 10;
  display: flex;
  align-items: center;
  width: 100%;
  height: 32px;
  padding: 4px 4px 4px 16px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 6px;
  transition: all ease-in-out 0.15s;

  &:hover {
    border-color: var(--dmc-blue-500-color);
    cursor: move;
  }

  .label {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    text-overflow: ellipsis;
  }
}
