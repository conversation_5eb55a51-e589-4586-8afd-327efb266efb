import _ from 'lodash';
import { TooltipText } from '@metroDesign/tooltip';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { ModalWithBtnsCompEmotionController } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { RequestController } from '@mdtBsControllers/request-controller';
import { Dropmenu } from '@mdtDesign/dropdown';
import { MenuInfo } from '@mdtDesign/menu';
import toastApi from '@mdtDesign/toast';
import { DataNode } from '@mdtDesign/tree';
import { IOrgs, IOrgTrees } from '@mdtProComm/interfaces';
import {
  addToTreeWithKey,
  getChildOrgsWithOrg,
  getCurrentOrgs,
  getOrgKeyToChildMap,
  orgsToOrgTrees,
  removeToTreeWithKey,
  setChildToOrg,
  updateTreeWithKey,
} from '@mdtProComm/utils/orgUtil';
import i18n from '../../languages';
import {
  DialogModifyFromOrganizationController,
  IFormData as IOrgFormData,
  IModalData as IOrgModalData,
  IPermissionModalData,
} from '../dialog-modify-from-organization';
import { DrawerModifyFormPermissionOnlyController } from '../drawer-modify-form-permission-only';
import { OrgRoot } from './components/OrgRoot';
import { IOrgManageTreeListModel, IOrgOperateEnum } from './OrgManageTreeListModel';

// 开启虚拟滚动的数量
const START_VIRTUAL_COUNT = 1000;

type IActionType = 'create' | 'update' | 'delete';
export interface IResult {
  data: IOrgs;
  flatData: IOrgs[];
  treeData: IOrgTrees[];
}

export type IHandleOrgs = (data: IOrgs, treeData: IOrgTrees[], flatData: IOrgs[]) => void;
export type ISelect = (id: string, isRoot: boolean, flatData: IOrgs[]) => void;
export type IPermissionUiFUnc = () => Observable<IPermissionModalData>;
export interface IControllerOptions {
  // 当前appId
  appId: number;
  // 当前appName
  appName: string;
  // 只做展示(此选项勾选后，机构不再有操作功能，只有列表展示)
  onlyRead?: boolean;
  // 权限ui
  queryPermissionUiDataFunc?: IPermissionUiFUnc;
  // 选择回调
  onOrgSelectFunc?: ISelect;
  // 创建回调
  createOrgFunc?: IHandleOrgs;
  // 更新回调
  updateOrgFunc?: IHandleOrgs;
  // 删除回调
  deleteOrgFunc?: IHandleOrgs;
  // 打开资源弹窗
  openResourceFunc?: (items: IOrgs) => void;
  // 是否允许操作
  enablePermission?: boolean;
  // 是否显示footer，默认为true
  showFooter?: boolean;
  // 显示操作栏，默认为true
  showOpactor?: boolean;
}

// 防止app的id和部门的id重合，为appId加上前缀
export const APP_KEY_SUFFIX = 'app_';

// onlyRead  DefaultValue
export const READONLY_ONLY_VALUE = {
  showFooter: false,
  showOpactor: false,
  enablePermission: false,
  queryPermissionUiDataFunc: undefined,
};

class OrgManageTreeListController extends RequestController {
  private Model: IOrgManageTreeListModel;
  private readonly appId: number;
  private readonly appName: string;
  private readonly enablePermission?: boolean;
  private readonly showFooter?: boolean;
  private readonly showOpactor?: boolean;
  private createOrgFunc?: IHandleOrgs;
  private updateOrgFunc?: IHandleOrgs;
  private deleteOrgFunc?: IHandleOrgs;
  private onOrgSelectFunc?: ISelect;
  private queryPermissionUiDataFunc?: IPermissionUiFUnc;
  private openResourceFunc?: (items: IOrgs) => void;

  // 删除部门
  private deleteOrgController: ModalWithBtnsCompEmotionController<IOrgs>;
  // 新建/管理子部门
  private modifyOrgController?: DialogModifyFromOrganizationController<IOrgs, IOrgModalData>;
  // 权限
  private permissionOnlyController?: DrawerModifyFormPermissionOnlyController<IOrgs['permission'], IOrgs>;

  // tree的扁平化结构
  private flatTreeData$ = new BehaviorSubject<IOrgs[]>([]);
  // tree结构
  private treeData$ = new BehaviorSubject<IOrgTrees[]>([]);
  // 是否为根节点
  private isRoot$ = new BehaviorSubject<boolean>(true);
  // 当前选中的节点
  private currentSelectId$ = new BehaviorSubject<string>('');
  // 开启虚拟滚动
  private isVirtual$ = new BehaviorSubject<boolean>(true);
  // 搜索
  private searchValue$ = new BehaviorSubject<string>('');
  private orgActionTrigger$ = new BehaviorSubject<
    | {
        actionType: IActionType;
        value: {
          current: IOrgs;
          treeData: IOrgTrees[];
          flatData: IOrgs[];
        };
      }
    | undefined
  >(undefined);

  private currentDataCache: { type: IActionType; data: IOrgs } | undefined = undefined;
  private treeDataCache: IOrgTrees[] = [];
  private mapCache: Record<string, IOrgs[]> = {};

  public constructor(options: IControllerOptions, Model: IOrgManageTreeListModel) {
    super();
    this.Model = Model;

    // 如果只读属性触发，则覆盖options的值
    const optionsOverride = options.onlyRead ? { ...options, ...READONLY_ONLY_VALUE } : options;
    this.appId = optionsOverride.appId;
    this.appName = optionsOverride.appName;
    this.enablePermission = optionsOverride.enablePermission;
    this.showFooter = optionsOverride.showFooter;
    this.showOpactor = optionsOverride.showOpactor;
    this.createOrgFunc = optionsOverride.createOrgFunc;
    this.updateOrgFunc = optionsOverride.updateOrgFunc;
    this.deleteOrgFunc = optionsOverride.deleteOrgFunc;
    this.onOrgSelectFunc = optionsOverride.onOrgSelectFunc;
    this.queryPermissionUiDataFunc = optionsOverride.queryPermissionUiDataFunc;
    this.openResourceFunc = optionsOverride.openResourceFunc;

    this.deleteOrgController = new ModalWithBtnsCompEmotionController<IOrgs>({
      clickOkBtnFunc: this.deleteOrgToService,
      modalCompOptions: { modalOptions: this.initDeleteOrgModalOptions },
      uiOptions: { okBtnProps: { type: 'primary' } },
    });
    this.listenFlatData();
    this.listenActionTrigger();
  }

  public init(orgList: IOrgs[], initSelectId?: string) {
    const rootId = this.getRootInfo().key;
    this.flatTreeData$.next(orgList);
    this.treeData$.next(this.getRootTreeData());
    this.currentSelectId$.next(initSelectId || rootId);
  }

  public loadMore = async ({ key }: any) => {
    const treeData = this.treeData$.getValue();
    const result = await setChildToOrg(treeData, key, this.getLeafTreeData(this.mapCache[key]));
    this.treeData$.next(result);
  };

  public onChange = (val: string) => {
    this.searchValue$.next(val);
    if (!val) {
      this.treeData$.next(this.getRootTreeData());
    } else {
      const flatTreeData = this.flatTreeData$.getValue();
      const searchedData = _.filter(flatTreeData, ({ title }) => title.includes(val)).map((item) => ({
        ...item,
        isLeaf: true,
      }));
      this.treeData$.next(searchedData);
    }
  };
  public update(orgList: IOrgs[]) {
    const rootId = this.getRootInfo().key;
    const newList = [...this.flatTreeData$.getValue(), ...orgList];
    this.flatTreeData$.next(newList);
    const treeData = orgsToOrgTrees(newList, rootId);
    this.treeData$.next(treeData);
    this.treeDataCache = treeData;
  }

  public destroy() {
    this.deleteOrgController.destroy();
    this.modifyOrgController?.destroy();
    this.permissionOnlyController?.destroy();
    this.isRoot$.complete();
    this.currentSelectId$.complete();
    this.flatTreeData$.complete();
    this.treeData$.complete();
    this.searchValue$.complete();
    this.isVirtual$.complete();
    this.orgActionTrigger$.complete();
    this.modifyOrgController = undefined;
    this.permissionOnlyController = undefined;
    this.createOrgFunc = undefined;
    this.updateOrgFunc = undefined;
    this.deleteOrgFunc = undefined;
    this.onOrgSelectFunc = undefined;
    this.queryPermissionUiDataFunc = undefined;
    this.openResourceFunc = undefined;
    this.Model = null!;
    this.mapCache = undefined!;
    this.treeDataCache = undefined!;
    this.currentDataCache = undefined;
  }

  public getModifyOrgController() {
    if (!this.modifyOrgController && this.queryPermissionUiDataFunc) {
      this.modifyOrgController = new DialogModifyFromOrganizationController<IOrgs, IOrgModalData>({
        loadDrawerUiDataFunc: this.queryDrawerOrgUiData,
        loadDrawerPermissionUiDataFunc: this.queryPermissionUiDataFunc,
        modifyDataFunc: this.modifyOrgDataToService,
        openResourceFunc: this.openResourceFunc,
        openPermissionFunc: this.openPermission,
      });
    }
    return this.modifyOrgController;
  }

  public getPermissionOnlyController() {
    if (!this.permissionOnlyController && this.queryPermissionUiDataFunc) {
      this.permissionOnlyController = new DrawerModifyFormPermissionOnlyController<IOrgs['permission'], IOrgs>({
        loadDrawerUiDataFunc: this.queryPermissionUiDataFunc,
        modifyDataFunc: this.modifyUserPermissionToService,
      });
    }
    return this.permissionOnlyController;
  }

  public getDeleteOrgController() {
    return this.deleteOrgController;
  }

  public getFlatTreeData$() {
    return this.flatTreeData$;
  }

  public getTreeData$() {
    return this.treeData$;
  }

  public getIsRoot$() {
    return this.isRoot$;
  }

  public getIsVirtual$() {
    return this.isVirtual$;
  }

  public getSearchValue$() {
    return this.searchValue$;
  }

  public getCurrentSelectId$() {
    return this.currentSelectId$;
  }

  public openPermission = () => {
    const flatTreeData = this.flatTreeData$.getValue();
    const currentSelectId = this.currentSelectId$.getValue();
    const originalData = _.find(flatTreeData, ({ key }) => _.isEqual(key, currentSelectId));
    this.permissionOnlyController?.openModal(originalData).subscribe((result) => {
      result.success && this.updateOrgs(result.result!);
    });
    this.modifyOrgController?.closeModal();
  };

  public getRootInfo = () => {
    return {
      title: this.appName,
      key: APP_KEY_SUFFIX + this.appId,
    };
  };

  public getFooterPermission = () => {
    return {
      disableCreate: !this.enablePermission,
      disableManage: !this.enablePermission || this.isRoot$.getValue(),
    };
  };

  public getShowfooter() {
    return _.isBoolean(this.showFooter) ? this.showFooter : true;
  }

  public getShowOpactor() {
    return _.isBoolean(this.showOpactor) ? this.showOpactor : true;
  }

  // 新增子部门
  public createOrgData = () => {
    const defaultOrgValue = {
      title: '',
      key: '',
      pid: this.currentSelectId$.getValue(),
      permission: [],
    };
    this.modifyOrgController?.openModal(defaultOrgValue).subscribe((result) => {
      result.success && this.createOrgs(result.result!);
    });
  };

  // 管理部门
  public editOrgData = () => {
    const flatTreeData = this.flatTreeData$.getValue();
    const currentSelectId = this.currentSelectId$.getValue();

    const originalData = _.find(flatTreeData, ({ key }) => _.isEqual(key, currentSelectId));
    this.modifyOrgController?.openModal(originalData).subscribe((result) => {
      result.success && this.updateOrgs(result.result!);
    });
  };

  // 删除部门
  public deleteOrgData = () => {
    const flatTreeData = this.flatTreeData$.getValue();
    const currentSelectId = this.currentSelectId$.getValue();
    const originalData = _.find(flatTreeData, ({ key }) => _.isEqual(key, currentSelectId));
    this.deleteOrgController.openModal(originalData).subscribe((result) => {
      result.success && this.deleteOrgs(result.result!);
    });
  };

  public onOrgSelect = (keys: string[], root?: boolean) => {
    // 过滤掉不选择或者二次点击取消选择的情况
    if (!keys.length) {
      return;
    }
    const currentKey = keys[0];
    const isRoot = !!root;
    const flatData = this.flatTreeData$.getValue();
    this.isRoot$.next(isRoot);
    this.currentSelectId$.next(currentKey);
    this.onOrgSelectFunc?.(currentKey, isRoot, flatData);
  };

  public orgMenuOnClick = (info: MenuInfo) => {
    switch (info.key) {
      case IOrgOperateEnum.ADD_CHILD:
        this.createOrgData();
        break;
      case IOrgOperateEnum.EDIT:
        this.editOrgData();
        break;
      case IOrgOperateEnum.DELETE:
        this.deleteOrgData();
        break;
    }
  };

  // --------- 构建类 ---------
  public renderTitleByTree = (data: DataNode[]): DataNode[] => {
    const showOpactor = this.getShowOpactor();
    if (!showOpactor) {
      return data;
    }
    return _.map(data, ({ title, children, ...rest }: DataNode) => {
      let child: DataNode[] | undefined;
      if (children?.length) {
        child = this.renderTitleByTree(children);
      }
      return {
        ...rest,
        children: child,
        title: (
          <div className="module_org_manage_tree_list-tree-cell">
            <TooltipText text={title as string} />
            <Dropmenu
              className="module_org_manage_tree_list-tree-cell-operate"
              menus={[
                {
                  title: i18n.chain.proMicroModules.orgList.addChildOrg,
                  key: IOrgOperateEnum.ADD_CHILD,
                  disabled: !this.enablePermission,
                },
                {
                  title: i18n.chain.proMicroModules.orgList.orgManage,
                  key: IOrgOperateEnum.EDIT,
                  disabled: !this.enablePermission,
                },
                {
                  title: i18n.chain.proMicroModules.orgList.delOrg,
                  key: IOrgOperateEnum.DELETE,
                  disabled: !this.enablePermission,
                },
              ]}
              values={[]}
              icon="more"
              iconType="only-icon"
              onClickMenuItem={this.orgMenuOnClick}
            />
          </div>
        ),
      };
    }) as DataNode[];
  };

  // 加载部门的UI数据
  private queryDrawerOrgUiData = () => {
    const { title, ...rest } = this.getRootInfo();
    return of({
      treeData: [{ title: <OrgRoot title={title} />, ...rest }, ...this.treeDataCache],
      currentKey: this.currentSelectId$.getValue(),
    });
  };

  // 渲染部门删除弹窗所需信息
  private initDeleteOrgModalOptions = () => {
    return {
      emotion: 'help-2',
      title: i18n.chain.proMicroModules.orgList.delConfirm,
      description: i18n.chain.proMicroModules.orgList.delConfirmText,
    };
  };

  // --------- 数据类 - 部门 ---------

  // 服务端修改用户权限
  private modifyUserPermissionToService = async (permission: IOrgs['permission']) => {
    const flatTreeData = this.flatTreeData$.getValue();
    const currentId = this.currentSelectId$.getValue();
    const originalData = _.find(flatTreeData, ({ key }) => _.isEqual(key, currentId));
    return this.modifyOrgDataToService({ ...originalData!, permission }, originalData);
  };

  private updateOrgs = (data: IOrgs) => {
    const flatData = this.flatTreeData$.getValue();
    const currentId = this.currentSelectId$.getValue();
    const treeData = this.treeData$.getValue();
    const result: IOrgs[] = _.map(flatData, (item) => {
      if (_.isEqual(item.key, currentId)) {
        return { ...item, ...data };
      }
      return item;
    });
    this.currentDataCache = { type: 'update', data };
    this.flatTreeData$.next(result);
    this.treeData$.next(updateTreeWithKey(treeData, currentId, data));
  };

  private createOrgs = (data: IOrgs) => {
    const flatData = this.flatTreeData$.getValue();
    const treeData = this.treeData$.getValue();
    const rootId = this.getRootInfo().key;
    const result = _.concat(flatData, [data]);
    const currentData = { ...data, isLeaf: true };
    this.currentDataCache = { type: 'create', data: currentData };
    this.flatTreeData$.next(result);

    if (data.pid === rootId) {
      this.treeData$.next(_.concat(treeData, [currentData]));
    } else {
      this.treeData$.next(addToTreeWithKey(treeData, data.key, data));
    }
  };

  private deleteOrgs = (data: IOrgs) => {
    const flatData = this.flatTreeData$.getValue();
    const treeData = this.treeData$.getValue();
    const result = _.filter(flatData, ({ key }) => !_.isEqual(key, data.key));
    this.currentDataCache = { type: 'delete', data };
    this.flatTreeData$.next(result);
    this.treeData$.next(removeToTreeWithKey(treeData, data.key, data));
    this.currentSelectId$.next(this.getRootInfo().key);
    this.isRoot$.next(true);
    this.onOrgSelect([this.getRootInfo().key], true);
  };

  private getLeafTreeData = (data: IOrgTrees[]) => {
    return _.map(data, (item) => ({
      ...item,
      isLeaf: !this.mapCache[item.key]?.length,
    }));
  };

  private getRootTreeData = () => {
    const rootId = this.getRootInfo().key;
    const data = this.flatTreeData$.getValue();
    return this.getLeafTreeData(getCurrentOrgs(data, rootId));
  };

  private listenFlatData = () => {
    const rootId = this.getRootInfo().key;
    this.flatTreeData$.subscribe((data) => {
      const { type, data: current } = this.currentDataCache || {};
      // 构造部门id->下层tree的映射表
      this.mapCache = getOrgKeyToChildMap(data, rootId);
      // 是否还是虚拟
      this.isVirtual$.next(data.length > START_VIRTUAL_COUNT);
      // 当前树结构，缓存
      const treeData = orgsToOrgTrees(data, rootId);
      this.treeDataCache = treeData;

      if (type && current) {
        this.orgActionTrigger$.next({
          actionType: type,
          value: {
            current,
            treeData,
            flatData: data,
          },
        });
      }
    });
  };

  private listenActionTrigger = () => {
    this.orgActionTrigger$.subscribe((trigger) => {
      if (!trigger) {
        return;
      }
      const { actionType, value } = trigger;
      switch (actionType) {
        case 'update':
          this.updateOrgFunc?.(value.current, value.treeData, value.flatData);
          break;
        case 'create':
          this.createOrgFunc?.(value.current, value.treeData, value.flatData);
          break;
        case 'delete':
          this.deleteOrgFunc?.(value.current, value.treeData, value.flatData);
          break;
      }
    });
  };
  // 部门向服务端修改数据
  private modifyOrgDataToService = async (params: IOrgFormData, originalData?: IOrgs) => {
    const rootId = this.getRootInfo().key;
    if (originalData?.key) {
      const resp = await this.Model.updateOrgs(params, originalData, rootId).toPromise();
      resp.success && toastApi.success(i18n.chain.proMicroModules.orgList.editOrgSuccess);
      return { ...resp, result: _.merge(originalData, params) };
    }
    const resp = await this.Model.createOrgs(this.appId, params, rootId).toPromise();
    resp.success && toastApi.success(i18n.chain.proMicroModules.orgList.createOrgSuccess);
    return resp;
  };

  // 删除部门
  private deleteOrgToService = async (originalData?: IOrgs) => {
    const delIds = getChildOrgsWithOrg(originalData?.key, this.flatTreeData$.getValue());
    const resp = await this.Model.deleteOrgs(delIds.join(','), originalData!).toPromise();
    resp.success && toastApi.success(i18n.chain.proMicroModules.orgList.delOrgSuccess);
    return resp;
  };
}

export { OrgManageTreeListController };
