.module_org_manage_search {
  margin-bottom: 12px;
}

.module_org_manage_tree_list {
  flex-shrink: 0;
  width: 260px;
  height: 100%;
  padding: 12px;
  color: var(--dmc-text-color);
  background-color: var(--dmc-org-list-bg-color);

  &-root {
    height: 32px;
    padding: 4px 0 4px 20px;
    color: var(--metro-text-1);

    &:hover:not(&-active) {
      background: var(--dmc-tree-bg-hover-color);
    }
  }

  &-root-active {
    color: var(--dmc-tree-text-active-color);
    background: var(--dmc-tree-bg-selected-color);
  }

  .metro-tree-node-content-wrapper {
    overflow: hidden;
  }

  &-tree-cell {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 205px;
    padding-right: 6px;

    &-operate {
      flex-shrink: 0;
      visibility: hidden;
    }

    &:hover {
      .module_org_manage_tree_list-tree-cell-operate {
        visibility: visible;
      }
    }
  }

  &-footer {
    position: absolute;
    bottom: 20px;
    display: inline-flex;
    justify-content: space-between;
    width: 236px;
  }
}

.component_organization-root {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;

  :first-child {
    flex-shrink: 0;
    margin-right: 5px;
  }
}
