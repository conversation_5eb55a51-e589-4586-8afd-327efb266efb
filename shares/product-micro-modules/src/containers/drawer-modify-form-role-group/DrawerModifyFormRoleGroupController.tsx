import _ from 'lodash';
import { combineLatest } from 'rxjs';
import { FormCompController } from '@mdtBsComponents/form-comp';
import {
  IBusinessResult,
  IInnerView,
  ModalWithBtnsCompDrawerController,
} from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import i18n from '../../languages';
import { TransferPanelController } from '../transfer-panel';
import { IUserSelectorModel } from '../user-selector';
import { DrawerModifyFormRoleGroupInner } from './DrawerModifyFormRoleGroupInner';

export enum FormDataAttrs {
  NAME = 'name',
  DESCRIPTION = 'description',
  USERS = 'users',
}

export interface IFormData {
  [FormDataAttrs.NAME]: string;
  [FormDataAttrs.DESCRIPTION]: string;
  [FormDataAttrs.USERS]?: TreeLabelValueItemInterface[];
}

interface IUiData {
  userOptions: TreeLabelValueItemInterface[];
  users?: TreeLabelValueItemInterface[];
}

export type IModifyDataFunc<V> = (data: IFormData, originalData?: V) => Promise<IBusinessResult<V>>;

export interface IControllerOptions<V> {
  appId: number;
  modifyDataFunc: IModifyDataFunc<V>;
  loadUiDataFunc: (rest?: V) => Promise<IUiData>;
  userSelectorModel: IUserSelectorModel;
}

class DrawerModifyFormRoleGroupController<V extends IFormData = any> extends ModalWithBtnsCompDrawerController<V> {
  // 表单控制器
  private formCompController: FormCompController<V>;
  // 点击ok按钮回调
  private modifyDataFunc: IModifyDataFunc<V>;
  private usersController?: TransferPanelController;
  private loadUiDataFunc;
  private userSelectorModel: IUserSelectorModel;

  public constructor(options: IControllerOptions<V>) {
    super({
      modalCompOptions: {
        modalOptions: () => ({ width: '600px' }),
        innerViewController: () => this,
        InnerView: DrawerModifyFormRoleGroupInner as IInnerView<ModalWithBtnsCompDrawerController<V>, V>,
        beforeOpenFunc: (rest?: V) => this.loadUiDataFunc(rest),
      },
      uiOptions: () => this.initUiOptions(),
      clickOkBtnFunc: () => this.onClickDrawerOkBtn(),
    });
    this.userSelectorModel = options.userSelectorModel;
    this.modifyDataFunc = options.modifyDataFunc;
    this.formCompController = new FormCompController();
    this.loadUiDataFunc = options.loadUiDataFunc;
  }

  public destroy() {
    super.destroy();
    this.formCompController.destroy();
    this.usersController?.destroy();
    this.usersController = undefined;
    this.modifyDataFunc = null!;
    this.loadUiDataFunc = null!;
  }

  public openModal(data?: V) {
    const result = super.openModal(data);
    const subscription = this.getModalCompController()
      .getLoadingModalData$()
      .subscribe((isLoading) => {
        if (!isLoading) {
          this.init(data);
          subscription.unsubscribe();
        }
      });
    return result;
  }

  public init(value?: V) {
    const { users } = this.getModalUiData()!;
    this.formCompController?.changeFormData(value);
    if (value) {
      this.formCompController.changeFormDataError(false);
    }
    this.usersController = new TransferPanelController({
      // disableOrg: true,
      // disableRole: true,
      // disableGroup: true,
      pullUsers: true,
      userIds: _.map(users, 'key'),
      userSelectorModel: this.userSelectorModel,
    });
    this.computeConfirmBtnDisabled();
  }

  public getFormCompController() {
    return this.formCompController!;
  }

  public getUsersController() {
    return this.usersController;
  }

  // 点击弹窗OK按钮
  private onClickDrawerOkBtn = async () => {
    await this.formCompController.validateFormData();
    if (this.formCompController.dataHasError()) {
      return {
        success: false,
      };
    }

    const value = this.formCompController!.getFormData$().getValue()!;
    return this.modifyDataFunc(
      { ...value, users: this.usersController!.getSelectedItems$().getValue() },
      this.getModalRest(),
    );
  };

  private initUiOptions() {
    return {
      title: this.getModalRest()
        ? i18n.chain.proMicroModules.role.group.groupManage
        : i18n.chain.proMicroModules.role.group.groupCreate,
    };
  }

  private computeConfirmBtnDisabled() {
    combineLatest(this.formCompController.getFormDataError$(), this.usersController!.getSelectedItems$()).subscribe(
      ([formError, selectedUsers]) => {
        this.changeDisabledOkBtn(formError || _.isEmpty(selectedUsers));
      },
    );
  }
}

export { DrawerModifyFormRoleGroupController };
