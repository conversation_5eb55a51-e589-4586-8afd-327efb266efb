import _ from 'lodash';
import { FormCompController } from '@mdtBsComponents/form-comp';
import {
  IBusinessResult,
  IDrawerHeaderProps,
  IDynamicOptions,
  IInnerView,
  ModalWithBtnsCompDrawerController,
} from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import {
  DialogUsersTransferPanelController,
  IUserOptions,
  UsersTransferPanelTabEnum,
} from '../dialog-users-transfer-panel';
import { UserLazySelectorController, UserLazySelectorModel } from '../user-lazy-selector';
import { DrawerModifyFormThemeShareInner } from './DrawerModifyFormThemeShareInner';

export enum FormDataAttrs {
  USERS = 'users',
  THEMES = 'themes',
  APPS = 'apps',
  // SHOW_DATAPKG_WITH_NO_ALBUMS = 'showDatapkgWithNoAlbums',
}

export interface IFormData {
  [FormDataAttrs.USERS]?: number[];
  [FormDataAttrs.THEMES]?: TreeLabelValueItemInterface[];
  [FormDataAttrs.APPS]?: TreeLabelValueItemInterface[];
  // [FormDataAttrs.SHOW_DATAPKG_WITH_NO_ALBUMS]?: boolean;
}

export type IModifyDataFunc<V> = (originalData?: V) => Promise<IBusinessResult<V>>;

export interface IControllerOptions<V> {
  modifyDataFunc: IModifyDataFunc<V>;
  initDrawerHeaderPropsFunc: IDynamicOptions<IDrawerHeaderProps<any>>;
  appId: number;
}

class DrawerModifyFormThemeShareController extends ModalWithBtnsCompDrawerController<IFormData> {
  // 表单控制器
  private formCompController?: FormCompController<IFormData>;
  // 点击ok按钮回调
  private modifyDataFunc: IModifyDataFunc<IFormData>;
  // 当前的options类型
  private currentType?: FormDataAttrs;
  private editUserController: DialogUsersTransferPanelController;
  private userSelectorController: UserLazySelectorController;
  private compOptions: Record<string, TreeLabelValueItemInterface[]> = {
    [FormDataAttrs.USERS]: [],
    [FormDataAttrs.THEMES]: [],
    [FormDataAttrs.APPS]: [],
  };

  public constructor(options: IControllerOptions<IFormData>) {
    super({
      modalCompOptions: {
        modalOptions: () => this.initDrawerProps(),
        innerViewController: () => this,
        InnerView: DrawerModifyFormThemeShareInner as IInnerView<
          ModalWithBtnsCompDrawerController<IFormData>,
          IFormData
        >,
        beforeOpenFunc: (rest) => this.initEditInfo(rest),
      },
      uiOptions: options.initDrawerHeaderPropsFunc,
      clickOkBtnFunc: () => this.onClickDrawerOkBtn(),
      beforeCancelFunc: () => this.destroyEditInfo(),
    });
    this.modifyDataFunc = options.modifyDataFunc;
    this.editUserController = new DialogUsersTransferPanelController({
      onClickOkFunc: (values) => this.onUsersSelect(values.user),
      title: i18n.chain.proMicroModules.themeShare.editDetail,
    });
    this.userSelectorController = new UserLazySelectorController({
      app: DatlasAppController.getInstance(),
      Model: new UserLazySelectorModel({ appId: options.appId }),
      disableOrg: true,
      disableRole: true,
      disableGroup: true,
    });
  }

  public destroy() {
    super.destroy();
    this.destroyEditInfo();
    this.editUserController.destroy();
    this.userSelectorController.destroy();
    this.modifyDataFunc = this.defaultModifyDataFunc;
    this.compOptions = {};
    this.currentType = undefined;
  }

  public initCompOptions(options: Record<string, TreeLabelValueItemInterface[]>) {
    this.compOptions = options;
  }

  public getEditUserController() {
    return this.editUserController;
  }

  public getUserSelectorController() {
    return this.userSelectorController;
  }

  // 复写父类的方法
  public openModal(rest?: IFormData) {
    this.destroyEditInfo();
    return super.openModal(rest);
  }

  // 复写父类方法
  public closeModal() {
    this.destroyEditInfo();
    super.closeModal();
  }

  public getFormCompController() {
    return this.formCompController!;
  }

  public editByUser(type: FormDataAttrs) {
    const formData = this.formCompController?.getFormDataValue() as IFormData;
    this.currentType = type;
    if (type === FormDataAttrs.USERS) {
      this.userSelectorController
        .openModal({
          userIds: formData[FormDataAttrs.USERS],
        })
        .subscribe((resp) => {
          if (resp.success) {
            const result = { ...formData, [FormDataAttrs.USERS]: _.map(resp.result, ({ id }) => _.toNumber(id)) };
            this.formCompController?.changeFormData(result);
            this.formCompController?.getFormIns()?.setFieldsValue(result);
          }
        });
      return;
    }
    this.editUserController.init(this.compOptions[type] as IUserOptions[]);
    this.editUserController.openModal({
      [UsersTransferPanelTabEnum.USER]: _.map(formData[type], ({ key }) => _.toString(key)),
    });
  }

  private destroyEditInfo = async () => {
    this.formCompController?.destroy();
    this.formCompController = undefined;
    return true;
  };

  // 点击弹窗OK按钮
  private onClickDrawerOkBtn = async () => {
    // 保证代码健壮, 一般都能保证有值
    if (!this.formCompController) {
      console.warn('unkonw error, formCompController is not exist, please reflush browse!');
      return { success: false };
    }
    await this.formCompController.validateFormData();
    // 如果有错误，则返回
    if (this.formCompController.dataHasError()) return { success: false };
    const formData = this.formCompController?.getFormDataValue() as IFormData;
    return this.modifyDataFunc(formData);
  };

  private onUsersSelect = (users: TreeLabelValueItemInterface[]) => {
    const formData = this.formCompController?.getFormDataValue() as IFormData;
    const result = { ...formData, [this.currentType as FormDataAttrs]: users };
    this.formCompController?.changeFormData(result);
    this.formCompController?.getFormIns()?.setFieldsValue(result);
  };

  // 弹窗所需信息
  private initDrawerProps = () => {
    return {
      width: '550px',
    };
  };

  private initEditInfo(rest?: IFormData) {
    const defaultRest: IFormData = {
      [FormDataAttrs.USERS]: [],
      [FormDataAttrs.THEMES]: this.compOptions[FormDataAttrs.THEMES],
      [FormDataAttrs.APPS]: this.compOptions[FormDataAttrs.APPS],
      // [FormDataAttrs.SHOW_DATAPKG_WITH_NO_ALBUMS]: true,
    };
    this.formCompController = new FormCompController<IFormData>(rest || defaultRest);
    return Promise.resolve();
  }

  // 默认的修改数据方法
  private async defaultModifyDataFunc() {
    return { success: false };
  }
}

export { DrawerModifyFormThemeShareController };
