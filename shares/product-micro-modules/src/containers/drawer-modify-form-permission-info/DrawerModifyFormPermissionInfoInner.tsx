import { FormComp } from '@mdtBsComponents/form-comp';
import i18n from '../../languages';
import { DrawerModifyFormPermissionInfoController } from './DrawerModifyFormPermissionInfoController';
import './index.less';

// 子组件--基本信息表单================================================================================
interface IProps<U, F, V> {
  controller: DrawerModifyFormPermissionInfoController<U, F, V>;
}
function BaseInfo<U, F, V>({ controller }: IProps<U, F, V>) {
  const InnerView = controller.getFormInnerView();
  const modalUiData = controller.getModalUiData();
  return (
    <FormComp controller={controller.getFormCompController()}>
      <InnerView modalUiData={modalUiData} controller={controller} />
    </FormComp>
  );
}

// DrawerModifyRoleInner组件=========================================================================
export function DrawerModifyFormPermissionInfoInner<U, F, V>({ controller }: IProps<U, F, V>) {
  return (
    <div className="module_drawer-modify-form-permission">
      <div className="drawer-permission_base-info">
        <div className="drawer-permission_title">{i18n.chain.proMicroModules.permission.basic}</div>
        <div className="drawer-permission_card">
          <BaseInfo controller={controller} />
        </div>
      </div>
    </div>
  );
}
