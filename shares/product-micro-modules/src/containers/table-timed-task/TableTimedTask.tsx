import { FC } from 'react';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import { TableCurdWithSimpleSearch } from '../table-curd-with-simple-search';
import { TableTimedTaskController } from './TableTimedTaskController';
import './index.less';

interface IProps {
  controller: TableTimedTaskController;
}

const TableTimedTask: FC<IProps> = ({ controller }) => {
  return (
    <div className="module_table-timed-task">
      <TableCurdWithSimpleSearch controller={controller.getTableController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
      <ModalWithBtnsCompDialog controller={controller.getModifyController()} />
    </div>
  );
};

export { TableTimedTask };
