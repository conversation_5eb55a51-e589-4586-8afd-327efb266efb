import { FC } from 'react';
import { FormField } from '@mdtBsComm/components/form';
import { FormComp } from '@mdtBsComponents/form-comp';
import DatePicker, { TimeFormat } from '@mdtDesign/date-picker';
import i18n from '../../languages';
import { EmotionExpireDateController } from './EmotionExpireDateController';
import './index.less';

interface IProps {
  controller: EmotionExpireDateController<any>;
}

const ExpireComp: FC<IProps> = ({ controller }) => {
  const formCompController = controller.getFormCompController();
  return (
    <div className="module_emotion-expire-date">
      <FormComp controller={formCompController}>
        <FormField
          direction="column"
          label=""
          name="expire"
          required
          rules={[{ required: true, message: i18n.chain.comPlaceholder.select }]}
        >
          <DatePicker timeFormat={TimeFormat.hm} />
        </FormField>
      </FormComp>
    </div>
  );
};

export { ExpireComp };
