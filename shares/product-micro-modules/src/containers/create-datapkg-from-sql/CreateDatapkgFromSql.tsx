import _ from 'lodash';
import { FC } from 'react';
import { FormField } from '@mdtBsComm/components/form';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { FormComp } from '@mdtBsComponents/form-comp';
import { SqlEditorComp } from '@mdtBsComponents/sql-editor-comp';
import Button, { LinkButton } from '@mdtDesign/button';
import Icon from '@mdtDesign/icon';
import Input from '@mdtDesign/input';
import Select from '@mdtDesign/select';
import Spin from '@mdtDesign/spin';
import { getOwnershipOptions } from '@mdtProComm/utils/datapkgUtil';
import { CreateDatapkgPreview } from '@mdtProTasks/create-datapkg-from-upload-local-file-task/create-datapkg-preview';
import { FormItemRefreshTime } from '../../ascatter-expand/datapkg-refresh-timer/FormItemRefreshTime';
import i18n from '../../languages';
import { CreateDatapkgFromSqlController, PkgFormAttrs } from './CreateDatapkgFromSqlController';
import { IUiData } from './CreateDatapkgFromSqlModel';
import './index.less';

interface IProps {
  controller: CreateDatapkgFromSqlController;
  modalUiData?: IUiData;
}

const SqlHead: FC<IProps> = ({ controller }: IProps) => {
  const sqlEditorController = controller.getSqlEditorController();
  const runSqlBtnLoading = useObservableState(sqlEditorController.getRunSqlBtnLoading$());
  const formatSqlBtnLoading = useObservableState(sqlEditorController.getFormatSqlBtnLoading$());

  const runSql = () => sqlEditorController.runSql();
  const formatSql = () => sqlEditorController.formatSql();

  return (
    <div className="sql-edit_head">
      <div className="sql-left-box">
        <span className="title-text">SQL</span>
        <Button
          className="operation-btn"
          type="assist-bg"
          leftIcon="arrow-right"
          loading={runSqlBtnLoading}
          onClick={runSql}
        >
          {i18n.chain.proMicroModules.datapkg.datapkgSql.play}
        </Button>
        <Button
          className="right-btn"
          type="assist-bg"
          loading={formatSqlBtnLoading}
          leftIcon="format-align-left"
          onClick={formatSql}
        >
          {i18n.chain.proMicroModules.datapkg.datapkgSql.format}
        </Button>
      </div>
    </div>
  );
};

const SqlContainer: FC<IProps> = ({ controller }: IProps) => {
  return (
    <div className="sql-container">
      <SqlHead controller={controller} />
      <SqlEditorComp controller={controller.getSqlEditorController()} />
    </div>
  );
};

export const PkgCreate: FC<IProps> = (props: IProps) => {
  const { controller, modalUiData } = props;
  const { dsOptions, geometryOptions } = modalUiData!;
  const isUpdate = controller.enableUpdate();
  const ownershipFix = controller.ownershipFix();

  const linkEle = controller.showCreateDatasource() ? (
    <LinkButton onClick={() => controller.goDatasource()}>
      {i18n.chain.proMicroModules.datapkg.datapkgSql.toCreate}
      <Icon icon="geo-flow" />
    </LinkButton>
  ) : (
    i18n.chain.proMicroModules.datapkg.datapkgSql.noDatasetTip
  );
  const goDatasource = (
    <div className="datasource-tip">
      {i18n.chain.proMicroModules.datapkg.datapkgSql.noDataset}
      {linkEle}
    </div>
  );

  let otherUserDatasourceTip: any = null;
  if (isUpdate) {
    const dsId = controller.getModalRest()!.datasource;
    if (!_.find(dsOptions, (item) => item.value === dsId)) {
      otherUserDatasourceTip = (
        <Input placeholder={i18n.chain.proMicroModules.datapkg.datapkgSql.otherDataSet} disabled={true} />
      );
    }
  }

  return (
    <FormComp controller={controller.getPkgFormController()} className="create-form">
      <FormField
        name={PkgFormAttrs.OWNERSHIP}
        label={i18n.chain.proMicroModules.datapkg.datapkgSql.ownership}
        required={true}
        rules={[{ required: true, message: i18n.chain.proMicroModules.datapkg.datapkgSql.ownershipEmptyError }]}
        direction="column"
      >
        <Select
          placeholder={i18n.chain.comPlaceholder.select}
          options={getOwnershipOptions()}
          disabled={ownershipFix}
        />
      </FormField>

      <FormField
        name={PkgFormAttrs.NAME}
        label={i18n.chain.proMicroModules.datapkg.datapkgName}
        required={true}
        rules={[{ required: true, message: i18n.chain.proMicroModules.datapkg.datapkgNameEmptyError }]}
        direction="column"
      >
        <Input placeholder={i18n.chain.comPlaceholder.input} allowClear={false} disabled={isUpdate} />
      </FormField>

      <div className="datasource-wrap">
        <FormField
          name={PkgFormAttrs.DATASOURCE}
          label={i18n.chain.proMicroModules.datapkg.datapkgSql.dataset}
          required={true}
          rules={[{ required: true, message: i18n.chain.proMicroModules.datapkg.datapkgSql.datasetEmptyError }]}
          direction="column"
        >
          <Select placeholder={i18n.chain.comPlaceholder.select} options={dsOptions} disabled={isUpdate} />
        </FormField>
        <div className="tip">{otherUserDatasourceTip}</div>
      </div>

      {goDatasource}

      <FormField
        name={PkgFormAttrs.GEOMETRY_TYPE}
        label={i18n.chain.proMicroModules.datapkg.datapkgSql.geoType}
        required={true}
        rules={[{ required: true, message: i18n.chain.proMicroModules.datapkg.datapkgSql.geoTypeEmptyError }]}
        direction="column"
      >
        <Select placeholder={i18n.chain.comPlaceholder.select} options={geometryOptions} disabled={isUpdate} />
      </FormField>

      <FormItemRefreshTime />
    </FormComp>
  );
};

const DataPreviewWithLoading: FC<IProps> = (props: IProps) => {
  const { controller } = props;
  const loading = useObservableState(controller.getLoadingPreviewData$());
  const previewController = controller.getPreviewController();

  if (loading) return <Spin tip={i18n.chain.proMicroModules.datapkg.datapkgSql.previewLoading} className="loading" />;

  return previewController ? <CreateDatapkgPreview controller={previewController} /> : null;
};

const SqlTip: FC<IProps> = ({ controller }: IProps) => {
  const hasDs = useObservableState(controller.getHasDatasource$());

  return hasDs ? null : (
    <div className="sql-tip">
      <div className="tip-inner">
        <Icon icon="left-back" size={20} />
        {i18n.chain.proMicroModules.datapkg.datapkgSql.chooseDatasetEmpty}
      </div>
    </div>
  );
};

export const CreateDatapkgFromSql: FC<IProps> = (props: IProps) => {
  const { controller } = props;
  return (
    <div className="module_create-pkg-from-sql">
      <PkgCreate {...props} />
      <div className="data-container">
        <SqlContainer controller={controller} />
        <DataPreviewWithLoading controller={controller} />
        <SqlTip controller={controller} />
      </div>
    </div>
  );
};
