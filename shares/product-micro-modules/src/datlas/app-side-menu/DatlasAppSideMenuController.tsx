import { FC } from 'react';
import { BaseSideMenuController } from '@mdtProComm/controllers/BaseSideMenuController';
import { IDatlasAppController } from '../app/DatlasAppController';

abstract class DatlasAppSideMenuController extends BaseSideMenuController {
  protected app?: IDatlasAppController;

  public constructor(app: IDatlasAppController) {
    super();
    this.app = app;
  }

  public destroy() {
    super.destroy();
    this.app = undefined;
  }

  public changeRouter(router: string) {
    const rt = this.app!.getRouterController();
    rt.gotoPath(router);
  }

  public getExtraClassName() {
    return '';
  }

  public getExtraRender(): FC<any> | undefined {
    return undefined;
  }

  public abstract initMenus(): void;
}

export { DatlasAppSideMenuController };
