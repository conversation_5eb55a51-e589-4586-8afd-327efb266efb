import _ from 'lodash';
import { WarningTriangle } from '@metro/icons';
import { Modal } from '@metroDesign/modal';
import { Space } from '@metroDesign/space';
import { toastApi, ToastContainer } from '@metroDesign/toast';
import type { NoticeType } from '@metroDesign/toast/interface';
import type { IOnetableExecuteCommandsPost } from '@mdtApis/interfaces';
import { isPc } from '@mdtBsComm/utils';
import { deleteDatapkgAsync, patchDatapkgRowsAsync } from '@mdtBsServices/datapkgs';
import {
  deleteFloworkFormAsync,
  deleteWorkflowAsync,
  excuteOnetableCommandsAsync,
  getWorkflowAsync,
  postWorkflowCancelAsync,
  queryOnetableInvolvedUsersAsync,
} from '@mdtBsServices/flowork';
import { openFormView } from '../components/form-view';
import { ModalReason } from '../components/modal-reason';
import {
  IOpenOnetableNewPublishConfig,
  openOnetableNewPublishConfigModal,
} from '../components/one-table-new-publish-config';
import { ONE_TABLE_INFO } from '../datlas/datlasConfig';
import type { IOneTableNewOperatorDataComm } from '../interfacts';
import i18n from '../languages';
import { IOpenBatchFillFormOptions, openBatchFillFormModal } from '../pages/one-table-new-batch-fill';
import { IOpenOneTableNewFillFormOptions, openOneTableNewFillFormDrawer } from '../pages/one-table-new-fill-form';
import { IOpenOneTableNewIssuedFormOptions, openOneTableNewIssuedFormDrawer } from '../pages/one-table-new-issued-form';
import {
  IOpenOneTableNewPreviewFormOptions,
  openOneTableNewPreviewFormDrawer,
} from '../pages/one-table-new-preview-form';
import {
  IOpenOneTableNewReportCreateOptions,
  IOpenOneTableNewReportSettingOptions,
  openOneTableNewReportCreateDrawer,
  openOneTableNewReportSettingDrawer,
} from '../pages/one-table-new-report-create';
import {
  IOpenOneTableNewReportDetailOptions,
  openOneTableNewReportDetailDrawer,
} from '../pages/one-table-new-report-detail/drawer-one-table-new-report-detail';
import {
  IOpenOneTableNewReportDetailH5Options,
  openOneTableNewReportDetailH5Drawer,
} from '../pages/one-table-new-report-detail-h5/drawer-one-table-new-report-detail-h5';
import {
  IOpenOnetableNewSubmitConfigOptions,
  openOnetableNewSubmitConfigModal,
} from '../pages/one-table-new-submit-confirm';
import { getDownstreamUsers, getTransformDatapkgAssignUserFilter } from '../utils/oneTableNewFilterUtil';
import {
  BooleanEnum,
  checkUserExistsInMap,
  COLUMN_ASSIGN_USER,
  COLUMN_STATUS,
  getAssignees,
  getDisplayFormName,
  getOnetableRootDownstreamUsers,
  getPrimaryAssignee,
} from '../utils/oneTableNewUtil';

interface IDoOneTableNewOperatorOptions {
  itemData: IOneTableNewOperatorDataComm;
  successCallback?: () => void;
}

// 结束流程
export const doOneTableNewOperatorCompleteFlow = async ({
  itemData,
  successCallback,
}: IDoOneTableNewOperatorOptions) => {
  Modal.confirm({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.cancelFlowTip),
    content: i18n.chain.proMicroModules.oneTable.cancelFlowDesc,
    icon: <WarningTriangle />,
    okButtonProps: { danger: true },
    onOk: async () => {
      const resp = await postWorkflowCancelAsync(itemData.rootWfId, {
        cancel_linked_workflows: 'descendants',
        success: true,
      });
      if (!resp.success) return;
      toastApi.success(i18n.chain.comTip.optSuccess);
      successCallback?.();
    },
  });
};

// 草稿编辑
export const doOneTableNewOperatorDraftEdit = async (
  options: Pick<IOpenOneTableNewReportCreateOptions, 'formType' | 'formId' | 'onSuccessCb' | 'onCancelCb'>,
) => {
  openOneTableNewReportCreateDrawer(options);
};

// 删除草稿
interface IDoOneTableNewOperatorDeleteDraft {
  formId: string;
  successCallback?: () => void;
}
export const doOneTableNewOperatorDeleteDraft = async ({
  formId,
  successCallback,
}: IDoOneTableNewOperatorDeleteDraft) => {
  Modal.error({
    title: i18n.chain.proMicroModules.oneTable.confirmDeleteDraft.title,
    content: i18n.chain.proMicroModules.oneTable.confirmDeleteDraft.content,
    okText: i18n.chain.proMicroModules.oneTable.confirmDeleteDraft.okText,
    okCancel: true,
    okButtonProps: { danger: true },
    onOk: async () => {
      const { success } = await deleteFloworkFormAsync(formId);
      if (!success) return;
      successCallback?.();
    },
  });
};

// 删除流程
export const doOneTableNewOperatorDeleteWorkflow = async ({
  itemData,
  successCallback,
}: Required<IDoOneTableNewOperatorOptions>) => {
  Modal.error({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.confirmDeleteWorkFlow.title),
    content: i18n.chain.proMicroModules.oneTable.confirmDeleteWorkFlow.content,
    okText: i18n.chain.proMicroModules.oneTable.confirmDeleteWorkFlow.okText,
    okCancel: true,
    okButtonProps: { danger: true },
    onOk: async () => {
      const [resp1] = await Promise.allSettled([
        deleteWorkflowAsync(itemData.rootWfId, { params: { delete_linked_workflows: 'descendants' } }),
        deleteDatapkgAsync(itemData.pkgId, { quiet: true }),
        deleteFloworkFormAsync(itemData.formId, { quiet: true }),
      ]);
      resp1.status === 'fulfilled' && resp1.value.success && successCallback();
    },
  });
};

// 打开新建
export const doOneTableNewOperatorCreateReport = async (
  options: Pick<IOpenOneTableNewReportCreateOptions, 'formType' | 'fromPkgId' | 'onSuccessCb' | 'onCancelCb'>,
) => {
  openOneTableNewReportCreateDrawer(options);
};

// 立即发起
export const doOneTableNewOperatorPublish = async (options: IOpenOnetableNewPublishConfig) => {
  openOnetableNewPublishConfigModal(options);
};

// 复制新建
export const doOneTableNewOperatorCopyCreate = async (
  options: Pick<IOpenOneTableNewReportCreateOptions, 'formType' | 'fromFormId' | 'onSuccessCb' | 'onCancelCb'>,
) => {
  openOneTableNewReportCreateDrawer(options);
};

// 填报
export const doOneTableNewOperatorFillForm = async (options: IOpenOneTableNewFillFormOptions) => {
  openOneTableNewFillFormDrawer(options, { title: getDisplayFormName(options.itemData) });
};

// 下发
export const doOneTableNewOperatorIssueTask = async (options: IOpenOneTableNewIssuedFormOptions) => {
  openOneTableNewIssuedFormDrawer(options);
};

// 预览
export const doOneTableNewOperatorPreviewForm = async (options: IOpenOneTableNewPreviewFormOptions) => {
  openOneTableNewPreviewFormDrawer(options);
};

// 任务增援
export const doOneTableNewOperatorAddTaskCollaborate = async ({
  itemData,
  successCallback,
}: IDoOneTableNewOperatorOptions) => {
  openSelectOrgUsers({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.btnAddCollector),
    tip: i18n.chain.proMicroModules.oneTable.btnCollectorTooltip,
    tipType: 'info',
    formSpec: ONE_TABLE_INFO.reportConfig.selectedReinforceUsersSpec,
    rootWfId: itemData.rootWfId,
    successCallback,
    otherFormProps: { formData: { mode: 'append' } },
    doOneTableNewTask: async (data) => {
      return doOneTableNewExcuteCommands({
        root_workflow_id: itemData.rootWfId,
        commands: [
          { ...data, command: CommandEnum.REINFORCE },
          { ...data, command: CommandEnum.APPROVAL_REINFORCE, apply_all: true, ignore_if_empty: true },
        ],
      });
    },
  });
};

// 报表管理增援
export const doOneTableNewOperatorAddManageCollaborate = async ({
  itemData,
  successCallback,
}: IDoOneTableNewOperatorOptions) => {
  openSelectOrgUsers({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.btnAddCollector),
    tip: i18n.chain.proMicroModules.oneTable.btnCollectorTooltip2,
    tipType: 'info',
    formSpec: ONE_TABLE_INFO.reportConfig.selectedReinforceUsersSpec,
    rootWfId: itemData.rootWfId,
    successCallback,
    otherFormProps: { formData: { mode: 'append' } },
    doOneTableNewTask: async (data) => {
      return doOneTableNewExcuteCommands({
        root_workflow_id: itemData.rootWfId,
        commands: [
          { ...data, command: CommandEnum.MANAGE_REINFORCE },
          { ...data, command: CommandEnum.REINFORCE },
          { ...data, command: CommandEnum.APPROVAL_REINFORCE, apply_all: true, ignore_if_empty: true },
        ],
      });
    },
  });
};

interface IDoOneTableNewOperatorEditCollaborateOptions extends Required<IDoOneTableNewOperatorOptions> {
  users: number[]; // 现有的协同者
}
const checkEditSelectUsers = async (rootWfId: string, preUsers: number[], nowUsers: IOrgUserItem[]) => {
  const userMap = _.keyBy(preUsers);
  const existUserMap = await getOnetableRootDownstreamUsers(rootWfId);
  const addNewUsers = _.filter(nowUsers, (it) => !userMap[it.userId]);
  return checkUserExistsInMap(addNewUsers, existUserMap);
};
// 编辑协同(目前先强制替换)
export const doOneTableNewOperatorEditCollaborate = async ({
  itemData,
  users,
  successCallback,
}: IDoOneTableNewOperatorEditCollaborateOptions) => {
  openSelectOrgUsers({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.btnEditCollector),
    formSpec: ONE_TABLE_INFO.reportConfig.selectedReinforceUsersSpec,
    rootWfId: itemData.rootWfId,
    successCallback,
    otherFormProps: { formData: { orgUsers: users, mode: 'replace' } },
    checkSelectUsers: async (nowUsers: IOrgUserItem[]) => {
      return checkEditSelectUsers(itemData.rootWfId, users, nowUsers);
    },
    doOneTableNewTask: async (data) => {
      if (_.isEqual(_.sortBy(users), _.sortBy(data.users))) {
        toastApi.warning(i18n.chain.proMicroModules.oneTable.editCollectorEqualTip);
        return false;
      }
      // 删除的用户, 需要把数据权限转移
      const deleteUsers = _.difference(users, data.users);
      return modifyDatapkgAssignUser({
        itemData: itemData,
        sourceUserIds: deleteUsers,
        targetUserId: itemData.primaryAssignee, // 主负责人
        postCommands: {
          root_workflow_id: itemData.rootWfId,
          commands: [
            { ...data, command: CommandEnum.REINFORCE },
            { ...data, command: CommandEnum.APPROVAL_REINFORCE, apply_all: true, ignore_if_empty: true },
          ],
        },
      });
    },
  });
};

// 编辑协同(目前先强制替换)
export const doOneTableNewOperatorEditManageCollaborate = async ({
  itemData,
  users,
  successCallback,
}: IDoOneTableNewOperatorEditCollaborateOptions) => {
  openSelectOrgUsers({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.btnEditCollector),
    formSpec: ONE_TABLE_INFO.reportConfig.selectedReinforceUsersSpec,
    rootWfId: itemData.rootWfId,
    successCallback,
    otherFormProps: { formData: { orgUsers: users, mode: 'replace' } },
    checkSelectUsers: async (nowUsers: IOrgUserItem[]) => {
      return checkEditSelectUsers(itemData.rootWfId, users, nowUsers);
    },
    doOneTableNewTask: async (data) => {
      if (_.isEqual(_.sortBy(users), _.sortBy(data.users))) {
        toastApi.warning(i18n.chain.proMicroModules.oneTable.editCollectorEqualTip);
        return false;
      }
      // 删除的用户, 需要把数据权限转移
      const deleteUsers = _.difference(users, data.users);
      return modifyDatapkgAssignUser({
        itemData: itemData,
        sourceUserIds: deleteUsers,
        targetUserId: itemData.primaryAssignee, // 主负责人
        postCommands: {
          root_workflow_id: itemData.rootWfId,
          commands: [
            { ...data, command: CommandEnum.MANAGE_REINFORCE },
            { ...data, command: CommandEnum.REINFORCE },
            { ...data, command: CommandEnum.APPROVAL_REINFORCE, apply_all: true, ignore_if_empty: true },
          ],
        },
      });
    },
  });
};

// 需要将管理者及协同者填写的数据一并转移
interface IModifyDatapkgAssignUserOptions {
  itemData: IOneTableNewOperatorDataComm;
  sourceUserIds?: number[]; // 传递的数据源
  targetUserId?: number; // 需要赋值的用户Id
  withReinforce?: boolean; // 协助者是否一并修改
  withChild?: boolean; // 子流程是否一并修改
  queriedAssignWfId?: string; // 指定子流程
  postCommands?: IOnetableExecuteCommandsPost; // 批量执行
  doAction?: () => Promise<boolean>;
}
const modifyDatapkgAssignUser = async ({
  sourceUserIds,
  targetUserId,
  withReinforce,
  withChild,
  queriedAssignWfId,
  postCommands,
  doAction,
  itemData,
}: // eslint-disable-next-line sonarjs/cognitive-complexity
IModifyDatapkgAssignUserOptions) => {
  const { rootWfId, assignWfId, pkgId, isFormManageLevelUser } = itemData;
  let targetAssignUser = targetUserId || 0;
  let childrenUserIds: number[] = [];
  if (withChild) {
    const dwonResp = await queryOnetableInvolvedUsersAsync({
      root_workflow_id: rootWfId,
      only_downstream: true,
      assign_workflow_id: queriedAssignWfId,
    });
    if (!dwonResp.success) return false;
    childrenUserIds = getDownstreamUsers(dwonResp.data!);
  }
  // 如果外部传递了值，则使用外部的数据
  let orginUserIds: number[] = sourceUserIds || [];
  if (_.isNil(sourceUserIds) && assignWfId) {
    // 之所以重新获取一遍数据，是为了保证数据是新的, 防止界面打开期间, 协同者有更新
    const assignWfResp = await getWorkflowAsync(assignWfId, {
      params: { with_data: true },
    });
    if (!assignWfResp.success) return false;
    const wfData = assignWfResp.data!.data!;
    const pas = getPrimaryAssignee(wfData);
    const as = withReinforce ? getAssignees(wfData) : [];
    orginUserIds = _.uniq([pas, ...as]);
    !targetAssignUser && (targetAssignUser = wfData.primary_approval_assignee);
  }
  orginUserIds = _.uniq([...orginUserIds, ...childrenUserIds]);
  const needModify = orginUserIds.length && targetAssignUser;
  // 需要将数据包的AssignUser调整为targetUserId;
  if (needModify) {
    // 如果更新失败，只能通过手动修改数据库了
    const values: Record<string, any> = { [COLUMN_ASSIGN_USER]: targetAssignUser };
    // 这里会有风险，会导致部分数据状态错乱
    (isFormManageLevelUser || targetAssignUser === itemData.formOwner.userId) &&
      (values[COLUMN_STATUS] = BooleanEnum.True);
    const condition = getTransformDatapkgAssignUserFilter(orginUserIds);
    const { success } = await patchDatapkgRowsAsync(itemData.pkgId, {
      values,
      condition,
      row_permission_params: { permission_scope: 'all' },
    });
    if (!success) return false;
  }
  if (postCommands) {
    const commandResp = await doOneTableNewExcuteCommands(postCommands);
    if (commandResp) return true;
  }
  if (typeof doAction === 'function') {
    const actionResp = await doAction();
    if (actionResp) return true;
  }
  // 更新失败则还原assignUser
  if (needModify) {
    // 如果command执行失败, 对于取消下发流程, 无法还原，只能将所有数据重新赋值给取消下发人员
    const values = { [COLUMN_ASSIGN_USER]: orginUserIds[0] };
    const condition = getTransformDatapkgAssignUserFilter([targetAssignUser]);
    await patchDatapkgRowsAsync(
      pkgId,
      { values, condition, row_permission_params: { permission_scope: 'all' } },
      { quiet: true },
    );
  }
  return false;
};

// 转交管理
export const doOneTableNewOperatorTransferManage = async ({
  itemData,
  successCallback,
}: Required<IDoOneTableNewOperatorOptions>) => {
  openSelectOrgUsers({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.btnTransferForm),
    tip: i18n.chain.proMicroModules.oneTable.btnTransferFormTip2,
    formSpec: ONE_TABLE_INFO.reportConfig.selectedTransferUserSpec,
    rootWfId: itemData.rootWfId,
    successCallback,
    doOneTableNewTask: async (data) => {
      return modifyDatapkgAssignUser({
        itemData: itemData,
        targetUserId: data.users[0],
        withReinforce: data.clear_reinforce,
        postCommands: {
          root_workflow_id: itemData.rootWfId,
          commands: [
            { ...data, command: CommandEnum.MANAGE_TRANSFER },
            { ...data, command: CommandEnum.TRANSFER },
            { ...data, command: CommandEnum.APPROVAL_TRANSFER, apply_all: true, ignore_if_empty: true },
          ],
        },
      });
    },
  });
};

// 转交任务
export const doOneTableNewOperatorTransferTask = async ({
  itemData,
  successCallback,
}: Required<IDoOneTableNewOperatorOptions>) => {
  openSelectOrgUsers({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.btnForword3),
    tip: i18n.chain.proMicroModules.oneTable.btnForword4Tooltip,
    formSpec: ONE_TABLE_INFO.reportConfig.selectedTransferUserSpec,
    rootWfId: itemData.rootWfId,
    successCallback,
    doOneTableNewTask: async (data) => {
      return modifyDatapkgAssignUser({
        itemData: itemData,
        targetUserId: data.users[0],
        withReinforce: data.clear_reinforce,
        postCommands: {
          root_workflow_id: itemData.rootWfId,
          commands: [
            { ...data, command: CommandEnum.TRANSFER },
            { ...data, command: CommandEnum.APPROVAL_TRANSFER, apply_all: true, ignore_if_empty: true },
          ],
        },
      });
    },
  });
};

// 提交数据
export const doOneTableNewOperatorSubmitTask = async (options: IOpenOnetableNewSubmitConfigOptions) => {
  openOnetableNewSubmitConfigModal(options, {
    title: getDisplayFormName(options.itemData),
    width: isPc() ? '80vw' : '100vw',
  });
};

// 取消提交
export const doOneTableNewOperatorCancelSubmit = async ({
  itemData,
  successCallback,
}: Required<IDoOneTableNewOperatorOptions>) => {
  Modal.warn({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.confirmCancelSubmit.title),
    content: i18n.chain.proMicroModules.oneTable.confirmCancelSubmit.content,
    okText: i18n.chain.proMicroModules.oneTable.confirmCancelSubmit.okText,
    okCancel: true,
    okButtonProps: { danger: true },
    onOk: async () => {
      const success = await doOneTableNewExcuteCommands({
        root_workflow_id: itemData.rootWfId,
        commands: [{ command: CommandEnum.UNSUBMIT }],
      });
      if (!success) return;
      toastApi.success(i18n.chain.comTip.optSuccess);
      successCallback?.();
    },
  });
};

// 退回任务
export const doOneTableNewOperatorRefuseTask = async ({
  itemData,
  successCallback,
}: Required<IDoOneTableNewOperatorOptions>) => {
  ModalReason.warn({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.confirmRevokeTask.title),
    content: i18n.chain.proMicroModules.oneTable.btnRevokeTooltip,
    okText: i18n.chain.proMicroModules.oneTable.confirmRevokeTask.okText,
    okCancel: true,
    okButtonProps: { danger: true },
    reasonOptions: {
      component: 'Textarea',
      componentProps: {
        placeholder: i18n.chain.proMicroModules.oneTable.confirmRevokeTask.content,
      },
    },
    onOk: async (reason: string) => {
      const success = await modifyDatapkgAssignUser({
        itemData: itemData,
        withReinforce: true,
        withChild: true,
        postCommands: {
          root_workflow_id: itemData.rootWfId,
          commands: [{ command: CommandEnum.REFUSE, comment: reason }],
        },
      });
      if (!success) return;
      toastApi.success(i18n.chain.comTip.optSuccess);
      successCallback?.();
    },
  });
};

// 取消下发
export const doOneTableNewOperatorCancelTask = async ({
  itemData,
  successCallback,
}: Required<IDoOneTableNewOperatorOptions>) => {
  ModalReason.warn({
    title: getDisplayFormName(itemData, i18n.chain.proMicroModules.oneTable.confirmCancelTask.title),
    content: i18n.chain.proMicroModules.oneTable.cancelIssuedDesc,
    okText: i18n.chain.proMicroModules.oneTable.confirmCancelTask.okText,
    okCancel: true,
    okButtonProps: { danger: true },
    reasonOptions: {
      component: 'Textarea',
      componentProps: {
        placeholder: i18n.chain.proMicroModules.oneTable.confirmCancelTask.content,
      },
    },
    onOk: async (reason: string) => {
      const success = await modifyDatapkgAssignUser({
        itemData: itemData,
        withReinforce: true,
        withChild: true,
        queriedAssignWfId: itemData.assignWfId,
        doAction: async () => {
          const resp = await postWorkflowCancelAsync(itemData.assignWfId, {
            cancel_linked_workflows: 'descendants',
            success: false,
            cancel_comment: reason,
          });
          return resp.success;
        },
      });
      if (!success) return;
      toastApi.success(i18n.chain.comTip.optSuccess);
      successCallback?.();
    },
  });
};

// 批量填报
export const doOneTableNewOperatorBatchFillForm = async (options: IOpenBatchFillFormOptions) => {
  openBatchFillFormModal(options);
};

// 编辑流程配置
export const doOneTableNewOperatorEditSetting = async (options: IOpenOneTableNewReportSettingOptions) => {
  openOneTableNewReportSettingDrawer(options);
};

// 打开详情页面
export const doOneTableNewOperatorViewDetail = async (options: IOpenOneTableNewReportDetailOptions) => {
  openOneTableNewReportDetailDrawer(options);
};

// 打开详情h5
export const doOneTableNewOperatorViewDetailH5 = async (options: IOpenOneTableNewReportDetailH5Options) => {
  openOneTableNewReportDetailH5Drawer(options);
};

interface IOrgUserItem {
  orgId: number;
  userId: number;
}
export const checkRootDownstreamUsers = async (rootWfId: string, users: IOrgUserItem[]) => {
  const existUserMap = await getOnetableRootDownstreamUsers(rootWfId);
  return checkUserExistsInMap(users, existUserMap);
};

export const enum CommandEnum {
  REINFORCE = 'reinforce',
  TRANSFER = 'transfer',
  REFUSE = 'refuse',
  SUBMIT = 'submit',
  UNSUBMIT = 'unsubmit',
  APPROVE = 'approve',
  REJECT = 'reject',
  SEND_DOWN = 'send_down',
  APPROVAL_REINFORCE = 'approval_reinforce',
  APPROVAL_TRANSFER = 'approval_transfer',
  APPROVAL_UNSUBMIT = 'approval_unsubmit',
  MANAGE_REINFORCE = 'manage_reinforce',
  MANAGE_TRANSFER = 'manage_transfer',
}

export const doOneTableNewExcuteCommands = async (data: IOnetableExecuteCommandsPost) => {
  const resp = await excuteOnetableCommandsAsync(data);
  return !!resp.data?.success;
};

interface IOptions {
  rootWfId: string;
  title: string;
  formSpec: Record<string, any>;
  doOneTableNewTask: (data: Record<string, any>) => Promise<boolean>;
  successCallback?: () => void;
  tip?: string;
  tipType?: NoticeType;
  otherFormProps?: Record<string, any>;
  checkSelectUsers?: (orgUsers: IOrgUserItem[]) => Promise<boolean>;
}
const openSelectOrgUsers = ({
  formSpec,
  title,
  tip,
  tipType = 'warning',
  rootWfId,
  successCallback,
  checkSelectUsers,
  otherFormProps,
  doOneTableNewTask,
}: IOptions) => {
  const tipEle = tip ? (
    <ToastContainer style={{ maxWidth: 'unset' }} type={tipType} message={tip} withIcon={false} />
  ) : null;
  const titleDom = (
    <Space direction="vertical" style={{ width: '100%' }}>
      {title}
      {tipEle}
    </Space>
  );
  openFormView(
    { title: titleDom, width: isPc() ? '50%' : '100%' },
    {
      ...formSpec,
      ...otherFormProps,
      onSubmit: async (data: { orgUsers: IOrgUserItem[] }) => {
        const { orgUsers, ...resetData } = data;
        const existUser = checkSelectUsers
          ? await checkSelectUsers(orgUsers)
          : await checkRootDownstreamUsers(rootWfId, orgUsers);
        if (existUser) {
          return { success: false };
        }
        const success = await doOneTableNewTask({ ...resetData, users: _.map(orgUsers, 'userId') });
        if (!success) {
          return { success: false };
        }
        toastApi.success(i18n.chain.comTip.optSuccess);
        successCallback?.();
        return { success: true };
      },
    },
    true,
  );
};

// 深度遍历json schema，获取所有properties的key
export const collectPropertyKeys = (obj: any) => {
  let keys: string[] = [];

  const traverse = (currentObject: any) => {
    if (currentObject && typeof currentObject === 'object') {
      _.forOwn(currentObject, (value, key) => {
        if (key === 'properties') {
          keys = keys.concat(Object.keys(value));
        }
        traverse(value);
      });
    }
  };

  traverse(obj);
  return keys;
};
