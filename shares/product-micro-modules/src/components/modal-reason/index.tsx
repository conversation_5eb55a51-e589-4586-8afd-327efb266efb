import { FC } from 'react';
import { Flex } from '@metroDesign/flex';
import { type ModalFuncProps, Modal as OriginalModal } from '@metroDesign/modal';
import { useCreation } from 'ahooks';
import { Reason } from './Reason';
import { type IControllerOptions, ReasonController } from './ReasonController';

interface IModalOptions extends ModalFuncProps {
  reasonOptions?: IControllerOptions;
}

const ReasonContentView: FC<IControllerOptions & { onReady: (ctrl: ReasonController) => void }> = (options) => {
  const ctrl = useCreation(() => {
    const c = new ReasonController(options);
    options.onReady(c);
    return c;
  }, []);
  return <Reason controller={ctrl} />;
};

const createCustomModal = (originalMethod: (options: IModalOptions) => void) => {
  return (options: IModalOptions) => {
    const originalOnOk = options.onOk;
    let reasonController: ReasonController | undefined;

    const newOptions: IModalOptions = {
      ...options,
      destroyOnClose: true,
      content: (
        <Flex gap="middle" vertical>
          {options.content}
          <ReasonContentView
            {...(options.reasonOptions || {})}
            onReady={(ctrl) => {
              reasonController = ctrl;
            }}
          />
        </Flex>
      ),
      afterClose: () => {
        reasonController?.destroy();
        reasonController = undefined;
        options?.afterClose?.();
      },
      onOk: async (...args: any) => {
        const val$ = reasonController?.getValue$();
        const reason = val$?.getValue();
        const require = reasonController?.getRequire();

        if (require && reasonController?.getIsValidValue()) {
          Promise.reject(new Error(`Current value is :${reasonController?.getValue$().getValue()}, value is required`));
          val$?.next(reason ?? '');
          return;
        }
        await originalOnOk?.(reason, ...args);
      },
    };

    originalMethod(newOptions);
  };
};

const ModalReason = {
  warn: createCustomModal(OriginalModal.warn),
  info: createCustomModal(OriginalModal.info),
  success: createCustomModal(OriginalModal.success),
  error: createCustomModal(OriginalModal.error),
  confirm: createCustomModal(OriginalModal.confirm),
  open: createCustomModal(OriginalModal.open),
};

export { ModalReason };
