import { FC, useRef, useState } from 'react';
import { Setting } from '@metro/icons';
import { Drawer } from '@metroDesign/drawer';
import { Input } from '@metroDesign/input';
import i18n from '../../languages';
import { DrawGeometry } from './DrawGeometry';
import { PunchGeometry } from './PunchGeometry';
import './style.less';

export interface IGeometryInputProps {
  value: string;
  onChange: (value: string) => void;
  operationMode?: 'default' | 'punch';
  placeholder?: string;
  limit?: number;
  dataType?: 'ewkb' | 'bd09' | 'gcj02' | 'wgs84';
  geometryType?: 'Point' | 'LineString' | 'Polygon';
  valueType?: 'wkb' | 'coordinates' | 'address';
  disabled?: boolean;
  previewMode?: 'default' | 'map';
  previewMapWidth?: string;
  previewMapHeight?: string;
}

export const GeometryInput: FC<IGeometryInputProps> = (props) => {
  const {
    value,
    onChange,
    placeholder,
    operationMode,
    geometryType,
    valueType,
    disabled,
    previewMode,
    previewMapWidth,
    previewMapHeight,
    ...resetProps
  } = props;
  const [open, setOpen] = useState(false);
  const ref = useRef<any>();

  const openMap = () => {
    if (disabled) return;
    setOpen(true);
  };

  const closeMap = () => {
    setOpen(false);
  };

  const okButtonProps = {
    onClick: () => {
      onChange(ref.current.getValue() || '');
      closeMap();
    },
  };

  const cancelButtonProps = {
    onClick: closeMap,
  };

  const View = operationMode === 'punch' ? PunchGeometry : DrawGeometry;

  if (disabled && previewMode === 'map') {
    return (
      <div style={{ width: previewMapWidth, height: previewMapHeight }}>
        <View
          ref={ref}
          {...resetProps}
          value={value}
          geometryType={geometryType}
          valueType={valueType}
          disabled={true}
          preview={true}
        />
      </div>
    );
  }

  return (
    <div>
      <Input
        readOnly
        value={value}
        addonAfter={!disabled && <Setting onClick={openMap} />}
        placeholder={placeholder}
        disabled={disabled}
      />
      <Drawer
        title={i18n.chain.proMicroModules.formView.drawTitle}
        placement="right"
        width="90vw"
        maskClosable={false}
        destroyOnClose
        closable={false}
        onClose={closeMap}
        open={open}
        okButtonProps={okButtonProps}
        cancelButtonProps={cancelButtonProps}
        operatorsTarget="extra"
      >
        <View ref={ref} {...resetProps} value={value} geometryType={geometryType} valueType={valueType} />
      </Drawer>
    </div>
  );
};
