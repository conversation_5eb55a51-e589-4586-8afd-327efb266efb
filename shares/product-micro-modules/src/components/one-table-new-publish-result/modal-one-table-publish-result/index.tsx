import { type ModalProps, Modal } from '@metroDesign/modal';
import { IProps, OneTableNewPublishResult } from '../OneTableNewPublishResult';

export const openOnetableNewPublishResultModal = (
  childrenProps: Omit<IProps, 'onClose'>,
  modalProps?: Omit<ModalProps, 'children' | 'afterClose'>,
) => {
  Modal.open({
    closable: true,
    maskClosable: false,
    footer: null,
    ...(modalProps || {}),
    children: (close) => <OneTableNewPublishResult onClose={close} {...childrenProps} />,
  });
};
