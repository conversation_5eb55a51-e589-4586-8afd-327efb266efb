import _ from 'lodash';
import type { ItemType } from '@metroDesign/menu/hooks/useItems';
import { Tag } from '@metroDesign/tag';
import { toastApi } from '@metroDesign/toast';
import { RequestController } from '@mdtBsControllers/request-controller';
import { AbstractAppController } from '../../controllers/AbstractAppController';
import { SSO_LOGOUT_URL } from '../../datlas/datlasConfig';
import i18n from '../../languages';
import { IDentityView } from './IdentitySelect';
import { IIdentitySelectModel } from './IdentitySelectModel';

interface IControllerOptions {
  ssoUrl?: string;
  app: AbstractAppController;
  Model: IIdentitySelectModel;
  isDroopdown?: boolean;
  isMain?: boolean;
}

class IdentitySelectController extends RequestController {
  private Model: IIdentitySelectModel;
  private app: AbstractAppController;
  private ssoUrl: string;
  private isDroopdown: boolean;
  private isMain: boolean;
  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    this.ssoUrl = options.ssoUrl || SSO_LOGOUT_URL;
    this.isDroopdown = options.isDroopdown ?? true;
    this.app = options.app;
    this.isMain = options.isMain ?? false;
  }

  public destroy() {
    super.destroy();
    this.Model = undefined!;
    this.ssoUrl = undefined!;
    this.isDroopdown = undefined!;
    this.isMain = undefined!;
    this.app.destroy();
  }

  public getApp() {
    return this.app;
  }

  public getIsMain() {
    return this.isMain;
  }

  public getIsDropdown() {
    return this.isDroopdown;
  }

  public getMainIdentityKey(): string {
    const identities = this.app.getIdentities();
    const mainIdentity = _.find(identities, (identity) => _.get(identity, 'is_main', false));
    return _.get(mainIdentity, 'id', this.app.getUserUuid());
  }

  public generateIdentitiesData(): ItemType[] {
    const identities = this.app.getIdentities();

    const transformIdentity = (identity: any) => {
      const originalName = _.get(identity, 'nickname', '') || _.get(identity, 'name', '');
      const [name = '', orgName] = originalName.split('|');
      const defaultText = _.get(identity, 'is_main', false) ? i18n.chain.proMicroModules.header.default : '';
      const expiredLabel =
        defaultText + (_.get(identity, 'expired', false) ? `(${i18n.chain.proMicroModules.header.expired})` : '');

      return {
        appName: _.get(identity, 'app_name', ''),
        isMain: _.get(identity, 'is_main', false),
        data: {
          label: <IDentityView name={name + expiredLabel} orgName={orgName} />,
          key: _.get(identity, 'id', ''),
          onClick: (info: any) => {
            this.changeInditity(info.key);
          },
          disabled: !_.get(identity, 'enable', false) || _.get(identity, 'expired', false),
        },
      };
    };

    const transformedIdentities = _.map(identities, transformIdentity);

    const mainIdentities = _.filter(transformedIdentities, (item) => item.isMain);

    const appGroups = _.groupBy(
      _.filter(transformedIdentities, (item) => !item.isMain),
      'appName',
    );

    const result: ItemType[] = [];

    if (!_.isEmpty(mainIdentities)) {
      const mainIdentitiesByApp = _.groupBy(mainIdentities, 'appName');

      _.forEach(mainIdentitiesByApp, (appIdentities, appName) => {
        if (!_.isEmpty(appIdentities)) {
          const mainGroup = {
            type: 'group' as const,
            label: <Tag color="processing">{appName}</Tag>,
            children: _.map(appIdentities, (item: any) => item.data),
          };
          result.push(mainGroup);
        }
      });
    }

    _.forEach(appGroups, (identities, appName) => {
      if (!_.isEmpty(identities)) {
        const appGroup = {
          type: 'group' as const,
          label: <Tag color="processing">{appName}</Tag>,
          children: _.map(identities, (item: any) => item.data),
        };
        result.push(appGroup);
      }
    });

    return result;
  }

  // 切换身份
  public async changeInditity(uuid: string) {
    const currentId = this.isMain ? this.getMainIdentityKey() : this.app.getUserUuid();
    if (uuid === currentId) {
      return;
    }

    if (this.isMain) {
      const { success } = await this.Model.putUserMainInditity(uuid).toPromise();
      if (success) {
        toastApi.success(i18n.chain.proMicroModules.header.switchMainIdentitySuccess);
        window.location.reload();
      }
      return;
    }

    const val = await this.Model.postUserInditity(uuid).toPromise();
    if (val.success) {
      // 登录一致性：切换app使原有界面的token失效, 并刷新sso的存储token
      this.app.refreshSsoToken(this.ssoUrl, val.data!.auth);
    }
  }
}

export { IdentitySelectController };
