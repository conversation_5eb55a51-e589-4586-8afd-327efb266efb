import { cn as proTasksCn } from '@mdtProTasks/languages';

export const cn = {
  ...proTasksCn,
  proMicroModules: {
    userInactivityLogout: (time: number) => `您的账号在 ${time} 分钟内未进行任何操作，即将退出登录`,
    crontab: {
      cronExp: 'Cron表达式',
      hot: '热门常用',
      custom: '自定义',
      exampl1: '每30秒执行一次',
      exampl2: '每5分钟执行一次',
      exampl3: '每10小时执行一次',
      exampl4: '每天凌晨1点执行一次',
      exampl5: '每天中午12点执行一次',
      exampl6: '每月最后一天23点执行一次',
      exampl7: '每周星期天凌晨1点执行一次',
      onetable: {
        exampl1: '每天的00:00:00执行',
        exampl2: '每周日的00:00:00执行',
        exampl3: '每月1号的00:00:00执行',
        exampl4: '每月的最后一天的00:00:00执行',
        exampl5: '每季度第一个月的第一天的00:00:00执行',
        exampl6: '1月1号和7月1号的00:00:00执行',
        exampl7: '每年1月1号的00:00:00执行',
      },
      simpleSetting: '快捷设置',
      highSetting: '高级设置',
      notSimpleExp: '它不是由快捷方式设置生成的，无法正确解析',
      notHighExp: '它不是由高级方式设置生成的，无法正确解析',
      viewExecuteBtn: '查看执行时间',
      viewLastTime: '最近5次的执行时间',
      language: {
        // 面板标题,
        // panel title,
        paneTitle: {
          second: '秒',
          minute: '分',
          hour: '时',
          day: '日',
          month: '月',
          week: '周',
          year: '年',
        },
        // assign  指定
        assign: '指定',
        // Don't assign  不指定
        donTAssign: '不指定',
        // btn text
        okBtnText: '生成',
        // Every minute ...   每一秒钟、每一分钟
        everyTime: {
          second: '每一秒钟',
          minute: '每一分钟',
          hour: '每一小时',
          day: '每一日',
          month: '每一月',
          week: '每一周',
          year: '每年',
        },
        // weel option  周选项
        week: {
          sun: '星期日',
          mon: '星期一',
          tue: '星期二',
          wed: '星期三',
          thu: '星期四',
          fri: '星期五',
          sat: '星期六',
        },
        // from [a] to [b] [unit], executed once [unit]    a 到 b 每一个时间单位执行一次
        aTob: {
          second: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}秒，每秒执行一次
            </span>
          ),
          minute: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}分，每分钟执行一次
            </span>
          ),
          hour: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}时，每小时执行一次
            </span>
          ),
          day: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}日，每日执行一次
            </span>
          ),
          month: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}月，每月执行一次
            </span>
          ),
          week: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}，每星期执行一次
            </span>
          ),
          year: (AInput: string, BInput: string) => (
            <span>
              周期 从{AInput}-{BInput}年，每年执行一次
            </span>
          ),
        },
        // from [a] [unit] start, every [b] Execute once [unit]   从 a 开始, 每一个时间单位执行一次
        aStartTob: {
          second: (AInput: string, BInput: string) => (
            <span>
              按照 从{AInput}秒开始，每{BInput}秒执行一次
            </span>
          ),
          minute: (AInput: string, BInput: string) => (
            <span>
              按照 从{AInput}分开始，每{BInput}分执行一次
            </span>
          ),
          hour: (AInput: string, BInput: string) => (
            <span>
              按照 从{AInput}时开始，每{BInput}小时执行一次
            </span>
          ),
          day: (AInput: string, BInput: string) => (
            <span>
              按照 从{AInput}日开始，每{BInput}日执行一次
            </span>
          ),
          workday: (AInput: string) => <span>工作日 每月{AInput}号最近的工作日执行一次</span>,
          lastday: () => <span>按照 本月最后一日执行一次</span>,
          month: (AInput: string, BInput: string) => (
            <span>
              按照 从{AInput}月开始，每{BInput}月执行一次
            </span>
          ),

          // [n] in the NTH week of this month    本月第 n 周的 星期[n] 执行一次
          week: (AInput: string, BInput: string) => (
            <span>
              按照 本月第{AInput}周的{BInput}执行一次
            </span>
          ),

          // 本月的最后一个 星期[n] 执行一次
          week2: (AInput: string) => <span>按照 本月的最后一个{AInput}执行一次</span>,

          year: (AInput: string, BInput: string) => (
            <span>
              按照 从{AInput}年开始，每{BInput}年执行一次
            </span>
          ),
        },
      },
    },
    customLogin: {
      createTemplate: '新建模板',
      createTitleDesc: '自定义登录模板名称',
      createDropTitle: '点击活拖拽【登录配置项】到此区域',
      createDropDesc: (accept: string) => `支持${accept}文件格式`,
      createDefault: '设为默认登录配置',
      defaultTemplateName: '自定义模板',
      giveupTitle: '确认放弃?',
      giveupDesc: '放弃后，你的修改将无法恢复',
      titleRequired: '需要定义模板名称',
      commonConfigTab: '通用配置',
      deployWindowTitle: '网页名称',
      deployWindowDesc: '网页描述',
      deployFavicon: '网页图标',
      productTitle: '标题',
      productDesc: '描述',
      productFooterVisible: '页脚',
      customLogoRadioTitle: '显示Logo',
      defaultLogo: '默认Logo',
      customLogo: '自定义Logo',
      productShowLanguage: '支持切换语言',
      languageSimplifiedChinese: '简体中文',
      languageEnglish: 'English',
      productTabsTitle: '登录方式',
      loginMethodAccount: '账号登录',
      loginMethodPhone: '短信登录',
      loginMethodWechat: '微信登录',
      loginMethodDingtalk: '钉钉登录',
      loginMethodZZDing: '浙政钉',
      productPrivacyVisibleCheckedText: '隐私协议打开',
      productPrivacyVisibleUncheckedText: '隐私协议关闭',
      productPrivacyCheckChildren: '默认勾选',
      productPrivacyNameTitle: '协议标题',
      productPrivacyLinkTitle: '跳转链接',
      productPrivacyAdditionTitle: '新增隐私协议',
      productShowPasswordForgetTitle: '忘记密码',
      productShowPasswordForgetDesc: '可通过手机号或邮箱找回密码',
      productRegisterVisibleTitle: '注册按钮',
      productRegisterVisibleDesc: '开启后可以配置注册跳转链接',
      productRegisterLinkTitle: '注册跳转地址',
      productRegisterJumpTargetTitle: '跳转方式',
      productRegisterJumpTargetSelf: '系统内跳转',
      productRegisterJumpTargetBlank: '打开新页面',
      pageStyleTab: '界面样式',
      productThemeTitle: '主题色',
      lightTheme: '浅色主题',
      darkTheme: '深色主题',
      displayLayoutTitle: '布局方式',
      layoutLeft: '登录框居左',
      layoutRight: '登录框居右',
      layoutCenter: '登录框居中',
      loginBgStyleTitle: '登录框样式配置',
      productBgIsVideoTitle: '视频播放',
      productBgIsVideoDesc: '视频大小不能超过1MB',
      productBgUrlTitle: '背景图',
      productTitleCenterTitle: '标题样式',
      alignLeft: '向左对齐',
      alignCenter: '居中对齐',
      productTitleSizeTitle: '标题字号',
      customGridMediaRadioTitle: '素材框背景图',
      defaultBackground: '默认背景',
      customBackground: '自定义背景',
      productGridIsVideoTitle: '视频播放',
      productGridIsVideoDesc: '视频大小不能超过1MB',
      advancedConfigTab: '高级配置',
      productVerificationTitle: '滑动认证开启',
      productVerificationDesc: '按住鼠标拖动滑块，登录认证更加安全',
      productChooseApp: '选择App',
      productChooseAppDesc: '登录系统后，选择任一有权限的App进入',
      productRedirectUrlTitle: '指定登录成功跳转地址',
      productRedirectUrlPlaceholder: '默认使用sso的redirect参数',
    },
    card: {
      searchTag: '搜索标签',
      delTags: '删除标签组',
      createTag: '新建标签',
      createTagSuccess: '新建标签成功',
      delTagSuccess: '标签删除成功',
    },
    products: {
      myData: '我的数据（通用）',
      org: '机构管理（通用）',
      other: '其他通用模块',
      datlas: '地图轻应用',
      workflow: '流程引擎',
      datamarket: '数据市场',
      datafactory: '数据工厂',
      onetable: '一表通',
    },
    header: {
      workBench: '工作台',
      helpCenter: '帮助中心',
      settings: '个人设置',
      goAdmin: '进入后台',
      impersonate: '模拟登录',
      exitImpersonate: '退出模拟登录',
      changeImpersonateUser: '切换模拟登录账号',
      imperError: '当前模拟APP不可用或已到期',
      orderLastest: '最近模拟登录',
      orderDefault: '默认排序',
      starSub: '星标置顶',
      people: '人',
      logout: '退出',
      expired: '已过期',
      clearCacheSuccess: '缓存已清理',
      appForCloud: (product: string, appName: string) => `使用【${product}】账号从【${appName}】申请数据`,
      preferenceSetting: '偏好设置',
      notificationSetting: '通知设置',
      notificationSettingDesc: '用于管理系统内置的通知事件的状态，通知方式',
      themeName: '主题色',
      dark: '深色主题',
      light: '浅色主题',
      systemLanguage: '系统语言',
      loginSetting: '登录配置',
      otherSetting: '其他配置',
      autoLoginApp: '自动登录最近使用APP',
      autoLoginIdentity: '自动登录默认身份',
      autoLoginIdentitySwitchMainIdentity: '切换身份同时指定默认身份',
      switchMainIdentity: '切换默认身份',
      disableServiceWorker: '禁用Service Worker',
      disableServiceWorkerDesc: '禁用后，可能会影响使用体验。',
      notificationType: '通知方式',
      notificationTypeIn: '站内信',
      notificationTypeEmail: '邮件',
      readed: '已读',
      del: '删除',
      allNotifications: '全部消息',
      readedNotifications: '已读消息',
      unreadNotifications: '未读消息',
      allReaded: '全部已读',
      allDel: '全部删除',
      personal: '个人资料',
      username: '用户名',
      nickname: '昵称',
      changeIdentities: '切换身份',
      systemNotificationEvent: '系统通知事件',
      status: '状态',
      statusOn: '开启',
      statusOff: '关闭',
      notificationCustom: '用户关注事件',
      notificationExpired: '个人账户过期提醒',
      notificationApproval: '数据审批结果',
      notificationResource: '资源授权通知',
      notificationFlow: '流程待处理',
      org: '所属机构',
      phone: '手机号',
      email: '邮箱号',
      password: '密码',
      dingtalk: '钉钉',
      wechat: '微信',
      copyId: '复制ID',
      change: '更改',
      verify: '去验证',
      noVerify: '（未验证）',
      bind: '绑定',
      unBind: '解绑',
      unBindConfirm: '确定要解除绑定吗？',
      unBindSuccess: '解除绑定成功',
      copyIdSuccess: '机构ID已复制到剪贴板',
      role: '角色',
      searchApp: '搜索APP',
      changeThemeSuccess: '切换主题色成功',
      autoLoginSuccess: '修改自动登录配置成功',
      editServerWorkerSuccess: '修改Service Worker状态成功，刷新页面生效',
      changeLanguageSuccess: '切换语言成功',
      notificationNomal: '普通通知',
      notificationSystem: '系统通知',
      notificationTask: '后台任务通知',
      notificationSubscribe: '订阅通知',
      notificationAnnouncement: '公告通知',
      productList: '脉策产品列表',
      default: '(默认)',
      switchMainIdentitySuccess: '设置主身份成功',
    },
    theme: {
      name: '主题库',
      delTagDesc: '若用户已设置仅部分主题库可见，删除主题库标签后，用户将无法查看该主题库中的数据。',
      themeName: '主题库名称',
      pkgStats: '数据包数量',
      search: '搜索主题库',
      create: '新建主题库',
    },
    taggroup: {
      name: '标签组',
      tag: '标签',
      tagName: '标签名称',
      create: '新建标签组',
      update: '修改标签组',
      createTip: '可通过空格间隔，批量添加',
      taggroupName: '标签组名称',
      delDesc: '删除后，标签组内的标签会同时被删除。',
      searchTag: '搜索标签',
      addTagDesc: '可通过空格间隔，批量添加标签',
      dataTag: '数据标签',
    },
    mdTmpl: {
      name: '数据包文档',
      tmplName: '名称',
      tmplType: '格式',
      updateTime: '最近更新时间',
      update: '修改文档',
      assign: '分配',
      preview: '预览',
      tmplPreview: '模版预览',
      copyId: '复制文档Id',
      delete: '删除模版',
      tmplContent: '模版内容',
      selectPkg: '选择数据包',
      source: '来源',
      pkg: '数据包',
      assignField: '分配字段',
      pkgDesc: '数据包说明',
      upload: '导入文件',
      assignSuccess: '分配成功',
      theme: '按主题库',
    },
    approval: {
      selpApp: '本机构',
      crossApp: '跨机构',
      appData: '机构数据',
      use: '使用',
      search: '检索',
      download: '下载',
      publish: '发布',
      toApproval: '待审批',
      finished: '已完成',
      searchApply: '搜索申请',
      pkgName: '数据包名称',
      applier: '申请人',
      applyDate: '申请时间',
      permission: '权限类型',
      approver: '审批人',
      status: '状态',
      applyApp: '申请机构',
      expireDate: '有效期',
      approved: '已通过',
      rejected: '已拒绝',
      cancelled: '已取消',
      other: '其它',
      expireTitle: '下载有效期设置',
      expireDesc: '截止日期',
      downloadTimeOutsid: '可供下载时间必须大于五分钟',
      applyFinished: (name: string) => `已完成"${name}"的申请审批`,
    },
    dataSearch: {
      allData: '全部数据',
      selfApp: '当前机构',
      byName: '按名称',
      byTag: '按标签',
      byField: '按字段',
      search: '搜索数据包',
      dataTag: '数据标签',
      searchTag: '搜索标签',
      dataPermission: '数据权限',
      dataGeoType: '数据地理类型',
      pkg: '数据包',
      dataType: '数据类型',
      dataTypeEmptyError: '数据类型不能为空',
      permission: '权限',
      updateDate: '更新时间',
      count: '数据量(条)',
      mutiGeoType: '多地理类型',
      searchBoxPlachode: '搜索你想要的数据包',
      searchBoxHistory: '历史搜索',
      searchBoxTotal: (total: number) => `共${total}条结果`,
      searchBoxDescEmpty: '暂无说明',
      searchBoxColums: '字段',
      searchBoxSetting: '搜索设置',
      searchBoxSettingTitle: '搜索范围',
      searchBoxSettingTheme: '主题库',
      searchBoxSettingApp: '机构',
      searchBoxSettingSubmitText: '保存设置',
      searchBoxSettingResetText: '恢复默认',
    },
    // 数据市场数据包详情
    pkgDetail: {
      apply: '数据申请',
      count: '数据量',
      updateDate: '上次更新',
      basic: '基本概览',
      tablePreview: '表格预览',
      field: '字段属性',
      desc: '数据包说明',
      genelogy: '血缘图',
      use: '使用权限',
      search: '检索权限',
      download: '下载权限',
      applyBtn: '申请',
      aggined: '已获取',
      share: '公开链接分享',
      shareBtn: '复制链接',
    },
    orgList: {
      searchOrg: '部门搜索',
      createChildOrg: '新建子部门',
      addOrg: '新建部门',
      orgManage: '部门管理',
      addChildOrg: '添加子部门',
      delOrg: '删除部门',
      orgAuth: '部门权限',
      orgResource: '部门资源',
      authManage: '权限设置',
      resourceManage: '资源设置',
      orgName: '部门名称',
      orgNameDesc: '部门名称不能为空',
      pOrg: '上级部门',
      delConfirm: '删除确认',
      delConfirmText: '删除部门将会删除该部门及下属部门，请确认是否删除？',
      createOrgSuccess: '新建部门成功',
      delOrgSuccess: '删除部门成功',
      editOrgSuccess: '更新部门成功',
      importOrg: '导入部门',
      importUser: '导入用户',
      downloadImportTemp: '下载导入模板',
      downloadImportTempTitle: '您可以下载',
      downloadImportTempDesc: '空的表格模板',
      downloadImportTempDesc2: '，填写部门信息后上传以批量新建部门',
      dropTitle: '点击或拖拽文件到此区域',
      dropDesc: '下载模板并完善信息后，可直接将文件上传，支持.xlsx格式文件',
      userDropTitle: '根据提示信息完善表格内容',
      userDropDesc: '下载空的模板表格',
    },
    roleList: {
      createRole: '新建角色',
      manageRole: '管理角色',
      delRole: '删除角色',
      empty: '暂无角色',
      removeRole: '移出角色',
      addRoleUser: '添加角色成员',
      removeConfirm: (user: string) => `确认删除"${user}"用户移出该角色？`,
      removeConfirmDesc: '移出后，用户将不再属于该角色',
      delConfirm: '删除确认',
      delConfirmDesc: '请确认是否删除该角色',
      editRoleSuccess: '更新角色成功',
      createRoleSuccess: '新建角色成功',
      delRoleSuccess: '删除角色成功',
      editRoleAuthSuccess: '更新角色权限成功',
    },
    permission: {
      basic: '基本信息',
      authManage: '权限设置',
      editError: '编辑出现了意外情况, 无法继续进行，请尝试刷新下浏览器',
      editUser: '编辑用户',
      createUser: '新建用户',
      username: '用户名',
      userNameEmptyError: '用户名不能为空',
      emailDesc: '邮箱（初始密码将通过邮箱发送，建议填写）',
      emailEmptyError: '邮箱不能为空',
      emailInvalid: '邮箱格式错误：<EMAIL>',
      phone: '手机号',
      fromOrgs: '所属部门',
      fromRoles: '所属角色',
      fromRolesDesc: '可以选择一个或多个角色',
      expireTime: '过期时间',
      expireTimeEmptyError: '过期时间不能为空',
      isFreeze: '是否禁用',
      freeze: '禁用',
    },
    resetPwd: {
      forceResetPwd: '强制修改密码',
      newPwd: '新密码',
      pwdReg: '至少8个字符，至少包含大写字母、小写字母、数字、特殊字符其中三类',
      pwdConfirm: '确认新密码',
      pwdError: '密码少于8个字符或字符种类少于三类',
      pwdDiff: '两次密码输入不一致',
      placeholder: '请输入',
    },
    role: {
      roleManage: '管理角色',
      roleCreate: '新建角色',
      roleName: '角色名称',
      roleNameEmptyError: '角色名不能为空',
      roleDesc: '角色描述',
      roleAuth: '角色权限',
      authSetting: '权限设置',
      roleResource: '角色资源',
      resourceSetting: '资源设置',
      editRole: '编辑角色',
      group: {
        groupManage: '群组管理',
        groupCreate: '新建群组',
        groupName: '群组名称',
        groupNameEmptyError: '群组名称不能为空',
        groupDesc: '群组描述',
        userName: '用户名',
      },
    },
    defaultPlay: {
      title: '默认播放设置',
      page: '默认播放页',
      add: '添加',
      chooseTitle: '选择默认播放页',
      project: '项目',
      reset: '重置',
      all: '全部',
      pages: '页面',
      searchByProj: (value: string) => `按项目搜索 "${value}"`,
      searchByPage: (value: string) => `按页面搜索 "${value}"`,
    },
    themeShare: {
      editUser: '编辑用户',
      username: '用户名',
      usernameEmptyError: '用户名不能为空',
      editTheme: '编辑主题库',
      themeVisible: '可见主题库',
      editOrgs: '编辑机构',
      orgsVisible: '可见机构',
      editDetail: '编辑详情',
    },
    genealogy: {
      delBorder: '删除边',
      delDom: '删除节点',
      arrange: '整理',
      scaleScreen: '缩放到适应屏幕',
      switchSelection: '开启/关闭框选',
      searchDatapkg: '搜索数据包',
      saveSuccess: '保存成功',
      noChange: '无修改',
      inputPile: '输入桩',
      outputPile: '输入桩',
      dataPreview: '地理数据预览',
      edit: '编辑血缘',
      delDomOrBorder: '删除 节点 或者 边',
    },
    markdownPreview: {
      editDesc: '编辑说明',
      fileUploadFailed: (name: string) => `文件${name}上传失败`,
    },
    timeTask: {
      delSuccess: '删除成功',
      operatorSucces: '操作成功',
      name: '名称',
      timingWay: '定时方式',
      expired: '有效期至',
      nextRunTime: '下次运行时间',
      avtive: '启用',
      delConfirm: (name?: string) => `确认删除"${name}"?`,
      delComfirmDesc: '删除后，定时任务内的标签会同时被删除。',
      addTiming: '新增定时',
      timingJob: '定时任务',
      updateTimingSuccess: '更新定时任务成功',
      createTimingSuccess: '新建定时任务成功',
      createTiming: '新建定时任务',
      editTiming: '修改定时任务',
      nailTime: '固定间隔',
      nailDate: '固定日期',
      interval: '间隔',
      numberPlaceholder: '请输入数值',
      frequency: '频率',
      time: '时刻',
      timePlaceholder: '请选择时刻',
      chooseWeek: '每周选择',
      chooseMonth: '每月选择',
      nameEmptyError: '名称不能为空',
      namePlaceholder: '请输入名称',
      startEndDate: '起止日期',
    },
    datapkg: {
      createZoneTip: '从本地复制文件，点击窗口或拖拽文件到此区域',
      createZoneDesc: '支持.csv/.xlsx(单Sheet)上传',
      emptyDatapkg: '空数据包',
      qe: 'QE建表',
      tableExtraction: '整表抽取',
      sql: 'SQL建表',
      createPkg: '新建数据包',
      createEmptyPkg: '新建空数据包',
      limitFileSize: (size: string) => `文件大小不能超过${size}M`,
      create: '生成',
      update: '更新',
      operateMessage: (name: string, operate: string) => `数据包【${name}】${operate}成功`,
      viewPkg: '查看数据包',
      dataPreview: '数据预览',
      dataSearch: '数据搜索',
      blurSearchTip: '支持文本列模糊搜索',
      geoPreview: '地理预览',
      noSearchDesc: '您暂无检索权限，仅提供最多前10条数据预览',
      saving: '保存中...',
      addField: '添加字段',
      edit: '编辑',
      editFieldDesc: '修改字段类型可能造成对已有的数据不兼容，造成数据无法正常使用甚至被锁死。请谨慎修改！',
      keyNoRepeat: '关键列不能重复',
      name: '字段名称',
      nameEmptyError: '字段名称不能为空',
      type: '字段类型',
      typeEmptyError: '字段类型不能为空',
      displayFormatter: '展示格式',
      unique: '列值唯一',
      nullable: '允许空值',
      index: '普通索引',
      keyType: '关键字段类型',
      nameDesc: '字段描述',
      updatePkg: '更新数据包',
      generatePkg: '生成数据包',
      chooseGeoType: '请选择地理类型',
      chooseDataType: '请选择数据类型',
      datapkgName: '数据包名称',
      datapkgNameDesc: '请输入数据包名称',
      datapkgNameEmptyError: '数据包名称不能为空',
      schemaEmptyError: 'Schema不能为空',
      tableEmptyError: 'Table不能为空',
      addFormDataset: '从数据源新增',
      refreshTimer: '定时刷新数据包缓存',
      nextRefreshTime: '下次刷新缓存时间',
      nextRefreshTimeTip: '如果不指定则自动根据[定时刷新数据包缓存]配置和当前时间进行推算',
      lastRefreshTime: '最近一次刷新缓存时间',
      datapkgExist: '同样名称的数据包已经存在，请更换数据包名称再尝试生成数据包。',
      cleatFilter: '清空条件',
      noAuth: '无权限',
      typeDesc: '类型描述',
      tableEdit: '表格编辑',
      mapEdit: '地图编辑',
      geoTypeDesc: '该数据为非地理数据，需要切换为地理数据才可进入编辑页面。',
      validateError: '主题库与机构都需至少保留一个选项',
      share: {
        pkgEmpty: '在左侧选择要分享的数据包',
        selectedInputPlaceholder: '搜索已选数据',
        selected: '已选',
        clear: '清空',
        startSharing: '开始共享',
        shareApplyTitle: '已发起外部共享审批',
        shareApplyTitleDesc: (count: number) => `已发起“{${count}}”个数据包的外部共享审批，请等待管理员处理。`,
        followOrgDate: '跟随机构有效期',
        custom: '自定义',
        shareSetting: '共享设置',
        endDate: '结束时刻',
        endDatePlaceholder: '请选择结束时刻',
        otherApp: '外部机构',
        otherAppPlaceholder: '请选择外部机构',
        applyAuth: '申请权限',
        applyAuthPlaceholder: '请选择申请权限',
        date: '有效期',
        dateEmptyError: '请选择有效期',
        applyConfirm: '申请权限确认',
        authEmptyTip: '权限不能为空',
        btnConfirmText: '确认',
        permissionAcquired: '外部机构已拥有的权限',
        permissionToApproval: '待审批的权限',
        permissionToApprovaling: '待申请的权限',
        permissionToApprovalingTip: '权限申请将仅限于已选中的项，未被选中的权限不会触发申请',
        applyDesc: '申请数据包权限',
        applyResult: '申请结果',
        applySuccess: (pkg: string, ps: string) => `数据包"${pkg}"的【${ps}】权限申请成功`,
        applyFaild: (pkg: string, ps: string) => `数据包"${pkg}"的【${ps}】权限申请失败`,
        appDiffTip: '当前机构和你选择的外部机构不一致',
        useCurrentBtn: '使用当前机构',
      },
      datapkgSql: {
        play: '运行',
        format: '格式化',
        noDataset: '没有数据源？',
        noDatasetTip: '联系系统管理员帮你开通自定义数据源菜单或者由他添加数据源',
        toCreate: '去新增',
        otherDataSet: '外链数据源',
        ownership: '属性',
        ownershipEmptyError: '属性不能为空',
        dataset: '数据源',
        datasetEmptyError: '数据源不能为空',
        geoType: '地理类型',
        geoTypeEmptyError: '地理类型不能为空',
        previewLoading: '在生成预览...',
        chooseDatasetEmpty: '请先在左侧选择数据源',
        edit: 'SQL编辑',
        confirmEdit: '确认修改',
        createDb: 'SQL建表',
        createError: '请先运行sql',
        emptyError: '请先编写Sql语句',
        pkgExistError: '数据包已经存在',
      },
      description: {
        title: '数据包说明修改',
        customDesc: '自定义说明',
        chooseTemp: '选择模板',
        desc: '说明',
        contentEmptyError: '内容不能为空',
      },
      download: {
        exportResult: '仅下载筛选结果',
        exportGeo: '导出地理字段',
        startDownload: '开始下载',
        datapkgDownload: '下载数据',
        columnTips: '导出的列（不选导出所有列）',
      },
      subscribe: {
        subscribeNotification: '订阅通知',
        datapkgUpdate: '数据包更新',
        qualityMonitorTaskResult: '质量监控任务执行结果',
        subscribeAll: '订阅全部',
        success: '订阅成功',
        cancelSuccess: '已取消订阅',
      },
    },
    datapkgDetail: {
      applyingPublish: '发布申请中',
      publishWithPkg: '发布为机构数据包',
      updateData: '更新数据',
      delData: '删除数据',
      fillData: '数据填报',
      basicInfo: '基本信息',
      emptyPkgError: '数据包不存在或其他原因导致无法请求到数据',
      pkgLoading: '数据包详情加载中',
      nameUpdateFailed: '名称更新失败',
      nameUpdateSuccess: '名称更新成功',
      updateFailed: '元信息更新失败',
      updateSuccess: '元信息更新成功',
      personalDataToOrg: '确认要将个人数据发布为机构数据吗？',
      personalDataToOrgDesc: '个人数据转换为机构数据后，将无法再次变为个人数据。',
      submitApplay: '申请已提交',
      dataUpdateFailed: '数据更新失败',
      dataUpdateSuccess: '数据已更新',
      delPkg: (name: string) => `数据包"${name}"已删除成功`,
      delPkgConfirm: (name?: string) => `确认删除"${name}"数据包`,
      delPkgConfirmDesc: '删除后，可能会导致使用此数据制作的图表、地图、Lab文件、低码ETL文件失效。',
      tablePreview: '表格预览',
      filedPreview: '字段属性',
      pkgDesc: '数据包说明',
      pkgMonitor: '质量监控',
      collaborateEdit: '协作编辑',
      fieldDesensitize: '字段脱敏',
      genealogy: '血缘关系',
      theme: '主题库',
      userName: '负责人',
      description: '描述',
      orgName: '机构名称',
      geoType: '地理类型',
      hasPermission: '已有权限',
      tags: '标签',
      count: '行数',
      columnCount: '字段(列)数',
      checkLog: '质检结果',
      updateTime: '更新时间',
      createTime: '创建时间',
      uuid: 'uuid',
      dataset: '数据源',
      storageType: '存储类型',
      tableName: '表名',
      displayStatus: '是否上架',
      display: '已上架',
      unDisplay: '未上架',
      copy: '复制',
      copySuccess: '复制成功',
      startPos: '起始位置：',
      desensitizeLength: '脱敏长度：',
      reg: '正则表达式：',
      desensitizeStyle: '脱敏样式：',
      qualified: '合格',
      unqualified: '不合格',
    },
    user: {
      privateSetting: '个人设置',
      active: '可用',
      disable: '禁用',
      expired: '(已过期)',
      noExpire: '无期限',
      verify: '验证',
      bind: '绑定',
      email: '邮箱号',
      phone: '手机号',
      captchaEmptyError: '验证码不能为空',
      captchaPlaceholder: '请输入验证码',
      sendCaptcha: '发送验证码',
      reSendCaptcha: (s: number) => `${s}s重新获取`,
      sendTo: `验证码已发送至：`,
      cannotReceive: '收不到验证码？',
      use: '使用',
      emptyError: '不能为空',
      formatError: '格式错误',
      enterRight: '请输入正确的',
      captchaSendSuccess: '验证码发送成功',
      bindSuccess: '绑定成功',
      thirdPartyBind: '第三方绑定',
      oldPassword: '旧密码',
      newPassword: '新密码',
      passwordFormat: '至少8个字符，至少包含大写字母、小写字母、数字、特殊字符其中三类',
      confirmNewPassword: '确认新密码',
      changePassword: '修改密码',
      passwordError: '密码错误，请重新输入',
      passwordFormatError: '密码少于8个字符或字符种类少于三类',
      passwordDiffError: '两次密码输入不一致',
      updatePasswordSuccess: '修改密码成功',
      expireTip: {
        title: '个人账号过期提醒',
        titleDesc: (appName: string, userName: string, date: string) =>
          `您在【${appName}】下 [${userName}] 的用户账号即将在${date}过期，如需延长使用期限，请联系机构管理员。`,
        noTip: '不再提示',
      },
      pswTip: {
        title: '密码修改提醒',
        titleDesc: (appName: string, userName: string, date: number) =>
          `您在【${appName}】下 [${userName}] 的用户账号已经超过${date}天没有修改，为保证账户安全，请尽快修改密码。`,
        known: '知道了',
      },
    },
    collaborate: {
      addCollaborate: '添加协作',
      putCollaborate: '发起协作',
      editCollaborate: '修改协作',
      generateCollaborate: '创建协作',
      conditions: '数据条件',
      permission: '权限',
      user: '用户',
      confirmDelConditions: '确认删除条件',
      createCollaborateSuccess: '创建协作成功',
      updateCollaborateSuccess: '更新协作成功',
      read: '使用',
      viewDetail: '查看',
      download: '下载',
      batchDownload: '批量下载',
      filePreview: '文件预览',
      batchDownloading: '批量下载中...',
      downloadSuccess: '下载完成',
      update: '更新行',
      insert: '插入行',
      delete: '删除行',
      chooseDataRow: '选择数据行',
      chooseDataRowDesc: '"默认对全部数据设置权限，添加筛选条件后可对部分数据行设置权限',
      dataView: '数据查看',
      choosePermission: '选择权限',
      chooseUserOrTag: '选择用户或用户标签',
    },
    resource: {
      myShare: '我分享的',
      shareMe: '我获得的',
      emptyTitle: '还没有任何内容',
      emptyDesc: '请先在左侧栏选择用户/群组，进行资源共享',
      shareTime: '分享时间',
      auth: '权限',
      noAuth: '无法授权',
      fileName: '文件名称',
      fileType: '文件类型',
      creator: '创建者',
      addResource: '添加资源',
      searchResource: '搜索资源',
      resourceType: '资源类型',
      selected: '已选：',
      clear: '清空',
      authEmpty: '权限不能为空',
      shareSuccess: '分享成功',
      chooseUser: '选择用户',
      chooseGroup: '选择群组',
      groupManage: '群组管理',
      delGroup: '删除群组',
      createGroup: '新建群组',
      delGroupSuccess: '新建群组成功',
      delGroupTilte: (name?: string) => `确认删除"${name}?`,
      delGroupDesc: '删除群组将会撤回授权权限，请确认是否删除',
      share: '资源共享',
      authManage: '授权管理',
      authManageFailed: (name?: string) => `"${name}"请至少授予一个权限或撤回该资源`,
      removeResource: '撤销资源',
      selectResourceEmpty: '请先选择资源',
      operateSuccess: '操作成功',
      removeConfirm: '撤销确认',
      removeConfirmDesc: '请确认是否撤销',
      batchUpdateAuth: '批量修改权限',
    },
    sqlDataTable: {
      sqlNoChange: 'SQL无修改',
      sqlSaveSuccess: 'SQL保存成功',
      play: '运行',
      format: '格式化',
      generateDatapkg: '生成数据包',
      sqlEmptyError: '请先编写Sql语句',
      addDatapkgSuccess: '数据包添加成功',
      generateDatapkgFailed: '数据包生成失败',
    },
    luckySheet: {
      addCol: '添加一列',
      colName: '列名称',
      colNameEmptyError: '列名称不能为空',
      colNamePlaceholder: '请输入列名称',
      type: '类型',
      typeEmptyError: '类型不能为空',
      typePlaceholder: '请选择列类型',
      name: '电子表格',
    },
    dataset: {
      uploadSuccess: '上传成功',
      connectSuccess: '连接成功',
      connectFailed: '连接失败',
      connecting: '连接中',
      editConnect: '修改连接',
      createConnect: '新建连接',
      testConnect: '测试连接',
      connectType: '连接类型',
      connectTypeEmptyError: '连接类型不能为空',
      name: '昵称',
      nameEmptyError: '名称不能为空',
      host: '主机',
      hostEmptyError: '主机不能为空',
      port: '端口',
      portEmptyError: '端口不能为空',
      db: '数据库名称',
      dbEmptyError: '链接名称不能为空',
      username: '用户名',
      usernameEmptyError: '用户名称不能为空',
      password: '密码',
      passwordEmptyError: '密码不能为空',
      connectBySsh: '通过ssh链接',
      privateKey: '私钥',
      choosePrivateFile: '选择私钥文件',
    },
    desensitize: {
      editDesensitize: '修改脱敏',
      createDesensitize: '创建脱敏',
      startPos: '起始位置',
      startPosPlaceholder: '请输入从第几位开始加密',
      desensitizeLength: '脱敏长度',
      desensitizeLengthPlaceholder: '请输入需要隐藏的范围长度',
      reg: '正则表达式',
      regPlaceholder: '请输入需要被替换信息',
      desensitizeStyle: '脱敏样式',
      replacePlaceholder: '请输入替换后内容',
      desensitizeType: '脱敏类型',
      desensitizeTypePlaceholder: '请选择脱敏方式',
      noFigma: '设计图中无，待沟通',
      typeEmptyError: '类型不能为空',
      addDesensitize: '添加脱敏条件',
      activeDesensitize: '激活脱敏',
      activeSuccess: '激活成功',
      activeFailed: '激活已关闭',
      field: '字段',
      desensitizeWay: '脱敏方式',
      desensitizeInfo: '脱敏信息',
      delDesensitizeCol: (name?: string) => `确认删除列【${name}】的脱敏条件`,
      desensitizeRange: '脱敏范围',
      md5Desensitize: 'MD5脱敏',
      regDesensitize: '正则脱敏',
    },
    institution: {
      cannotAddOwnerApp: '禁止添加本机构',
      idNotExist: 'ID不存在',
      idNotMatchApp: '机构ID与名称不匹配',
      editApp: '修改机构',
      addApp: '新增机构',
      appId: '机构ID',
      appIdEmptyError: '机构ID不能为空',
      appIdPlaceholder: '请输入机构ID',
      name: '名称',
      nameEmptyError: '名称不能为空',
      namePlaceholder: '请输入机构名称',
      appName: '机构名称',
      createApp: '新增机构',
      appSetting: '机构设置',
      createAppSuccess: '新增机构成功',
      delAppSuccess: '删除机构成功',
      delAppTitle: (name?: string) => `确认删除"${name}"机构`,
    },
    monitor: {
      editMonitor: '修改监控',
      addMonitor: '创建监控',
      create: '新建',
      name: '名称',
      nameEmptyError: '名称不能为空',
      monitorType: '监控类型',
      monitorTypeEmptyError: '监控类型不能为空',
      monitorField: '监控字段',
      monitorFieldEmptyError: '监控字段不能为空',
      relationship: '关系',
      relationshipFieldEmptyError: '关系不能为空',
      monitorCondition: '监控条件',
      monitorConditionEmptyError: '监控条件不能为空',
      monitorConditionHasEmptyError: '请完善监控条件',
      formula: '公式表达式',
      formulaEmptyError: '公式表达式不能为空',
      formulaDesc1: '1. 请在此处编写判断语句，判断语句由 列计算公式、判断符号、判断值 三部分组成',
      formulaDesc2: '2. 判断语句的执行结果只能为 true 或 false',
      formulaDesc3: '3. 判断语句示例：t.单价 * t.数量 > 1000',
      formulaDesc4:
        '4. 运行监控时，会对数据包的每一行进行公式判断；当所有行的执行结果都为true时，质量状态为“合格”，否则为“不合格”',
      formulaDesc5: '5. 输入 t. 可选择列',
      sql: 'SQL语句',
      sqlEmptyError: 'SQL语句不能为空',
      sqlDesc1: '1. 请在此处编写SQL代码，代码中必须包含判断语句',
      sqlDesc2: '2. 判断语句由 计算公式、判断符号、判断值 三部分组成',
      sqlDesc3: '3. 判断语句的执行结果只能为 true 或 false；当执行结果为true时，质量状态为“合格”，否则为“不合格”',
      sqlDesc4: '4. 判断语句示例：select sum(t.单价 * t.数量)>100  from m.pkg_uuid as t where t.city="上海"',
      sqlDesc5: '5. 输入 m. 可选择数据包，输入 t. 可选择列',
      sqlDesc6: '6. 支持在SQL语句中对多个数据包进行操作',
      desc: '描述',
      descPlaceholder: '请简略描述下监控内容',
      startMonitor: '运行监控',
      timingJob: '定时任务',
      createMonitor: '新建监控',
      monitorResult: '最近监控结果：',
      monitorTIme: '最近监控时间：',
      type: '类型',
      condition: '条件',
      result: '结果',
      delMonitorSuccess: '删除监控成功',
      delMonitorTitle: (name?: string) => `确认删除该质量监控"${name}"?`,
      delMonitorDesc: '请慎重，删除后，数据质量监控将变弱！',
    },
    chooseUser: {
      user: '按用户',
      role: '按角色',
      org: '按组织',
      addUser: '添加成员',
      searchRole: '搜索角色',
      searchOrg: '搜索角色',
      sub: '下级',
      people: '用户',
      clear: '清空',
      selected: '已选：',
      group: '按群组',
      orgLeader: '组织负责人',
      orgOnetableAdmin: '部门一表通负责人',
      addPeople: '添加成员',
      addConnector: '添加联系人',
      searchUserOrOrg: '搜索用户/部门/用户 部门 (空格为分隔符)',
      searchUserOrOrgTip: '搜索用户/部门/用户 部门（空格为分隔符），支持模糊搜索，按回车键搜索',
      showCountTip: (count: number) => `已选择用户：${count}人`,
      userResource: '用户资源',
      me: '我自己',
      myOrgList: '我所在的部门',
      selectOrg: '选择部门',
      myOrg: '我的部门',
      setPermission: '设置共享权限',
    },
    filter: {
      advancedFilter: '高级筛选',
      and: '且',
      true: '真',
      false: '假',
      fieldPlaceholder: '请选择字段',
      operatorPlaceholder: '请选择操作符',
      clear: '清除',
      startSearch: '开始检索',
      addFilter: '添加筛选条件',
      setFilter: '设置筛选条件',
    },
    pkgBoundWfspec: {
      formName: '表单名称',
      creator: '创建者',
      createForm: '新建表单',
      searchForm: '搜索表单',
      open: '打开',
      fillData: '表单填报',
    },
    fillDesign: {
      generateFlow: '保存并打开流程引擎',
      fillDesign: '表单设计',
      propertyConfig: '流程配置',
      configTip: '请先完善配置信息',
      allTextCompTip: '表单不能只有【说明文字】题型',
      columnNameRepeatTip: '存储到数据包中的字段不能重复',
      columnExist: '自动存储到数据包中的字段不能再使用表单设计中的字段',
      columnEmptyTip: '表单设计不能为空',
      columnFieldEmptyTip: '题目的表单字段不能为空:',
      columnFieldRepeatTip: '题目的表单字段不能重复:',
      saveTip: '创建成功',
      insertData: '插入数据',
      approval: '审批',
      updateTip: '保存后将会覆盖在流程引擎做的修改！',
      fieldTyle: '题目类型',
      dragFieldToForm: (form: string) => `请向题目“${form}”拖入子组件`,
      colsNotExistedInPkg: (cols: string) => `在数据包中找不到表单字段：${cols}`,
      invalidFieldTypes: (cols: string) => `题目类型与表单字段类型不匹配：${cols}`,
    },
    wfSpecPkgProperyConfig: {
      wfName: '流程名称',
      wfInitiator: '谁可以填报',
      wfNameTmpl: '填报记录名称',
      wfNameTmplPlaceholder:
        '示例：{{__initiator_name}} 发起的 为期【{{假期天数}}天】的【{{假期类型}}假期申请】\n说明：所有流程均内置的变量名会用双下划线开头,比如 __initiator 为发起者ID, __initiator_app_name 为发起者所属app, __initiator_name 为发起者名称; \n其他局部变量(即每个流程的), 如上面示例的`假期天数`等则是表单数据中的元素。',
      wfDescTmpl: '填报记录描述',
      wfDescTmplPlaceholder: '示例：假期开始时间:{{假期起始}}; 假期结束时间:{{假期结束}}',
      approvers: '审批人',
      isApproval: '是否需要审批',
      golbalVariables: '已知全局变量',
      starterId: '发起者ID',
      starterApp: '发起者所属app',
      starterName: '发起者名称',
      isAnonymity: '是否匿名',
      isAutoStoreInfo: '自动存储填报信息',
      appUsers: '本机构所有人',
      loginUsers: '所有Datlas登录用户',
      linkUsers: '所有获得链接的人',
      assignUsers: '指定用户',
      fillInfo: '填报信息',
      storedColumn: '存储到数据包中的字段',
    },
    wfSpecCreate: {
      selectType: '选择流程类型',
      pkg: '数据包填报',
      pkgDesc: () => [
        '数据包填报可用于发起简单的表单填报类流程。',
        '你无需使用复杂的流程编辑器，只需像搭积木一样，通过拖拉拽组件设计填报表单，并选择数据包存储数据即可。',
      ],
      bpmnDesc: () => [
        'BPMN是一个基于业务的流程图，由一系列图形化元素组成，例如矩形表示活动，菱形表示条件等。',
        '图形简化了模型的开发，也使复杂的业务变得易于理解。',
      ],
      selectPkg: '选择数据包',
    },
    tagInput: {
      insert: '置入字段',
    },
    shortEmpty: '无',
    mediaJsonError: '文件内容不符合预期,解析失败',
    formView: {
      addDesc: '添加描述',
      addTitle: '添加标题',
      fillForm: '填报',
      defaultExcelName: '数据填报',
      geometryInputSearch: '搜索地址',
      redrawBtn: '重新绘制',
      retargeting: '重新定位',
      drawTitle: '绘制地理',
      uploadFile: '上传文件',
      startTime: '开始日期',
      endTime: '结束日期',
      geometry: '地理',
      connector: '联系人',
      annex: '附件',
      downloadFileError: '文件地址获取失败',
      uploadBtn: '上传文件',
      changeBtn: '更换',
      fileSizeLimit: '素材需要小于',
      uploading: '上传中',
      uploadFailed: '上传失败',
      mobileCarema: '移动端拍照',
      watermarkSetting: '水印设置',
      watermarkUserId: '添加用户名及id',
      watermarkUserIdTip: '只在登录状态下有效',
      watermarkDate: '日期',
      minUploadCount: '最小上传文件数量',
      maxUploadCount: '最大上传文件数量',
      formCheckError: '表单校验未通过，请检查输入内容',
    },
    formEditor: {
      and: '且',
      or: '或',
      not: '非',
      fieldPlaceholder: '请选择字段',
      operatorPlaceholder: '请选择操作符',
      clear: '清除',
      startSearch: '开始检索',
      addFilter: '添加筛选条件',
      setFilter: '设置筛选条件',
      editableInputEmptyError: '输入不能为空',
      overrideCurrent: '依赖值强制覆盖当前题目值',
      preserveValueOnEmptyDependency: '依赖为空时保留当前值',
      ruleInt: '请输入整数',
      ruleFloat: '请输入小数',
      ruleMaxFloat: (count: number) => `最多允许输入${count}位小数`,
      ruleLessThen: '数值不能小于',
      ruleMoreThen: '数值不能大于',
      ruleContentLessThen: '内容长度不能小于',
      ruleContentMoreThen: '内容长度不能大于',
      isPhoneError: '请输入正确的手机号',
      isIdCardError: '该字段不是合法的身份证格式',
      isEmailError: '请输入正确的邮箱',
      isUrlError: '请输入正确的网址',
      ruleInDataSource: '当前值在数据源中',
      ruleNotInDataSource: '当前值不在数据源中',
      fileCountLessThen: '文件数量不能小于',
      fileCountMoreThen: '文件数量不能大于',
    },
    wfStartWorkflow: {
      noInitiate: '无发起内容',
      stopFill: '问卷已停止填报',
      requestError: '问卷不存在或已删除',
      noAuth: '暂无权限访问该问卷',
      instanceName: '实例名称：',
      startSuccess: '发起成功',
      startInstance: '发起实例',
      fillAgain: '再填一份',
      submitSuccess: '提交成功',
      submitThanks: '感谢你的耐心填写',
      chooseDom: '选择流程节点',
      chooseDomDesc: '（如果上次填报内容为空，将会使用下面节点的内容作为默认值）',
      nodeChooseError: '请至少选择一个流程节点',
      nodeNameEmptyError: '节点名称不能为空',
      getFromNode: '从流程节点获取',
    },
    oneTable: {
      h5: {
        startEndData: '起止日期',
        to: '至',
        searchReportName: '搜索报表名称',
        unhandledTitle: '待处理任务',
        all: '全部 >',
        myForm: '我的报表',
        myTask: '我的任务',
        filling: '填报中',
        finished: '已结束',
        unhandled: '待处理',
        handled: '已处理',
      },
      menu: {
        dashbord: '概览',
        formManagement: '报表下发与管理',
        formCreateManagement: '报表创建与管理',
        missionCenter: '任务中心',
        missionCenterTask: '填报接单任务',
        missionCenterForm: '报表填写任务',
        missionCenterReview: '审核任务',
        missionCenterReviewSubmit: '审核提交任务',
        settingError: '配置有误',
        h5Home: '首页',
        h5MyTask: '我的任务',
        formDatapkg: '报表数据',
        setting: '个人设置',
      },
      uploadExcelValid: {
        clickCopy: '点击复制',
        copyed: '复制成功',
        rightTip1: '查看右侧列表禁止数据',
        rightTip2: '从右边列表中选择数据',
        valueTip1: '从Excel中删除',
        valueTip2: '复制到Excel',
        errorWinTitle: '数据上传校验错误',
        errorWinBodyTip: '请按照下面提示检查数据并修改',
        emailErrorTip: '邮箱地址格式错误',
        emailHowModify: '参考示例: <EMAIL>',
        urlErrorTip: 'URL地址格式错误',
        urlHowModify: '参考示例: https://example.com',
        phoneErrorTip: '手机号码格式错误',
        phoneHowModify: '请输入11位有效的数字',
        idcardErrorTip: '身份证格式错误',
        idcardHowModify: '身份证位数为15位或18位,且只有18位支持最后一位为X',
        numberErrorTip: '内容需要时数字类型',
        numberHowModify: '参考示例: 1, 1.5',
        integerErrorTip: '内容需要是整数类型',
        integerHowModify: '参考示例: 1, 2',
        stringErrorTip: '内容需要是字符串类型',
        stringHowModify: '参考示例: abc, 中文',
        booleanErrorTip: '内容需要是布尔类型',
        booleanHowModify: '参考示例: TRUE, FALSE',
        arrayErrorTip: '内容需要是数组类型',
        arrayHowModify: '参考示例: [1,2], ["a","b"]',
        objectErrorTip: '内容需要是对象类型',
        objectHowModify: '参考示例: {"a":"b"}',
        additionalPropertiesErrorTip: (val: string) => `存在多余的题目或属性(${val})`,
        additionalPropertiesHowModify: (val: string) => `删除多余的题目或属性(${val})`,
        datetimeErrorTip: '日期格式错误',
        datetimeHowModify: (val: string) => `参考示例: ${val}`,
        userSelectErrorTip: '联系人格式错误',
        userSelectHowModify: (val: string) => `参考示例: ${val}`,
        uploadErrorTip: '文件格式错误',
        uploadHowModify: '参考示例: [{"id": "xx", "type": "image", value: "xxxx", name: "example.png"}]',
        arrayCardOrTableErrorTip: '自增卡片或表格格式错误',
        arrayCardOrTableHowModify: '',
        missingPropertyErrorTip: '输入内容不能为空',
        missingPropertyHowModify: '请给该题目填写合适的内容',
        valueNeedInDataSourceErrorTip: '输入内容超出列表允许',
        valueNeedInDataSourceHowModify: '',
        valueCanNotInDataSourceErrorTip: '输入内容禁止在列表中',
        valueCanNotInDataSourceHowModify: '',
        maxDecimalCountErrorTip: (count: string) => `小数位不能超过${count}位`,
        maxDecimalCountHowModify: '减少小数位个数',
        minLengthErrorTip: '内容长度太短',
        minLengthHowModify: (count: string) => `拓展内容长度至少${count}位以上`,
        maxLengthErrorTip: (count: string) => `内容长度超过${count}位`,
        maxLengthHowModify: (count: string) => `缩短内容长度到${count}位以内`,
        maximumErrorTip: (limit: number) => `数字不能大于${limit}`,
        maximumHowModify: (limit: number) => `调整数字小于或等于${limit}`,
        minimumErrorTip: (limit: number) => `数字不能小于${limit}`,
        minimumHowModify: (limit: number) => `调整数字大于或等于${limit}`,
        multipleOfErrorTip: (multipleOf: number) => `数字不是${multipleOf}的倍数`,
        multipleOfHowModify: (multipleOf: number) => `调整数字使其能被${multipleOf}整除`,
        maxItemsErrorTip: (limit: number) => `数组中元素超过${limit}个`,
        maxItemsHowModify: (limit: number) => `减少数组中元素, 使其个数不超过${limit}个`,
        minItemsErrorTip: '数组中元素个数太少',
        minItemsHowModify: (limit: number) => `向数组中添加元素, 使其个数至少达到${limit}个`,
      },
      featureFlags: {
        enableInsertData: '允许新增数据',
        enableUpdateData: '允许修改数据',
        enableDeleteData: '允许删除数据',
        enableDownloadData: '允许下载数据',
        enableSendDown: '允许下发',
        enableTransfer: '允许转交',
        enableReinforce: '允许协同',
        enableRefuse: '允许退回',
        enableModifyDataAfterSubmit: '允许提交后修改数据',
      },
      onlyDealUnassignData: '只显示未分配数据',
      fastSelectLabel: {
        all: '全部数据',
        untouch: '只看未操作数据',
        add: '只看新增数据',
        update: '只看更新数据',
        delete: '只看删除数据',
        synced: '只看已同步数据',
        unsync: '只看未同步数据',
        rejected: '只看被驳回数据',
        approved: '只看被通过数据',
        all2: '同步全部数据',
        add2: '只同步新增且未同步数据',
        update2: '只同步更新且未同步数据',
        delete2: '只同步删除且未同步数据',
        untouch2: '只同步未操作且未同步数据',
        unsync2: '只同步所有未同步数据',
      },
      syncDataPrefixTip: '待同步数据共有',
      submitDataPrefixTip: '待提交数据共有',
      fastView: {
        all: '查看全部',
        add: '查看新增',
        update: '查看更新',
        delete: '查看删除',
        untouch: '查看未操作',
        allWithSubordinates: '查看包含下级全部',
      },
      tableColumns: {
        reportName: '报表名称',
        reportTag: '报表标签',
        startDepartment: '发起部门',
        taskStatus: '任务状态',
        startTime: '发起日期',
        endTime: '终止日期',
        fillDepartment: '填报部门',
        initiator: '发起人',
        filler: '填报人',
        dealTime: '下派时间',
        issuedTime: '下发时间',
        departName: '部门名称',
        userName: '部门人员',
        finishStatus: '完成状态',
        dealType: '处理方式',
        approvalStatus: '审批状态',
        reportDesc: '报表描述',
        periodicStatus: '周期个人状态',
        periodic: '周期',
        rowIndex: '行号',
        question: '题目',
        errorTip: '错误提示',
        howModify: '修改建议',
      },
      myIssued: '我的下发',
      uploadExcelError: '上传的Excel与模板不匹配或者没有数据',
      uploadExcelMaxRowError: '暂不支持超过1000行的数据上传, 你可以尝试将大的文件分割为多个小于1000行的文件再继续上传',
      uploadExcelDataError: '数据校验失败, 请修正后再重新上传',
      uploadExcelBtn: '上传Excel',
      excelDescIndex: (id: any) =>
        `可以将下载的数据，连着【${id}】一起复制到本表中。【${id}】存在值时，会对数据进行修改更新，【${id}】不存在值时，会对数据进行新增【请勿擅自：删除、填写、修改${id}】；`,
      noDataModify: '数据未变更，将关闭窗口',
      columnIndex: '序列',
      columnOpt: '操作',
      systemColumn: '系统列',
      rowAddBtn: '新增数据',
      modifyData: '修改数据',
      statusProcessing: '进行中',
      statusNotStart: '未开始',
      statusUnDeal: '未处理',
      firstDeal: '首次处理',
      statusRefuse: '被驳回',
      statusDoing: '处理中',
      statusUnFill: '未填报',
      firstFill: '首次填报',
      firstApproval: '首次审批',
      statusApproval: '待审批',
      statusDone: '已结束',
      btnViewHistory: '查看历史周期',
      switchVersion: '切换到该周期',
      currentVersionTip: '正在操作',
      btnGet: '填报',
      btnForword: '转派',
      btnSubmitJob: '提交',
      btnSubmitJob3: '提交数据给上级',
      btnSubmitJob4: '同步数据给上级',
      btnSubmitJob2: '重新提交',
      btnSubmitJob5: '重新提交数据给上级',
      btnRevoke: '退回',
      btnTransferForm: '转交报表',
      btnTransferFormTip: '当你不再负责报表管理时,可以转交给他人管理',
      btnTransferFormTip2: '将报表转交给他人管理,转交后您将无权查看该报表任何信息',
      clearReinforce: '同步清空协同人员',
      btnCollector: '新增协同',
      btnCollectorTooltip: '添加用户协助你共同完成任务的填报',
      btnCollectorTooltip2: '添加用户协助你共同管理报表',
      editCollectorEqualTip: '协同者未变更,无需修改',
      submitTooltip: '提交数据给上级查看审批',
      btnForword4Tooltip: '将任务转交给他人完成；转交后您将无权查看该任务的任何信息',
      btnRevokeTooltip: '若该任务并非您的职责范围，您可退回任务给上级',
      btnIssued: '任务下发',
      btnCancelIssued: '取消下发',
      cancelIssuedDesc:
        '确定取消下发任务? 取消后将同步取消该任务的所有子任务, 如果取消前已有数据填报, 需要您在填报界面处理遗留数据。',
      btnAftercancel: '处理遗留数据',
      btnIssueData: '下发新数据',
      btnIssued2: '下发',
      btnForword2: '下派',
      fillForm: '填表',
      btnStartFill: '开始填报',
      btnBatchFill: '批量填报',
      btnRepeatFill: '重新填报',
      btnDetail: '详情',
      btnPass: '通过',
      btnRefuse: '驳回',
      btnSubmit: '确认提交',
      btnSubmitFinished: '提交并结束',
      btnSubmitFinishedConfirm: '确认提交并结束？',
      btnFinish: '结束填报',
      btnCleanWf: '删除报表',
      btnFinishConfirm: '确认结束？',
      btnUpdate: '直接修改',
      btnReApproval: '下发管理',
      btnCreateReport: '创建报表',
      btnConfirmAndFill: '确认并填写',
      btnConfirmAndApproval: '进入审核',
      btnPublish: '发起',
      btnCopyCreate: '以此报表为模板新建',
      btnForword3: '转交',
      btnForword4: '转交',
      issuedUsers: '下发人员',
      addUsers: '添加人员',
      fillType: '填报方式',
      fillTypeIndividual: '单独填报',
      fillTypeIndividualDesc: '下发人员各自单独填报提交',
      fillTypeReinfoce: '协同填报',
      fillTypeReinfoceDesc: '下发人员共同协助填报,协同人员之间共享数据',
      reinfoceMain: '协同负责人',
      self: '（你）',
      issuedDataType: '分配数据的处理方式',
      issuedAll: '全部数据',
      issuedFilter: '筛选的数据',
      issuedSelect: '选择的数据',
      needIssuedData: '分配下发人员需要处理的数据',
      issuedDataStatue: '分配状态',
      issuedDoing: '未分配',
      issuedOk: '已分配',
      issueOkAndNext: '确定并继续下发',
      seletedDataView: '查看下发数据',
      issueTaskEmptyError: '下发配置中有必填项未填写',
      issueDataEmptyError: '不能分配空数据',
      notIssueyet: '当前任务并未进行下发',
      seletedTotal: (total: number) => `当前已选择${total}条数据`,
      issuedType: '下发方式',
      btnIssuedNoData: '不带数据下发',
      btnIssuedWithData: '带数据下发',
      btnBack: '返回填报',
      btnOnlySave: '仅保存,暂不提交给上级',
      btnOnlySave2: '仅保存,暂不同步给上级',
      btnSureSubmit: '确认无误,提交给上级',
      btnSureSubmit2: '确认无误,同步给上级',
      submitDataTip: (add: number, update: number, del: number) =>
        `本次提交共新增${add}条,修改${update}条,删除${del}条数据`,
      submitNoData: '暂无数据变更, 无需操作',
      taskIdEmpty: 'Task Id未获取到',
      cancelFlowTip: '确定结束填报',
      cancelFlowDesc: '结束填报后,未完成的填报将无法继续.',
      saveForm: '保存草稿',
      publishJob: '立即发起',
      deleteForm: '删除草稿',
      scheduledJob: '定时发起',
      published: '已发起',
      draft: '草稿箱',
      filling: '填报进行中',
      filled: '填报已结束',
      canceled: '填报被取消',
      createForm: '创建报表',
      createFormEmpty: '空白新建',
      createFromPkg: '从数据包创建',
      btnAddRow: '新增一行',
      editData: '编辑数据',
      exportToPdf: '导出PDF',
      btnAddOther: '增派',
      issuedDetail: '下发详情',
      fillDataDetail: '填报数据详情',
      rejectReasons: '审批驳回意见',
      refusedReasons: '退回原因',
      btnSureReject: '确定驳回',
      btnApproval: '审核',
      btnUnsubmit: '撤回提交',
      userAdmin: '负责人',
      collectorUser: '协同人员',
      manageCollector: '管理协同',
      btnApprovalAgain: '重新审核',
      btnAddDispatch: '新增派发',
      btnAddDispatch2: '新增下发',
      btnAddCollector: '新增协同',
      btnEditCollector: '编辑协同',
      btnUseTemplate: '使用模版',
      searchEndTime: '终止日期范围',
      searchTag: '搜索报表标签',
      searchRegion: '搜索报表领域',
      searchLevel: '搜索报表级别',
      searchFrequency: '搜索填报频率',
      searchRange: '搜索填报范围',
      searchDepartment: '搜索报表归属单位',
      searchLevelMuti: '搜索报表填报层级',
      searchTaskType: '搜索任务类型',
      searchName: '搜索报表名称',
      todoTaskDoing: '待处理',
      todoTaskDone: '我已处理',
      statisticLabel: '统计',
      statisticDays: '近30日',
      todoLabel: '待办',
      todoDoingReportLabel: '进行中报表',
      todoDoingTaskLabel: '未处理任务',
      lastedUser: '填报人',
      lastedTime: '填报时间',
      dataStatus: '数据状态',
      emptyListTip: '【注意】未填写任何数据。',
      formDetail: '报表详情',
      individual: '单人模式',
      reinfoce: '协同模式',
      reinfoceByOrg: '按部门协同',
      allData: '全部数据',
      editDateTip: '点击编辑可以在右侧进行编辑',
      uploadExcelTip: '请先下载模版后按照模版格式上传数据',
      uploadExcelFileTip: '支持XLSL, XLS格式',
      editFormSuccess: '表单编辑成功',
      submitOnce: '只允许提交一次，提交后任务自动结束',
      publishTip: '发起报表后将无法编辑报表（可以更改报表配置）。请确认报表设计无误后发起任务。',
      publishFlow: '确定发起',
      downloadData: '下载数据',
      dataFormat: '数据格式',
      flowDetail: '流转详情',
      publishTextTip: '发起后可开始填报',
      finishJob: '结束任务',
      editFormConfig: '编辑报表配置',
      exitCreateConfirm: '退出确认？',
      exitCreateConfirmDesc: '是否确认退出当前页面？',
      confirmCancelTask: {
        title: '确定取消下发？',
        content: '请输入取消理由, 方便您后期管理',
        okText: '确认取消下发',
      },
      confirmRevokeTask: {
        title: '确定退回',
        content: '请输入退回理由, 退回后该任务将不会出现在您的任务中心。上级可在下发管理中查看详情。',
        okText: '退回',
      },
      confirmCancelSubmit: {
        title: '确定撤回提交',
        content: '撤回提交后,你提交的数据也将同步撤回,需要你后续重新提交',
        okText: '确定撤回',
      },
      confirmDeleteDraft: {
        title: '确定删除草稿',
        content: '确定后将无法恢复，请谨慎操作。',
        okText: '确定删除',
      },
      confirmDeleteWorkFlow: {
        title: '确定删除报表',
        content: '删除报表会将已收集的数据一起删除，无法恢复。请谨慎操作。',
        okText: '确定删除',
      },
      flowType: {
        usual: '短期',
        periodic: '周期',
        endless: '长期',
        collaborate: '协同',
      },
      dateStatus: {
        start: '发起',
        create: '创建',
        publish: '发布',
      },
      statistics: {
        finishedReport: '已结束报表',
        finishedOrder: '已完成接单',
        finishedForm: '已完成填报',
        finishedReview: '已完成审核',
      },
      detail: {
        tabInfoLabel: '报表信息',
        tabFlowLabel: '流转管理',
        tabDataLabel: '数据详情',
      },
      tip: {
        tableDataTotal: '表格数据量',
        staticsPrefix2: '当前下发任务共有数据',
        staticsDataStatus: (info: Record<string, number>, staticsPrefix = '截止到目前共有数据') =>
          `${staticsPrefix}: ${info.total}条(数据状态新增：${info.add}条; 数据状态更新: ${info.update}条; 数据状态删除: ${info.del}条; 未操作数据: ${info.untouch}条)`,
        confirmFinish: '确认完成？',
        taskFinished: '任务已完成',
        publishReport: (name: string) => `确认发布报表"${name}"`,
        deleteReport: (name: string) => `确认删除报表"${name}"`,
        noPkgInfo: '缺少数据包信息',
        confirmSubmit: (name: string) => `确认提交"${name}"`,
        confirmReApproval: '请选择操作',
        confirmAccept: '确认进行填报（确认后无法继续下发和转派）',
        confirmEdit: '确认直接修改',
        publishFail: '发布失败，流程未启动',
        fillReportInfo: '请先完善报表信息',
        reportInfoError: '报表配置中有必填项未填写',
        periodicMinInterval: (minInterval: number) => `周期间隔不能小于${minInterval}分钟`,
        reportConfigError: '发布设置中有必填项未填写',
        residentInfoCount: '最多只能添加一个市民信息组件',
        residentIdCard: '请勾选市民信息组件的身份证选项',
        noSearchTip: '暂未收集或填写任何数据',
        deleteData: '删除数据',
        confirmDeleteData: '确定需要删除该行数据?',
        dataFail: '数据获取失败',
        flowEmpty: '暂无流转信息',
        dispatchRepeat: (names: string) => `不允许重复指派用户: [${names}]`,
        reassignTip:
          '【注意】选择转派后，主责部门将改为对应的部门，本部门将无权限继续访问该任务。如需要继续管理该任务，请选择“下派”。',
        reassignTip2: '【注意】选择转交后，您将无权限继续访问该任务。如需要继续访问该任务，请选择“下发”或“协同”。',
      },
      reportInfo: '报表信息',
      reportDesign: '报表设计',
      reportEdit: '编辑报表',
      publishSetting: '发布设置',
      fillSetting: '填报设置',
      fillDate: '填报时间：',
      endFillDate: '填报结束时间',
      flowCycletimer: '周期设置',
      flowSetting: '流程配置',
      needApproval: '需要上级审批',
      inheritData: '自动继承上一周期数据',
      completeWorkflowAfterNextPeriod: '当下个周期发起时自动结束上个周期任务',
      completeWorkflowAfterNextPeriodTip: '填报人员将无法在已结束周期任务中继续填报',
      showDataAfterSubmit: '数据提交后上级才可见',
      finishAfterSubmit: '数据提交后结束任务',
      reportConfig: '报表配置',
      collaborate: '协同信息',
      residentInfo: '市民信息',
      periodConfig: '周期设置',
      reportStartDate: '报表开始日期',
      reportEndDate: '报表终止日期',
      dispatchConfig: '派发部门设置',
      infoName: '报表名称',
      infoOrg: '所属部门',
      infoFormOrg: '数据权限部门',
      ownership: '报表归属部门',
      infoTag: '标签',
      infoDesc: '报表填报说明',
      reportFillTask: '报表填写任务',
      finisheProcessing: '完成度',
      firstDispatch: '首次指派',
      firstDispatch2: '接到新任务',
      firstDispatch3: '首次发起',
      endFill: '填报结束',
      rejectDispatch: '驳回指派',
      startEndDate: '起止时间',
      startDate: '起始时间',
      endDate: '截止时间',
      reportTemplate: '报表模板',
      downloadTemplate: '下载模板',
      reportCreate: '创建报表',
      municipalProject: '市级项目',
      districtProject: '区级项目',
      streetProject: '街道级项目',
      dataCollection: '数据采集',
      dataVerify: '数据核实',
      startSpecError: '配置文件缺少发起流程的ID',
      flowSpecError: '配置文件缺少流转流程的ID',
      ownershipPkgIdError: '配置文件缺少归属部门的数据包ID',
      expedite: '催办',
      expediteAll: '催办全部',
      viewFlowMap: '查看下发图谱',
      viewPeriodicHistory: '查看历史周期',
      hadIsuuedTip: '已下发',
      visit: '走访慰问',
      taskType: '任务类型',
      regionTitle: '报表领域',
      levelTitle: '报表级别',
      levelMutiTitle: '报表填报层级',
      frequencyTitle: '报表填报频率',
      rangeTitle: '填报范围',
      ownerTitle: '填报负责人',
      ownerPhoneTitle: '报表负责人联系方式',
      publishSuccessTitle: '发起成功！',
      publishSuccessTip: '当前报表已发起，可开始填报。',
      publishSuccessDividerTip: '接下来你可以开始',
      region: {
        person: '人',
        house: '房',
        company: '企',
        matter: '事',
        thing: '物',
      },
      level: {
        country: '国家级',
        province: '省级',
        city: '市级',
        district: '区级',
        street: '街镇级',
      },
      frequency: {
        day: '每天',
        week: '每周',
        month: '每月',
        quarter: '每季',
        year: '每年',
        halfYear: '每半年',
        demand: '按需',
        temporary: '临时',
      },
      levelMuti: {
        district: '区县',
        street: '街镇',
        community: '村社',
      },
      range: {
        hangzhou: '杭州市',
        shangcheng: '上城区',
        gongsu: '拱墅区',
        xihu: '西湖区',
        binjiang: '滨江区',
        xiaoshan: '萧山区',
        yuhang: '余杭区',
        linping: '临平区',
        qiantang: '钱塘区',
        fuyang: '富阳区',
        linan: '临安区',
        tonglu: '桐庐县',
        chunan: '淳安县',
        jiande: '建德市',
        xihu2: '西湖风景名胜区',
      },
      advanceFilter: {
        title: '高级筛选',
        reset: '清除全部',
      },
      formPlaceholder: '搜索报表名称',
      taskPlaceholder: '搜索任务名称',
    },
    resourceShare: {
      title: '共享',
      allShared: '所有共享用户',
      selected: (num: number) => `已选${num}项`,
      addUser: '添加用户',
      allSelect: '全选',
      cancel: '取消',
      confirm: '确定',
      delete: '移除用户',
      editPermission: '修改权限',
      noContent: () => (
        <span>
          当前暂无共享用户，点击右上角“<span style={{ color: 'var(--metro-primary-default)' }}>+添加用户</span>”来共享
        </span>
      ),
    },
    share: {
      shareWorkflow: '分享流程',
      shareLink: '分享链接',
      copyBtn: '复制',
      previewBtn: '预览',
      qrcode: '二维码',
      miniCode: '小程序二维码',
      qrFileName: (spName: string, type: string) => `${spName}_${type}_${Math.random().toString(16).slice(2)}`,
      fillParamLabel: '预定义参数',
      fillParamBtn: '去设置',
      fillParamTip: '通过在问卷链接后附带一些参数,来控制问卷中部分题目的选择和填写',
      fillParamSuccessTip: '分享此链接，已经包含预先设置好的参数',
      sharePlateform: '分享到应用',
      shareToPf: (pf?: string) => `分享到${pf}`,
      redbook: 'Redbook',
      weibo: 'Weibo',
      douyin: 'Douyin',
      qq: 'QQ',
    },
    workflow: {
      menu: {
        workflow: '全部流程',
        myInitiate: '我发起的',
        waitHandle: '我的任务',
        myApproval: '我的处理',
        formResources: '流程表单',
      },
      form: {
        createForm: '新建表单',
        searchForm: '搜索表单',
        columnName: '表单名称',
        columnDesc: '表单描述',
        columnCreateTime: '创建时间',
      },
      useSelf: '引用自己',
      startSuccess: '发起成功',
      approved: '通过',
      rejected: '拒绝',
      inputInfo: '填写信息',
      workflowImage: '流程图',
      instance: '实例',
      theWorkflowImage: '的运行图',
      viewInitiateInfo: '查看发起信息',
      viewEngineError: '查看错误信息',
      tempDataGetFailed: '流程数据获取失败',
      inputFormInfo: '填写表单信息',
      operateSuccess: '操作成功',
      noInitiate: '无发起内容',
      copyLinkSuccess: '链接复制成功',
      createLinkPage: '生成外链页面',
      createTime: '发起时间',
      byWorkflow: '所在流程',
      byInstance: '按实例',
      byTask: '按任务',
      byStatus: '运行结果',
      byResult: '运行结果',
      searchPlaceholder: '搜索实例名称',
      searchTaskPlaceholder: '搜索任务名称',
      moreFilterBtnLabel: '高级搜索',
      moreFilter: {
        chooseSpec: '请先选择流程',
        specTip: '针对不同流程，可对特定字段进行高级搜索',
        specLabel: '选择流程: ',
        emptyTip: '没有支持搜索的全局变量',
        clearBtn: '清除',
        okBtn: '搜索',
      },
      initiateStatusDesc: '请选择发起状态',
      cancel: '撤销',
      detail: '详情',
      flowName: '流程名称',
      flowId: '实例ID',
      title: '发起的实例名称',
      taskName: '任务名称',
      wfName: '所在实例',
      wfSpecName: '所在流程',
      taskDate: '任务发起时间',
      taskStatus: '任务状态',
      desc: '描述',
      applyTime: '填报时间',
      runResult: '运行结果',
      searchFlow: '搜索流程',
      cancelConfirm: '撤销确认',
      cancelConfirmDesc: '请确认是否撤销，并说明其理由',
      cancelRequired: '撤销理由不能为空',
      initiate: '发起的',
      initiateInfo: '发起信息',
      circulationInfo: '流转信息',
      instanceStatusDesc: '请选择实例状态',
      userName: '填报人',
      searchResource: '搜索资源名称',
      instanceName: '实例名称：',
      startInstance: '发起实例',
      copyStartInstanceLink: '复制发起实例链接',
      editFlow: '编辑流程',
      tempDataError: '流程数据获取失败',
      copyXml: '复制XML',
      replaceTemp: '从文件导入',
      replaceTempFromCopy: '从剪切板导入',
      clipboardError: '剪切板的内容不是流程XML',
      noname: '未命名',
      nameOrConfigEmptyError: '名称及配置不能为空！',
      tempOperateSuccess: (name?: string, isAdd?: boolean) => `${name} 流程${isAdd ? '更新' : '新增'}成功`,
      copyed: '已复制到剪贴板',
      userOrAdmin: '负责人/创建者',
      lastRunTime: '最近执行时间',
      lastRunResult: '最近执行结果',
      uploadTemp: '上传文件',
      uploadCompressTip:
        '开启后如果图片超出限制大小，系统会将图片压缩到限制大小以下，请注意，如果上传的图片大小超过限制大小差别过大，则压缩时间会变长。',
      createFlow: '新建流程',
      share: '分享流程',
      downloadTemp: '导出为文件',
      delTemp: '删除',
      delTempSuccess: (name?: string) => `流程 ${name} 删除成功`,
      delTempConfirm: (name?: string) => `确认删除 ${name} 流程？`,
      delTempConfirmDesc: '删除后，将无法查看已运行的记录及定时任务。',
      h5: {
        initiateUser: '发起人：',
        applyUser: '发起人：',
        applyTime: '任务发起时间：',
        completeTime: '任务完成时间：',
      },
      status: {
        idle: '挂起中',
        ready: '审批中',
        completed: '已通过',
        rejected: '已拒绝',
        cancelled: '已撤销',
        waiting: '审批中',
        processing: '流转中',
        processed: '已完成',
        done: '已完成',
        finishDone: '已结束',
        reject: '已驳回',
        noRun: '未运行',
        finished: '已处理',
        pending: '待处理',
        ongoing: '进行中',
        engine_error: '流程出错',
        approval: '待审核',
      },
      keybord: {
        function: '功能',
        hotKey: '快捷键',
        undo: '撤销',
        redo: '重做',
        copy: '复制',
        cut: '剪切',
        paste: '粘贴',
        find: '查找',
        drag: '拖拽',
        selectBox: '框选',
        connect: '连线',
        editLabel: '编辑标签',
        selectAll: '全选',
        delete: '删除',
        zoomin: '放大',
        zoomout: '缩小',
        nozoom: '不缩放',
        move: '移动',
      },
      notSupportForm: '推荐使用函数参数配置工具，系统将优先采用函数参数配置的参数值',
      active: '生效中',
      pkg: '数据包',
      mappingTable: '字段映射表',
      mappingTableTip: '请填写数据包字段与表单字段之间的映射关系',
      paramsName: '参数名称',
      paramsNameTip: '请填写参数名称',
      required: '（必填）',
      visualParamsConfig: '函数参数配置',
      traditionalParamsConfig: '函数参数配置（旧）',
      saveWithVisualParams: '保存函数参数配置',
      saveWithTraditionalParams: '保存函数参数配置（旧）',
      specialParamsConfigTip: '此函数有专门的配置方式，您可以使用上方的配置，也可以在此处直接编辑参数',
      manualParamsConfigTip: '您可以在此处直接编辑参数，但推荐使用上方的配置',
      updateDatapkgDataTip: '选择的数据包字段中必须：有且只包含一个“唯一索引”属性的字段',
      fieldName: '数据包字段名',
      fieldId: '表单字段',
      pkgIdTip: '数据包ID(隐藏属性、勿删！)',
      insertData: '插入数据',
      selectPkg: '选择数据包',
      generateForm: '生成表单',
      wfSpecList: {
        type: '流程类别',
        status: '流程状态',
        enable: '启用',
        disable: '禁用',
      },
      reFill: '再填一份',
      fillThanks: '感谢你的耐心填写',

      anonymous: '匿名',
      refuse: '驳回',
      applyInfo: '发起信息',
      flowInfo: '流转信息',
      anonymousNode: '匿名节点',
      fillStart: '填报发起',
      flowEnd: '结束',
      cancelled: '撤销',
      cancelledReason: '撤销原因',
      workflowNotExist: '流程不存在',
      edit: {
        datasource1: '选项1',
        datasource11: '选项1-1',
        datasource111: '选项1-1-1',
        datasource112: '选项1-1-2',
        datasource2: '选项2',
        datasource3: '选项3',
        datasourceLabel: '显示值',
        datasourceValue: '存储值',
        single: '单选题',
        multiple: '多选题',
        datePicker: '日期与时间',
        input: '问答',
        geometryInput: '地理',
        geometryInputDesc: '点击右侧图标输入',
        emptyContent: '暂无选项',
        switch: '开关',
        upload: '附件题',
        userSelector: '联系人',
        autoComplete: '联想填空题',
        rate: '评分题',
        arrayCards: '自增卡片题',
        arrayCardsTitle: '卡片标题',
        arrayTable: '自增表格题',
        colName: '列名',
        cascader: '级联选择题',
        datasourceStep1: '①数据源设置',
        datasourceStep2: '②数据处理',
        datasourceStep3: '③响应结果',
        datasourceEmpty: '该题型无需配置数据',
        datasourceTypeStatic: '静态数据',
        datasourceTypeApiOut: '外部API',
        datasourceTypeApiIn: '内部API',
        datasourceTypeApiInDesc: '请选择内部API',
        dataAuthFollow: '数据权限跟随',
        datasourceTypeQLang: 'QLang',
        datasourceTypeLowCodeQLang: '低码QLang',
        datasourceType: '数据源类型',
        datasourceTypeDesc: '请选择数据类型',
        fillEdit: '全屏编辑',
        unsupportObj: '不支持编辑复杂对象',
        addOne: '新增一行',
        changePreviewEdit: '切换至可视化编辑',
        changeCodeEdit: '切换至代码编辑',
        requestType: '请求方式',
        requestTypeDesc: '请选择请求方式',
        urlDesc: '请输入URL',
        serviceProxy: '服务器代理请求',
        serviceProxyDesc: '因跨域无法访问时可勾选',
        cookie: '需要cookie',
        cookieDesc: '不选择代理且需要获取 cookie 时使用',
        apiDep: 'API依赖',
        pkgColInfo: '数据包列信息',
        pkgRowInfo: '数据包行数据',
        curOrgUserList: '当前机构用户列表',
        curOrgList: '当前机构部门列表',
        creator: '创建者',
        infoUser: '填报人',
        allUser: '全部用户',
        orgUser: '部门用户',
        specificUser: '指定用户',
        allOrg: '全部部门',
        specificOrg: '指定部门',
        equalOrg: '平级部门',
        childOrg: '下级部门',
        allChildOrg: '下级所有部门',
        currentOrg: '本级部门',
        currentAndLowerOrg: '本级及下级部门',
        currentAndLowerAllOrg: '本级及下级所有部门',
        // CURRENT = 'current', // 本部门
        // CURRENT_AND_LOWER = 'currentAndLower', // 本部门及下级部门
        // CURRENT_AND_LOWER_ALL = 'currentAndLowerAll', // 本部门及下级所有部门
        settings: '选项设置',
        userRange: '用户范围',
        orgRange: '机构范围',
        sqlError: 'SQL不能为空，其不能有语法错误',
        runSql: '运行Sql',
        alloc: '格式化',
        previewModeEdit: '预览模式编辑',
        sqlDev: 'Sql依赖',
        orgDev: '部门依赖',
        objMode: '对象模式',
        lowCodeQe: 'Qe低码编辑器',
        lowCodeSql: '低码Sql',
        noDep: '暂无依赖',
        addCol: '新增一列',
        delCol: '删除一列',
        cascaderTitleDesc: '级选项',
        mdtAI: '脉策AI助手',
        AI: 'AI助手',
        mdtAIDesc: '不会写代码?试试脉策AI助手吧.',
        handler: '处理器',
        createHandler: '新建处理器',
        addGlobalHandler: '添加全局处理器',
        delHandler: '确定删除该处理器嘛？',
        delHandlerDesc: '删除后, 可能导致数据源结果不符合要求',
        codeEmptyOrGrammerError: '代码不能为空或有语法错误',
        codeEdit: '代码编辑',
        formatDataDesc: '当数据源返回结果不满足格式要求时,可以在这做些处理,比如排序,过滤,转换等',
        dataTransform: '数据转换',
        dataFilter: '转换&过滤',
        dataSort: '转换&排序',
        AIDesc: '你好,我是脉策AI助手,可以辅助你完成数据处理的代码编写',
        dataRespContent: (type: string, str: string) => `数据类型是${type}，数据截取部分如下\n\n${str}\n\n`,
        AIPlaceholder: '请描述你的要求',
        AIAwaitPrevAnswer: '请等待上一个回答结束, 才能继续',
        AINeedDatasource: '请先配置数据源, 并且数据源配置正确',
        AICodeFormat: '代码要求',
        AIAcceptThis: '采纳该回答',
        AIQuickReply1: '1. 数据可能为空,加些前置校验',
        AIQuickReply2: '2. 使用es6,保持代码简洁',
        AIQuickReply3: '3. 根据转换后的数据过滤value是空值的情况,只保留5条数据',
        AIQuickReply4: '3. 根据转换后的数据value进行从大到小排序',
        matching: '匹配中',
        matchSuccess: '匹配成功',
        matchFailed: '匹配失败',
        field: '字段',
        map: '映射',
        status: '状态',
        fieldMap: '字段映射',
        viewFormat: '查看格式要求',
        dataResultTip: '经过前2步骤后,最终结果展示如下',
        editableParam: '参数名(可编辑)',
        formatTransform: '格式转换要求',
        defaultValueFormatDesc1: '为保证字段格式正确，我们',
        defaultValueFormatDesc2: '约定',
        defaultValueFormatDesc3: '用下面的方式可以数据进行进一步的转换，例如:',
        defaultValueFormatDesc4: '1. 字段接收【整数】类型，会默认对数据进行一次 parseInt 处理；',
        defaultValueFormatDesc5: '2. 字段接收【浮点数】类型，会默认对数据进行一次 parseFloat 处理；',
        defaultValueFormatDesc6:
          '3. 字段接收【数组】类型，接收一个带分隔符（","）的字符串可以转换成数组，例如"1,2,3"会转换为["1","2","3"]',
        defaultValueMatchTitle: '默认会按照字段名称进行匹配，如需手动进行字段映射转换，请开启手动字段映射功能。',
        defaultValueMatchClose: '是否要关闭字段映射？',
        defaultValueMatchCloseDesc: '关闭后，您的配置无法保存，参数将根据字段名称自动匹配。',
        defaultValueManualMatch: '手动字段映射',
        defaultValueManualMatchDesc: '开启后可以精准过滤通过url获取的参数是否进行默认填充，或者映射匹配填充。',
        defaultValueStep1: '①默认值设置',
        defaultValueStep2: '②数据处理',
        defaultValueStep3: '③预览',
        defaultValue: '默认值',
        delDefaultValueTitle: '确定删除该默认值嘛？',
        delDefaultValueTitleDesc: '删除后, 可能导致部分数据无法默认填充',
        defaultValueSource: '默认值来源：',
        defaultValueType: '请选择默认值类型',
        defaultValueSortDesc: '此处的排序会影响最终默认值的结果，默认会从上到下依次合并。',
        createDefaultValue: '新建默认值',
        setted: '已设置',
        unset: '未设置',
        fieldDep: '依赖字段',
        operator: '操作',
        depSetting: '依赖配置',
        depSettingDesc: '引用变量通过变量名引用，变量名以:开头，如:h2gIMlc2GT',
        addDepField: '添加依赖字段',
        sourceSetting: '来源配置',
        dataHandle: '数据处理',
        finally: '到底啦',
        runScript: '执行脚本',
        visible: '显示条件',
        notJson: '不是标准的JSON字符串: ',
        needSql: '请填写Sql',
        needUrl: '请填写Url',
        chooseApi: '请选择API',
        serviceError: '服务器异常，请稍后再试',
        datasourceRequestSetError: '数据源设置请求错误:',
        filedNeedManual: (str: string) => `字段[${str}]未匹配成功, 请设置映射或代码处理`,
        dataHandleError: '经处理器处理后, 数据为NULL, 请关闭处理器或检查代码',
        dataHandleDataError: '数据有误，类型应为：',
        dataHandleFormatError: '数据格式不满足格式要求, 你可能忘了处理，或者尝试下字段映射',
        filterError: (name: string, message: string) => `过滤器[${name}]执行异常: ${message}`,
        dataValueSettingError: '表单值设置请求错误:',
        chooseDatapkg: '请选择数据包',
        datapkgEmpty: '该数据包暂无数据',
        needOrg: '请选择部门',
        needUser: '请选择用户',
        netError: '网络错误',
        downloadSuccess: '下载成功',
        formatError: '格式不正确',
        nodeName: '节点名称',
        addNode: '添加节点',
        question: '题目',
        formFieldName: '字段名',
        fieldNoEmpty: '变量名不能重复',
        addCondition: '添加条件',
        addRule: '添加规则',
        strType: '字符类型',
        numType: '数值类型',
        boolType: '布尔类型',
        literalValue: '静态值',
        globalVar: '全局变量',
        pythonExpr: 'python表达式',
        template: '模版',
        value: '值',
        dataTypeTransform: '将值设置为',
        userType: '成员分类',
        varCategory: '变量分类',
        configAfterSubmit: '提交后显示设置',
        fromWfSpec: '从现有流程获取',
        fromVars: '从变量获取',
        childWf: '子流程',
        propagateAllParentVariables: '将父流程所有参数传递给子流程',
        childWfInputVars: '子流程实例的输入参数',
        inputVars: '输入参数',
        childWfAcceptVars: '子流程接受的变量',
        propagateAllChildVariables: '将子流程所有参数传递给父流程',
        childWfOutputVars: '子流程实例的输出参数',
        outputVars: '输出参数',
        parentWfAcceptVars: '父流程接受的变量',
        waitChildCompleted: '是否等待子流程结束',
        childWfName: '子流程实例名称',
        childDescription: '子流程实例描述',
        asigneeVisible: '是否将子流程展示到前端待处理任务列表',
        assignUsers: '需要前端展示待处理任务的用户',
        userGrouped: '用户分组',
        allowAssigneeCancelChild: '是否允许指派人取消子流程',
        childInitiator: '子流程的实例发起人',
        childWfSource: '子流程来源',
        childWfStartTip: '子流程的实例发起人最多允许一人',
        exprType: '表达式类型',
        exprPlaceholder: '请输入表达式',
        conditionSetting: '条件表达式设置',
        executorId: '节点执行人来源',
        targetId: '应用的节点',
        target: '节点',
        configNodeExecutor: '节点执行人动态设置',
        fromNodeAndVars: '从流程节点和全局变量获取',
        tipSetForm: '请先设置表单',
        fieldMapList: '与当前节点表单字段的映射关系',
        nodeAndVarsField: '节点和全局变量字段',
        currentNodeField: '当前节点字段',
        chooseOtherField: '选择其他流程节点',
        defaultType: '默认值类型',
        isUsePrev: '是否使用上次填报内容',
        defaultValTitleFull: '与当前节点表单字段的映射关系（如果上次填报内容为空，将会使用下面节点的内容作为默认值）',
        defaultValTitle: '与当前节点表单字段的映射关系',
        configFormDefault: '表单默认值设置',
        pkg: '数据包',
        wfNode: '流程节点',
        usedNode: '应用的节点',
        formSource: '指定表单来源',
        tipVarName: '变量名称不能为空',
        tipVarType: (name: string) => `${name}变量类型不能为空`,
        tipVarId: (name: string) => `${name}变量ID不能为空`,
        tipVarDefault: (name: string) => `${name}的默认值类型不匹配`,
        tipVarIdFormat: '变量ID不符合规范',
        tipVarIdFormatContent: (name: string) =>
          `变量ID（${name}）不符合命名规范, ID必须以字母/汉字开头, 并且只能包含字母、汉字、数字和下划线。`,
        tipVarIdExist: (name: string) => `以下变量ID在流程中已存在，请修改：${name}`,
        sysGlobalVar: '系统全局变量',
        varName: '变量名称',
        varType: '变量类型',
        varID: '变量ID',
        customGlobalVars: '自定义全局变量',
        selectField: '选中节点字段',
        customGlobalVarTip: `
          <div>
            <div>默认值说明</div>
            <div>一、数值、文本类型请直接填写，例如：</div>
            <div>数值：12345</div>
            <div>文本：abcde</div>
            <div>二、数组类型的变量默认值请用逗号(,)分割，并用中括号包裹，例如：</div>
            <div>文本数组：[&quot;aaaa&quot;, &quot;bbb&quot;]</div>
            <div>整数数组：[1, 2]</div>
            <div>文件类型：[{JSON.stringify({ name: 'xxx.jpg' })}]</div>
            <div>三、布尔类型支持的默认值有：true、false</div>
          </div>
        `,
        varDefault: '变量默认值',
        varDesc: '变量描述',
        supportSearch: '是否支持搜索',
        inForm: '所在表单',
        questionName: '题目名称',
        formVar: '表单变量',
        configVar: '变量管理',
        tipLoopDataInputRef: '【创建循环所遍历的数据集】的类型必须是数组',
        tipLoopDataOutputRef: '【用于汇总所有实例输出值的数据集】的类型必须是数组',
        byLoopCount: '按循环次数',
        byDatasource: '按数据集',
        normalTimer: '常规设置',
        mdtVarRef: '引用变量',
        loopCount: '循环次数',
        loopDataInputRef: '创建循环所遍历的数据集',
        inputDataItem: '每个实例接受遍历值的变量',
        outputDataItem: '每个实例的输出变量',
        loopDataOutputRef: '用于汇总所有实例输出值的数据集',
        completionCondition: '完成条件',
        multiInstance: '多实例',
        tipCustomVar: '被赋值的变量只能为自定义变量。',
        tipExpr: `多个赋值语句请用分号(;)分割，字符串请用单引号(')包裹`,
        configGlobalVar: '全局变量赋值',
        tipLoopCountCondition: '【最大循环次数】和【循环条件】至少指定一个',
        loopMaximum: '最大循环次数',
        loopCondition: '循环条件',
        loopInterval: '循环自动执行间隔(单位秒)',
        recordLoop: '记录全部循环的执行历史',
        recordLoopTip:
          '默认只记录最后一次执行, 如果打开开关, 当循环次数很多, 可能导致流程实例占用内存过大, 影响流程效率',
        testBefore: '是否先检查循环条件',
        standardLoop: '标准循环',
        cycleTime: '循环周期',
        date: '日期',
        configTimer: '定时器值设置',
        userAssign: '用户分配',
        startFormField: '发起表单字段',
        configWfTemplate: '流程实例模版设置',
        configDetailPage: '详情界面设置',
        detailPageType: '页面类型',
        detailPageTypeDefault: '默认',
        detailPageTypeDatlas: 'Datlas大屏',
        detailPageTypeAmis: 'AmisJson',
        detailPageTypeUrl: '自定义Url',
        detailPageTypeUrlPlacehold: '请输入Url(支持内部相对路径)',
        detailPageTypeAmisPlacehold: '请输入JSON',
        detailPageTypeDatlasPlacehold: '请输入大屏ID',
        tipFieldFormat: (name: string) =>
          `字段名（${name}）不符合命名规范, 变量名必须以字母/汉字开头, 并且只能包含字母、汉字、数字和下划线。`,
        tipFieldExist: (name: string) => `以下题目的字段名在流程中已存在，请修改：${name}`,
        tipAddField: '请添加字段映射',
        tipFieldRepeat: '表单字段不能有重复项',
        tipPkgFieldRepeat: '数据包字段不能有重复项',
        formField: '初始表单字段',
        mappingField: '映射字段',
        returnColumns: '返回的列',
        returnRows: '返回前多少行',
        filterSet: '设置筛选条件',
        tipFilterSet: '条件值为变量请用大括号包裹，例：{{__initiator}}',
        text: '文本',
        link: '链接',
        textContent: '文本内容',
        linkTitle: '链接标题',
        linkContent: '链接内容',
        linkUrl: '点击链接跳转的地址',
        linkTip: '链接地址必须使用https协议，例：https://linkaddress',
        linkImg: '链接图片',
        linkImgTip: '图片地址必须使用https协议，例：https://picaddress.jpg',
        markdownTitle: '首屏会话透出的展示内容',
        markdownText: 'markdown格式的消息',
        webhook: 'Webhook地址',
        webhookMsgType: '消息类型',
      },
      config: {
        question: '题目',
        pkgField: '数据包字段',
        insertField: '插入表单字段',
        detailPage: '流程详情页',
        defaultPage: '默认完成页',
        outerPage: '跳转到外部页面',
        outerPc: 'pc端地址',
        outerMobile: '移动端地址',
        innerPage: '跳转到内部页面',
        refresh: '刷新原页面',
        blank: '在浏览器中打开新的标签页',
        submitDisplay: '提交后显示',
        allowMult: '是否允许多次填报',
        urlAfterSubmit: '请输入目标页面地址',
        formDataTip:
          '支持在 URL 路径及其查询参数中使用表单变量，变量格式：{{variable}}，例如 ?id={{userId}} 或 /api/{{endpoint}}',
        jumpType: '提交后跳转',
        storeLocation: '提交后存储位置',
        selectPkg: '选择数据包',
        storePkgMap: '题目与数据包字段的映射关系',
        baseConfig: '基础设置',
        fillConfig: '填报设置',
        storeConfig: '存储设置',
        submitConfig: '提交后显示设置',
      },
      startApply: '发起的申请',
      searchTitle: '搜索标题',
    },
    forwarder: {
      noContent: '无内容',
      noContentDesc: '对不起，您访问的页面不存在或已被删除',
    },
    comment: {
      replyToPlaceholder: '输入评论...',
      commentTitle: '评论:',
      noComment: '暂无评论',
      deleteSuccess: '评论已删除',
      more: '展开',
      less: '收起',
      addCommitSuccess: '提交评论成功',
      loadMore: '查看更多',
      deleteComment: '删除评论',
      reply: '回复',
      comment: '评论',
      refreshSuccess: '刷新成功',
    },
    pkg: {
      appSetting: '机构设置',
      shareDesc: '只允许将本机构的数据共享到外部机构。',
      dataSize: '数据量',
      lastUpdate: '上次更新',
      copyLink: '复制链接',
      copyLinkSuccess: '复制链接成功',
      publicLinkShare: '公开链接分享',
      noAuth: '无权限',
      dataApply: '数据申请',
      pkgEmptyError: '数据包不存在或其他原因导致无法请求到数据',
      pkgLoading: '数据详情加载中',
      basic: '基本概览',
      data: '表格预览',
      column: '字段属性',
      desc: '数据包说明',
      genealogy: '血缘图',
      read: '使用权限',
      readDesc: '可使用此数据制作图表、地图',
      viewDetail: '检索权限',
      viewDetailDesc: '可对数据内容进行排序、筛选操作或在低码ETL中作为数据输入',
      download: '下载权限',
      downloadDesc: '可下载数据到本地',
      description: '描述:',
    },
    folder: {
      outermost: '最外层',
      home: '首页',
      create: '新建文件夹',
      createTip:
        '当您选择在最外层新建文件夹时，默认会在该位置创建一个新的文件夹。如果您选中了某个文件夹，则新建的文件夹将会在当前选中的文件夹下创建。',
      rename: '重命名文件夹',
      delete: '删除文件夹',
      deleteTip: '您确定要删除这个文件夹吗？请注意，文件夹内的资源将被释放到根目录下',
      deleteCheck: '勾选后，文件夹内的所有文件资源将一并被删除。',
      moveSuccess: '移动成功',
      createSuccess: '创建成功',
      deleteSuccess: '删除成功',
      moveToOutermost: '移动到最外层',
    },
    selectDatapkg: {
      appData: '本机构数据',
      personalData: '个人数据',
    },
    submitStyleConfig: {
      sizeTitle: '尺寸',
      fillTitle: '填充',
      borderTitle: '描边',
      fontTitle: '文字',
    },
    uploadExcelCheck: {
      column: '列',
      columnEmpty: (emptyColumns: string) => `第[${emptyColumns}]列不能为空`,
      columnNotExist: (notExistColumns: string) => `[${notExistColumns}]在模板中不存在, 请删除`,
      columnNotMatch: (notMatchColumns: string) => `${notMatchColumns}列与模板中对应列名称不相同`,
    },
    oneTableFormTypeItems: {
      normal: '短期报表',
      normalDesc: '短期任务或临时性任务。填报人员需要将数据收集完成后一次性提交给上级，上级可以审核下级的数据。',
      endless: '长期报表',
      endlessDesc:
        '长期任务，没有固定填报频次，需要随时常态化更新。填报人员可以随时持续地填报数据，可以多次同步数据给上级，无需上级审核。',
      periodic: '周期报表',
      periodicDesc:
        '有固定填报频次的周期性任务，如每月、每季度等。周期性报表任务会按照预定的时间频次重复出现，填报人员需要在每个周期内完成这项任务。',
    },
    apply: '申请',
    hasApply: '已获取',
    applying: '申请中',
    emptyContent: 'App不存在或未授权',
    qeBuilder: 'Qe编辑器',
    fillFormData: '表单填报',
    editFormData: '表单编辑',
    previousRow: '上一条',
    nextRow: '下一条',
    saveAndAdd: '保存并新增一行',
    doFinished: '填完了',
    notSaveTip: '表单有修改尚未保存',
    notSaveTipText: '检测到有未保存内容，是否保存当前页面的修改。',
    noSave: '不保存',
    saveedTip: '数据已保存',
    btnReverDel: '取消删除',
    displayByTable: '以表格形式展示',
    displayByTree: '以可折叠树展示',
    editRegeonTip: '已选择该行,请在右侧区域编辑.',
    editCurrentDataId: '正在编辑报表数据的ID:',
    previewCurrentDataId: '正在预览报表数据的ID:',
    closeForm: '关闭表单',
    h5Setting: {
      menu: '个人设置',
      hello: '你好',
      logout: '退出登录',
      userInfo: '用户信息',
      userName: '用户姓名',
      appName: '所属机构',
      changeIdentity: '切换身份',
      perferenceSetting: '偏好设置',
      darkMode: '深色模式',
      systemSetting: '系统功能',
    },
    h5Grid: {
      myNotify: '我的消息',
      myHandle: '待我处理',
      myStart: '我发起的',
    },
    bpmn: {
      executeTime: '执行时间',
      executeUserId: '执行人ID',
      globalVars: '系统全局变量',
      globalCustomVars: '自定义全局变量',
      nodeExecuteUserId: '节点执行人ID',
      nodeExecuteTime: '节点执行时间',
      startNode: '发起节点',
      inlineScript: '内联脚本',
      dynamicAssignees: '节点执行人动态设置',
      globalVariablesSetting: '全局变量赋值',
      globalVarSetting: '全局变量设置',
      globalVarCalResult: '全局变量计算结果',
    },
    ruleForm: {
      addCondition: '添加条件',
      addRule: '添加条件组',
      copy: '拷贝',
      delete: '删除',
      multipleEmpty: '+ 请输入查询内容',
    },
  },
};

export type Locale = typeof cn;
