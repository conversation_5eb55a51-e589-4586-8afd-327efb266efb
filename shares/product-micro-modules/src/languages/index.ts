import { createI18n, I18n } from '@i18n-chain/core';
import { LanguageEnum } from '@mdtBsComm/constants/enum';
import { cn, Locale } from './locales/cn';
import { en } from './locales/en';
export { cn, en };
export type { Locale };
import editorI18n from '@mdtProFormEditor/languages';
import proTasksI18n from '@mdtProTasks/languages';
import { addDefaultConfigLocale } from '../pages/form-editor/default-config';
import { addDataSourceTypeLocale } from '../pages/form-editor/shared';

const i18n = createI18n({ defaultLocale: { key: LanguageEnum.CN, values: cn } });
i18n.define(LanguageEnum.EN, en);

const addUtilLocale = () => {
  addDefaultConfigLocale();
  addDataSourceTypeLocale();
};

const originalLocale = i18n.locale.bind(i18n);
i18n.locale = async (lang: string) => {
  originalLocale(lang);
  addUtilLocale();
  proTasksI18n.locale(lang);
  editorI18n.locale(lang);
};
export default i18n as unknown as I18n<Locale, Locale>;
