import _ from 'lodash';
import { FC, useEffect, useState } from 'react';
import ArrowDownIcon from '@metro/icons/dist/esm/react/ChevronDown';
import ArrowUpIcon from '@metro/icons/dist/esm/react/ChevronUp';
import { Picker } from '@metro/mobile-components/dist/esm/picker';
import { Input } from '@metroDesign/input';
import { ILabelValue } from '@mdtBsComm/interfaces';
import i18n from '../../languages';
import './index.less';

export interface IMobileSelectProps {
  value?: string;
  onChange?: (val?: string) => void;
  options?: ILabelValue[];
  placeholder?: string;
}
export const MobileSelect: FC<IMobileSelectProps> = ({ value, onChange, options = [], placeholder }) => {
  const [display, setDisplay] = useState<string | undefined>();
  const [pickerVal, setPickerVal] = useState<string[]>();
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const o = _.find(options, ['value', value]);
    setDisplay(o?.label);
  }, [value, options]);

  const icon = visible ? <ArrowUpIcon /> : <ArrowDownIcon />;

  return (
    <>
      <Input
        className="mobile-select-input"
        value={display}
        placeholder={placeholder || i18n.chain.comPlaceholder.select}
        addonAfter={icon}
        readOnly
        onClick={() => {
          setPickerVal(value ? [] : [value!]);
          setVisible(true);
        }}
      />
      <Picker
        columns={[options]}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        value={pickerVal}
        onConfirm={(v: any) => {
          setPickerVal(v);
          const o = _.find(options, ['value', value]);
          setDisplay(o?.label);
          onChange?.(v[0]);
        }}
      />
    </>
  );
};
