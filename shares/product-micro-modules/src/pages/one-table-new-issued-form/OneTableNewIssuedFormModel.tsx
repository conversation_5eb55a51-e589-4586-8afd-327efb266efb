import _ from 'lodash';
import { toastApi } from '@metroDesign/toast';
import { patchDatapkgRowsAsync } from '@mdtBsServices/datapkgs';
import type { IServerResponse } from '@mdtBsServices/interfaces';
import { ColumnOperatorFilterEnum } from '@mdtProComm/constants';
import i18n from '../../languages';
import { checkRootDownstreamUsers, CommandEnum, doOneTableNewExcuteCommands } from '../../shared/onetablenew';
import {
  BooleanEnum,
  COLUMN_APPROVE_COMMENT,
  COLUMN_APPROVE_STATUS,
  COLUMN_ASSIGN_USER,
  COLUMN_STATUS,
  IssueDataFilterTypeEnum,
} from '../../utils/oneTableNewUtil';

export class OneTableNewIssuedFormModel {
  public static async issuedUsersToService(rootWfId: string, assignWfId: string, pkgId: string, data: any) {
    const existUser = await checkRootDownstreamUsers(rootWfId, data.users);
    if (existUser) {
      return false;
    }
    const success = await doOneTableNewExcuteCommands({
      root_workflow_id: rootWfId,
      commands: [{ command: CommandEnum.SEND_DOWN, users: data.postUsers }],
    });
    if (!success) {
      return false;
    }
    const resp2 = await this.updatePgkColumnAssignUser(pkgId, data);
    resp2 && toastApi.success(i18n.chain.comTip.optSuccess);
    return resp2;
  }

  public static async updatePgkColumnAssignUser(pkgId: string, data: any) {
    // 修改数据字段
    const assignUser = data.postUsers[0].primarys[0];
    if (!assignUser) {
      toastApi.success(i18n.chain.comTip.primaryEmpty);
      return false;
    }
    const values = {
      [COLUMN_ASSIGN_USER]: assignUser,
      [COLUMN_STATUS]: BooleanEnum.False, // 将下发数据设置为未提交
      [COLUMN_APPROVE_STATUS]: null, // 将审批状态重置
      [COLUMN_APPROVE_COMMENT]: null, // 将审批评论重置
    };
    const { filterType, operatorFilter, selected, range } = data;
    let resp: IServerResponse | undefined;
    if (filterType === IssueDataFilterTypeEnum.ALL) {
      const param = range ? { condition: range } : { update_all: true };
      resp = await patchDatapkgRowsAsync(pkgId, { values, ...param });
    } else if (filterType === IssueDataFilterTypeEnum.SELECTED) {
      const sf = { column: 'id', operator: ColumnOperatorFilterEnum.IN, param: _.map(selected, 'id') };
      const condition = range ? { $and: [range, sf] } : sf;
      resp = await patchDatapkgRowsAsync(pkgId, { values: values, condition });
    } else if (filterType === IssueDataFilterTypeEnum.FILTER) {
      const condition = range ? { $and: [range, operatorFilter] } : operatorFilter;
      resp = await patchDatapkgRowsAsync(pkgId, { values, condition });
    }
    return resp ? resp.success : true;
  }
}

export type IOneTableNewIssuedFormModel = typeof OneTableNewIssuedFormModel;
