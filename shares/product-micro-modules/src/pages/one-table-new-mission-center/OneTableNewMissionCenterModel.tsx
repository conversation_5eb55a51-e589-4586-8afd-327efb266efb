import _ from 'lodash';
import { from } from 'rxjs';
import { map, takeWhile } from 'rxjs/operators';
import type {
  IOnetableInvolvedForm,
  IOnetableInvolvedFormsQueryPost,
  IPaginationQuery,
  IRequestRequestConfig,
} from '@mdtApis/interfaces';
import { formateDateByUnix } from '@mdtBsComm/utils/dayUtil';
import { queryOnetableInvolvedFormsAsync, queryOnetableInvolvedFormsPaginationAsync } from '@mdtBsServices/flowork';
import { OneTableNewDataStateEnum } from '@mdtProComm/constants';
import { IOneTableNewCardComm, IOneTableNewOperatorDataComm } from '../../interfacts';
import {
  getBindPkgId,
  getFormOwner,
  getFrontendPersonalStatus,
  getPeriodicVersionName,
  getPrimaryAssignee,
  getVersionGroup,
  isCollaborateTask,
  isEndlessForm,
  isFormManageLevelUser,
  isNeedApproval,
  isPeriodicForm,
  TaskPersonalStatusEnum,
  transformFeatureFlagsToFrontEnd,
  transformPersonalStatusToFrontend,
} from '../../utils/oneTableNewUtil';

export interface ICardItemData extends IOneTableNewCardComm, IOneTableNewOperatorDataComm {
  status: TaskPersonalStatusEnum;
  approvalStatus: OneTableNewDataStateEnum;
  reason?: string;
}

export class OneTableNewMissionCenterModel {
  public static transformToCardData(item: IOnetableInvolvedForm, dataStatus: TaskPersonalStatusEnum): ICardItemData {
    const { bind_form: bf = {} } = item;
    const { id, name, extra_meta: extraMeta = {}, form_spec: formSpec } = bf;
    const assignWf = item.assign_workflow!;
    const data = assignWf.data || {};
    const rootWfId = item.root_workflow_id;
    const isCollaborate = isCollaborateTask(data);
    const [aps, reason] = transformPersonalStatusToFrontend(item, dataStatus);
    return {
      id: id,
      name: name,
      createTime: formateDateByUnix(assignWf.create_time, 0),
      isPeriodic: isPeriodicForm(extraMeta),
      isEndless: isEndlessForm(extraMeta),
      isCollaborate: isCollaborate,
      primaryAssignee: getPrimaryAssignee(assignWf.data),
      isFormManageLevelUser: isFormManageLevelUser(rootWfId, assignWf.parent_workflow_id),
      isNeedApproval: isNeedApproval(data),
      status: dataStatus,
      formId: id,
      formName: name,
      formSpec: formSpec,
      formOwner: getFormOwner(bf),
      rootWfId: rootWfId,
      assignWfId: item.assign_workflow_id,
      approvalStatus: aps,
      reason,
      pkgId: getBindPkgId(extraMeta),
      featureFlags: transformFeatureFlagsToFrontEnd(extraMeta),
      versionName: getPeriodicVersionName(item),
      versionGroup: getVersionGroup(item),
    };
  }

  public static queryItemById(wfId: string) {
    return from(
      queryOnetableInvolvedFormsAsync(
        { assign_workflow_id: wfId, with_form_info: true, with_assign_workflow_info: true },
        { quiet: true },
      ),
    ).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const it = (resp.data || [])[0];
        if (!it) return;
        const dataStatus = getFrontendPersonalStatus(it.onetable_personal_status!, it.assign_workflow?.status);
        return it ? this.transformToCardData(it, dataStatus) : undefined;
      }),
    );
  }

  // 获取首页数据
  public static queryFirstPage(
    dataStatus: TaskPersonalStatusEnum,
    data: IOnetableInvolvedFormsQueryPost,
    config?: IRequestRequestConfig<IPaginationQuery>,
  ) {
    return from(queryOnetableInvolvedFormsPaginationAsync(data, config)).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        const data = resp.data || { total_count: 0, dataResult: [] };
        return [data.total_count, _.map(data.dataResult, (it) => this.transformToCardData(it, dataStatus))] as [
          number,
          ICardItemData[],
        ];
      }),
    );
  }

  // 获取下一页flow列表
  public static queryNextPage(
    dataStatus: TaskPersonalStatusEnum,
    data: IOnetableInvolvedFormsQueryPost,
    config?: IRequestRequestConfig<IPaginationQuery>,
  ) {
    return from(queryOnetableInvolvedFormsAsync(data, config)).pipe(
      takeWhile((v) => !v.canceled),
      map((resp) => {
        return _.map(resp.data || [], (it) => this.transformToCardData(it, dataStatus));
      }),
    );
  }
}
export type IOneTableNewMissionCenterModel = typeof OneTableNewMissionCenterModel;
