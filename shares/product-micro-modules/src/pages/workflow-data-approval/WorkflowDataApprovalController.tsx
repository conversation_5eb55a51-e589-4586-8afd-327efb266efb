import { BehaviorSubject, Observable } from 'rxjs';
import { IFloworkTasksQuery, IIFloworkTaskType, IPaginationQuery } from '@mdtApis/interfaces';
import { RequestController } from '@mdtBsControllers/request-controller';
import { LinkButton } from '@mdtDesign/button';
import Tag from '@mdtDesign/tag';
import { WorkflowRoutePathEnum } from '@mdtProComm/constants/route';
import { isPc } from '@mdtProComm/utils/commonUtil';
import {
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '../../containers/table-curd-with-simple-search';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import { WORKFLOW_INFO } from '../../datlas/datlasConfig';
import i18n from '../../languages';
import { getWfQueryData } from '../workflow-application-list';
import { DrawerWorkflowDetailController } from '../workflow-detail';
import { ISearchBarData } from './CompSearchBar';
import { IWorkflowDataApprovalModel } from './WorkflowDataApprovalModel';

export enum ApprovalStatusEnum {
  TO_APPROVAL = 'ready',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  REJECTED = 'rejected', // status=completed && approval_result=rejected
}

export const statusOptions = [
  {
    label: i18n.chain.proMicroModules.workflow.status.pending,
    value: ApprovalStatusEnum.TO_APPROVAL,
  },
  {
    label: i18n.chain.proMicroModules.workflow.status.finished,
    value: ApprovalStatusEnum.COMPLETED,
  },
  {
    label: i18n.chain.proMicroModules.workflow.status.cancelled,
    value: ApprovalStatusEnum.CANCELLED,
  },
];

export const STATUS_LABEL_MAP: Record<ApprovalStatusEnum, [string, string]> = {
  [ApprovalStatusEnum.COMPLETED]: [i18n.chain.proMicroModules.workflow.status.finished, 'green-700'],
  [ApprovalStatusEnum.CANCELLED]: [i18n.chain.proMicroModules.workflow.status.cancelled, 'yellow-700'],
  [ApprovalStatusEnum.TO_APPROVAL]: [i18n.chain.proMicroModules.workflow.status.pending, 'gray-700'],
  [ApprovalStatusEnum.REJECTED]: [i18n.chain.proMicroModules.workflow.status.reject, 'red-700'],
};

export interface IApprovalTableData {
  status: string;
  workflowId: string;
  workflowName: string;
  workflowDesc?: string;
  wfSpecId: string;
  wfSpecName: string;
  applyTime: string;
  statusDisplay: string[];
  nodeId?: string;
  taskId?: string;
  taskType?: IIFloworkTaskType;
  completeTime?: string;
  userName: string;
  taskName: string;
}

export interface IQueryParams {
  status: string;
  time: string;
  page_size?: number;
  page_num?: number;
}

export interface IDataApprovalControllerOptions {
  Model: IWorkflowDataApprovalModel;
  showBottomMenu?: boolean;
}

export class WorkflowDataApprovalController extends RequestController {
  private Model: IWorkflowDataApprovalModel;
  private searchData$ = new BehaviorSubject<ISearchBarData>({});
  private tableController: TableCurdWithSimpleSearchController<IApprovalTableData>;
  private detailController = new DrawerWorkflowDetailController();

  public constructor(options: IDataApprovalControllerOptions) {
    super();
    this.checkNeedJumpH5();
    this.Model = options.Model;
    this.tableController = new TableCurdWithSimpleSearchController<IApprovalTableData>({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: () => this.queryTableData(),
          loadNextPageDataListFunc: (params) => this.queryNextTableData(params),
          getBackendFilterParams: () => this.getQueryParams(),
          equalItemKey: 'taskId',
        },
        tableOptions: () => this.initTableProps(),
        curdOptions: () => {
          return {
            otherBtns: (item: IApprovalTableData) => (
              <LinkButton onClick={() => this.handleShowDetail(item)}>
                {i18n.chain.proMicroModules.workflow.detail}
              </LinkButton>
            ),
          };
        },
      },
      headerOptions: this.loadHeaderOptions(),
    });
    this.tableController.listenBackendFilter(this.searchData$);
    this.tableController.loadDataList();
  }

  public destroy() {
    super.destroy();
    this.Model = null!;
    this.searchData$.complete();
    this.searchData$.next({});
    this.tableController.destroy();
    this.detailController.destroy();
  }

  public getDetailController() {
    return this.detailController;
  }

  public getApplicationListSingleUrl() {
    return DatlasAppController.getInstance().getRouterController().getDataApprovalSingleUrl();
  }

  public isSinglePage() {
    return (
      DatlasAppController.getInstance().getRouterController().getPathname() ===
      WorkflowRoutePathEnum.DATA_APPROVAL_SINGLE
    );
  }

  public getTableController() {
    return this.tableController;
  }

  public handleShowDetail(item: IApprovalTableData) {
    this.detailController.open({
      wfId: item.workflowId,
      wfSpecId: item.wfSpecId,
      userTaskXmlId: item.nodeId,
      userTaskId: item.taskId,
      onSuccessCb: () => {
        this.tableController.loadDataList();
      },
    });
  }

  public getSearchData$() {
    return this.searchData$;
  }

  public changeSearchData = (val: ISearchBarData) => {
    this.searchData$.next(val);
  };

  public querySpecList = () => {
    return this.Model.querySpecList();
  };

  private initTableProps = (): IVirtualizedTableProps => {
    return {
      columns: [
        { name: i18n.chain.proMicroModules.workflow.taskName, code: 'taskName', width: 150 },
        { name: i18n.chain.proMicroModules.workflow.wfName, code: 'workflowName', width: 200 },
        // { name: i18n.chain.proMicroModules.workflow.desc, code: 'workflowDesc', width: 150 },
        { name: i18n.chain.proMicroModules.workflow.wfSpecName, code: 'wfSpecName', width: 100 },
        // { name: i18n.chain.proMicroModules.workflow.userName, code: 'userName', width: 90 },
        { name: i18n.chain.proMicroModules.workflow.taskDate, code: 'applyTime', width: 150 },
        {
          name: i18n.chain.proMicroModules.workflow.taskStatus,
          code: 'statusDisplay',
          width: 100,
          lock: true,
          render: (status: any[]) => {
            return <Tag tag={status[0]} color={status[1]} />;
          },
        },
      ],
      type: 'page-bg',
      primaryKey: 'id',

      withVerticalBorder: false,
    };
  };

  private loadHeaderOptions() {
    return {
      createBtnLabel: '',
      createBtnProps: { style: { display: 'none' } },
      inputPlaceholder: i18n.chain.proMicroModules.workflow.searchResource,
      // title: i18n.chain.proMicroModules.workflow.menu.waitHandle,
    };
  }

  private queryTableData = (): Observable<[number, IApprovalTableData[]]> => {
    const query = this.getQueryParams();
    return this.Model!.queryFirstPageList(query.params, query.cancelToken);
  };

  private queryNextTableData = (pagination: IPaginationQuery): Observable<IApprovalTableData[]> => {
    const query = this.getQueryParams();
    return this.Model!.getNextPageList(query.params, pagination, query.cancelToken);
  };

  private getQueryParams = () => {
    const cancelToken = this.getCancelToken();
    const searchData = this.searchData$.getValue();
    const specId = WORKFLOW_INFO.limitSpec || searchData.specId;
    const params: IFloworkTasksQuery = {
      status: searchData.status ? searchData.status : undefined,
      create_time_min: searchData.createTimeMin,
      create_time_max: searchData.createTimeMax,
      with_data: true,
      assign_me: true,
      fuzzy_bpmn_name: searchData.taskName || undefined,
      fuzzy_workflow_name: searchData.wfName || undefined,
      workflow_spec_id: specId || undefined,
      workflow_data_query: getWfQueryData(searchData.searchKeyDataList),
      process_type: WORKFLOW_INFO.needOneTableWorkflowAndTask ? undefined : ['normal', 'approval', 'datapkg_form'],
    };
    return { cancelToken, params };
  };

  private checkNeedJumpH5() {
    if (this.isSinglePage() && !isPc()) {
      const app = DatlasAppController.getInstance();
      const url = app.getRouterController().getWorkflowDataApprovalH5Url();
      app.jumpToOtherProduct(url, {}, { replace: true });
    }
  }
}
