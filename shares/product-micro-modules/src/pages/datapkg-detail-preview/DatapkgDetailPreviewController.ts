import _ from 'lodash';
import { AsyncSubject, BehaviorSubject } from 'rxjs';
import { map, takeWhile, tap } from 'rxjs/operators';
import { ModalToggleFullScreenController } from '@mdtBsComponents/modal-toggle-fullscreen';
import { RequestController } from '@mdtBsControllers/request-controller';
import { SortTypeEnum } from '@mdtDesign/data-table';
import { IGenealogyNode } from '@mdtProComm/components/g6-genealogy';
import { DatapkgPermissionEnum } from '@mdtProComm/constants';
import { IDatapkgColumn, IDatapkgRowsQueryOrderBy, IRequestCancelToken } from '@mdtProComm/interfaces';
import { addCreateDownloadDatapkgTask } from '@mdtProTasks/util';
import {
  DataTablePkgPreviewController,
  FIT_CONTENT,
  IDataTableProps,
  IPaginationParams,
} from '../../containers/data-table-pkg-preview';
import { FilterListController } from '../../containers/filter-list';
import { IDownloadPkgOptions, PopoverPkgDownloadController } from '../../containers/popover-pkg-download';
import { TablePkgColumnController } from '../../containers/table-pkg-column';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import { IDatapkgDetailPreviewModel, IDatapkgInfo } from './DatapkgDetailPreviewModel';

export const MY_DATA_PAGE_INSTITUTION = 'self_institution_data';
export const MY_DATA_PAGE_OTHER_INSTITUTION = 'other_institution_data';

export const AnchorLinkTitleMap = {
  BASEINFO: i18n.chain.proMicroModules.pkg.basic,
  DATA: i18n.chain.proMicroModules.pkg.data,
  COLUMN: i18n.chain.proMicroModules.pkg.column,
  DESC: i18n.chain.proMicroModules.pkg.desc,
  GENEALOGY: i18n.chain.proMicroModules.pkg.genealogy,
};

const APPLY_ITEM_INFO_MAP: Record<string, { title: string; description: string; icon: string }> = {
  [DatapkgPermissionEnum.READ]: {
    title: i18n.chain.proMicroModules.pkg.read,
    description: i18n.chain.proMicroModules.pkg.readDesc,
    icon: 'visibility-on',
  },
  [DatapkgPermissionEnum.VIEW_DETAIL]: {
    title: i18n.chain.proMicroModules.pkg.viewDetail,
    description: i18n.chain.proMicroModules.pkg.viewDetailDesc,
    icon: 'search',
  },
  [DatapkgPermissionEnum.DOWNLOAD]: {
    title: i18n.chain.proMicroModules.pkg.download,
    description: i18n.chain.proMicroModules.pkg.downloadDesc,
    icon: 'download',
  },
};

export interface IApplyItemStatus {
  loading?: boolean;
  appling?: boolean;
}

export interface IControllerOptions {
  pkgId: string;
  Model: IDatapkgDetailPreviewModel;
}

export class DatapkgDetailPreviewController extends RequestController {
  private Model: IDatapkgDetailPreviewModel;
  private readonly pageLoading$ = new BehaviorSubject(false);
  private readonly isEmpty$ = new BehaviorSubject(true);
  private readonly showAllTags$ = new BehaviorSubject(false);
  private readonly dataPreviewTableProps$ = new AsyncSubject<IDataTableProps>();
  private dataSearchTableProps$?: AsyncSubject<IDataTableProps>;
  // 判断审批状态 在权限false的前提下 true->审核中 false->申请
  private readonly applyRead$ = new BehaviorSubject<IApplyItemStatus>({});
  private readonly applySearch$ = new BehaviorSubject<IApplyItemStatus>({});
  private readonly applyDownload$ = new BehaviorSubject<IApplyItemStatus>({});
  private readonly genealogyData$ = new BehaviorSubject<IGenealogyNode | null>(null);
  private columnController?: TablePkgColumnController<IDatapkgColumn>;
  private columnModalController?: ModalToggleFullScreenController;
  private dataPreviewController?: DataTablePkgPreviewController;
  private dataModalController?: ModalToggleFullScreenController;
  private descModalController?: ModalToggleFullScreenController;
  private genealogyModalController?: ModalToggleFullScreenController;
  private downloadController?: PopoverPkgDownloadController;
  private filterController?: FilterListController;
  private dataSearchController?: DataTablePkgPreviewController;
  private appName$?: BehaviorSubject<string>;
  private hasAllPermission?: boolean;
  private pkgInfo?: IDatapkgInfo;
  private pkgId: string;
  private app?: DatlasAppController<any, any, any>;
  private isSelfInstitution = false;
  private columns: IDatapkgColumn[] = [];
  private sorterItem?: IDatapkgRowsQueryOrderBy;

  public constructor(options: IControllerOptions) {
    super();
    this.app = DatlasAppController.getInstance();
    this.Model = options.Model;
    this.pkgId = options.pkgId;
    this.init();
  }

  public destroy() {
    super.destroy();
    this.pageLoading$.complete();
    this.isEmpty$.complete();
    this.showAllTags$.complete();
    this.dataPreviewTableProps$.complete();
    this.dataSearchTableProps$?.complete();
    this.dataSearchTableProps$ = undefined;
    this.applyRead$.complete();
    this.applySearch$.complete();
    this.applyDownload$.complete();
    this.genealogyData$.complete();
    this.columnController?.destroy();
    this.columnController = undefined;
    this.columnModalController?.destroy();
    this.columnModalController = undefined;
    this.dataPreviewController?.destroy();
    this.dataPreviewController = undefined;
    this.dataSearchController?.destroy();
    this.dataSearchController = undefined;
    this.filterController?.destroy();
    this.filterController = undefined;
    this.dataModalController?.destroy();
    this.dataModalController = undefined;
    this.descModalController?.destroy();
    this.descModalController = undefined;
    this.genealogyModalController?.destroy();
    this.genealogyModalController = undefined;
    this.downloadController?.destroy();
    this.downloadController = undefined;
    this.appName$?.complete();
    this.appName$ = undefined;
    this.pkgInfo = undefined;
    this.app = undefined;
  }

  public getIsSelfInstitution() {
    return this.isSelfInstitution;
  }

  public getDownloadController() {
    return this.downloadController!;
  }

  public getPageLoading$() {
    return this.pageLoading$;
  }

  public getShowAllTags$() {
    return this.showAllTags$;
  }

  public changeShowAllTags(showAll: boolean) {
    this.showAllTags$.next(showAll);
  }

  public getIsEmpty$() {
    return this.isEmpty$;
  }

  public getApplyRead$() {
    return this.applyRead$;
  }

  public getApplySearch$() {
    return this.applySearch$;
  }

  public getApplyDownload$() {
    return this.applyDownload$;
  }

  public getGenealogyData$() {
    return this.genealogyData$;
  }

  public checkEnableDataPreview() {
    return this.pkgInfo!.dataPreview;
  }

  public checkEnableDataSearch() {
    return this.pkgInfo!.dataSearch;
  }

  public checkEnableDataDownload() {
    return this.pkgInfo!.dataSearch && this.pkgInfo!.hasDownload;
  }

  public checkHasAllPermission() {
    return this.hasAllPermission;
  }

  public getUserApplyPermission() {
    return this.app!.getUserPermissionController().getDataSearchPermission();
  }

  public getPkgInfo() {
    return this.pkgInfo!;
  }

  public getAppName$() {
    return this.appName$!;
  }

  public getColumnController() {
    return this.columnController!;
  }

  public getColumnModalController() {
    return this.columnModalController!;
  }

  public getDataController() {
    return this.dataPreviewController!;
  }

  public getDataFilterController() {
    return this.dataSearchController!;
  }

  public getFilterController() {
    return this.filterController!;
  }

  public getDataModalController() {
    return this.dataModalController!;
  }

  public getDescModalController() {
    return this.descModalController!;
  }

  public getGenealogyModalController() {
    return this.genealogyModalController!;
  }

  public getBaseInfoAnchorId() {
    return `baseInfo-${this.pkgId}`;
  }

  public getDataOverviewAnchorId() {
    return `dataOverview-${this.pkgId}`;
  }

  public getColumnOverviewAnchorId() {
    return `columnOverview-${this.pkgId}`;
  }

  public getDescAnchorId() {
    return `description-${this.pkgId}`;
  }

  public getGenealogyAnchorId() {
    return `genealogy-${this.pkgId}`;
  }

  public getScrollWrapId() {
    return `scrollWrap-${this.pkgId}`;
  }

  public getDatapkgShareUrl() {
    return this.app!.getRouterController().getDatapkgShareUrl(this.pkgId);
  }

  public goToSearchData = () => {
    this.dataModalController?.toggleModal();
  };

  public downloadDataPkg = async ({
    downloadType,
    exportGeoField,
    geoFormat,
    columns,
    column_mapping,
  }: IDownloadPkgOptions) => {
    const pkgName = this.pkgInfo!.name;
    addCreateDownloadDatapkgTask(this.pkgId, pkgName, {
      file_type: downloadType,
      geo: exportGeoField,
      geometry_format: geoFormat,
      only: columns,
      column_mapping,
    });
    return true;
  };

  public getApplyItemInfo(applyType: string, hasPermission?: boolean, applying?: boolean) {
    const info = APPLY_ITEM_INFO_MAP[applyType];
    const buttonLabel = hasPermission
      ? i18n.chain.proMicroModules.hasApply
      : applying
      ? i18n.chain.proMicroModules.applying
      : i18n.chain.proMicroModules.apply;
    return {
      ...info,
      applyType,
      buttonLabel,
      disabled: hasPermission || applying,
    };
  }

  public applyDatapkgPermission(permission: string, status: BehaviorSubject<IApplyItemStatus>) {
    status.next({ ...status.getValue(), loading: true });
    this.Model.applyDatapkgPermission(this.pkgInfo!, permission as 'read').subscribe((v) =>
      status.next({ loading: false, appling: v }),
    );
  }

  private loadColumns = (cancelToken?: IRequestCancelToken) => {
    return this.Model.getDatapkgColumns(this.pkgId, cancelToken).pipe(
      tap((v) => {
        const dc = this.dataPreviewController!;
        dc ? dc.defaultTableProps(v) : this.dataPreviewTableProps$.complete();

        this.columns = v;
        const ds = this.dataSearchController;
        if (ds) {
          this.initDataSearchColumns();
          this.filterController?.init(v);
        }
      }),
    );
  };

  private initDataSearchColumns() {
    this.dataSearchTableProps$ = new AsyncSubject<IDataTableProps>();
    const sortColumns = _.map(this.columns, (c) => ({
      ...c,
      sortType: this.getColumnSortType(c.name),
      sorter: this.pkgInfo?.dataSearch ? this.getSorter(c.name) : undefined,
    }));
    this.dataSearchController!.defaultTableProps(sortColumns);
  }

  private getColumnSortType = (name: string) => {
    if (!this.sorterItem || this.sorterItem.field !== name) return SortTypeEnum.UNSET;
    if (this.sorterItem.asc) return SortTypeEnum.ASCEND;
    return SortTypeEnum.DESCEND;
  };

  private getSorter = (name: string) => {
    return (type: SortTypeEnum) => {
      if (SortTypeEnum.ASCEND === type) {
        this.sorterItem = {
          asc: true,
          field: name,
        };
      } else if (SortTypeEnum.DESCEND === type) {
        this.sorterItem = {
          asc: false,
          field: name,
        };
      } else {
        this.sorterItem = undefined;
      }
      this.initDataSearchColumns();
      this.dataSearchController!.loadDataList();
    };
  };

  private loadPreviewData = (cancelToken?: IRequestCancelToken) => {
    return this.Model.getDatapkgPreview(this.pkgId, cancelToken);
  };

  private loadFirstPageData() {
    return this.Model.getDatapkgDataListFirstPage(this.pkgId, this.getFilterParams());
  }

  private loadNextPageData(params: IPaginationParams) {
    return this.Model.getDatapkgDataListNextPage(this.pkgId, {
      ...params,
      ...this.getFilterParams(),
    });
  }

  private getFilterParams() {
    const filters = this.filterController?.getFilterList$().getValue();
    return {
      condition: _.size(filters) ? filters : undefined,
      orderby: this.sorterItem ? [this.sorterItem] : undefined,
    };
  }

  // 请求数据
  private init() {
    this.pageLoading$.next(true);
    const app = this.app!;
    const cancelToken = this.getCancelToken();
    const appId = app.getAppId();
    this.Model.getDatapkg(this.pkgId, cancelToken, `${appId}`)
      .pipe(
        tap((v) => {
          this.pageLoading$.next(false);
          this.isEmpty$.next(!v);
        }),
        takeWhile((v) => !!v),
        map((v) => v!),
      )
      .subscribe((v) => {
        this.pkgInfo = v;
        this.appName$ = v.appName ? new BehaviorSubject(v.appName) : app.getNameCacheController()!.getAppName$(v.appId);
        // 如果可以预览，则初始化
        if (v.dataPreview) {
          // 数据预览
          this.dataPreviewController = new DataTablePkgPreviewController({
            loadPkgPreviewFunc: this.loadPreviewData,
            tableOptions: () => this.dataPreviewTableProps$,
            gridHeight: FIT_CONTENT,
          });
          this.dataPreviewController.loadDataList(cancelToken);
          // 数据预览弹窗
          this.dataModalController = new ModalToggleFullScreenController({
            uiOptions: { headerTitle: AnchorLinkTitleMap.DATA },
          });
          if (this.pkgInfo.hasSearch && this.checkEnableDataSearch()) {
            this.filterController = new FilterListController();
            this.dataSearchController = new DataTablePkgPreviewController({
              loadPkgPreviewFunc: () => this.loadFirstPageData(),
              loadNextPagePkgPreviewFunc: (params: IPaginationParams) => this.loadNextPageData(params),
              tableOptions: () => this.dataSearchTableProps$!,
              getBackendFilterParams: () => this.getFilterParams(),
            });
            // this.dataFilterController.loadDataList();
            this.dataSearchController.listenBackendFilter(this.filterController.getFilterList$());
          }
        }
        // 列表预览
        this.columnController = new TablePkgColumnController({
          loadColumnsFunc: this.loadColumns,
        });
        this.columnController.loadDataList(cancelToken);
        // 列表弹窗
        this.columnModalController = new ModalToggleFullScreenController({
          uiOptions: { headerTitle: AnchorLinkTitleMap.COLUMN },
        });
        // 数据审批状态
        this.hasAllPermission = !!(v.hasRead && v.hasDownload && v.hasSearch);
        if (!this.hasAllPermission) {
          // 如果没有部分权限，查询是否有申请中
          const userId = app.getUserId();
          this.Model.queryDatapkgApplyStatus(this.pkgId, userId, cancelToken).subscribe((v) => {
            this.applyRead$.next({ appling: v.applyingRead });
            this.applySearch$.next({ appling: v.applyingSearch });
            this.applyDownload$.next({ appling: v.applyingDownload });
          });
        }
        if (this.checkEnableDataDownload()) {
          this.downloadController = new PopoverPkgDownloadController({
            downloadPkgFunc: this.downloadDataPkg,
            loadColumnOptionsFunc: async () => this.columns,
          });
        }

        this.isSelfInstitution = appId === this.pkgInfo.appId;
        if (!this.isSelfInstitution) return;

        // 数据包说明弹窗
        this.descModalController = new ModalToggleFullScreenController({
          uiOptions: { headerTitle: AnchorLinkTitleMap.DESC },
        });

        this.Model.queryDatapkgGenealogys(this.pkgId).subscribe((data) => {
          // 血缘图弹窗
          this.genealogyModalController = new ModalToggleFullScreenController({
            uiOptions: { headerTitle: AnchorLinkTitleMap.GENEALOGY },
          });
          this.genealogyData$.next(data);
        });
      });
  }
}
