import { Observable } from 'rxjs';
import { ICurdOptions } from '@mdtBsComponents/data-list-comp-table-curd';
import { IArticlesQuery, IBusinessResult, ILabelValue, IQueryFolders } from '@mdtProComm/interfaces';
import { IFormData } from '@mdtProTasks/create-template-from-upload-local-file-task/dialog-modify-form-md-template';
import { IAssignFormData, IUiData } from '../../containers/dialog-form-template-to-datapkg';
import { IFolders } from '../../models/FolderModel';

export interface ITableData extends IFormData {
  id: string;
  contentType: string;
  version: string;
  revision: number;
  updateTime: number;
  updateTimeDisplay: string;
}

export interface ITableMdTemplateModel {
  // 查询权限
  queryPermissions: () => ICurdOptions<ITableData>;

  // 查询模板列表
  queryMdTemplateList: (params?: IArticlesQuery) => Observable<ITableData[]>;

  // 查询模板列表
  queryMdTemplateFolderList: (articleParams: IArticlesQuery, folderParams: IQueryFolders) => Observable<ITableData[]>;

  // 转换文件函数
  transformFolderToTableData: (item: IFolders) => ITableData;

  // 创建模板
  createMdTemplate: (createData: IFormData) => Observable<IBusinessResult<ITableData>>;

  // 修改模板
  updateMdTemplate: (updateData: IFormData, originalUpdateData: ITableData) => Observable<IBusinessResult<ITableData>>;

  // 删除模板
  deleteMdTemplate: (deleteData: ITableData) => Observable<IBusinessResult<ITableData>>;

  // 加载分配弹窗ui数据
  loadAssignUiData: () => Observable<IUiData>;

  // 查询数据包
  loadPkgOptionsFunc: (sourceId: string, search?: string) => Observable<ILabelValue[]>;

  // 模板分配数据包
  assignTemplateToPkg: (formData: IAssignFormData, tableData: ITableData) => Observable<IBusinessResult<ITableData>>;
}
