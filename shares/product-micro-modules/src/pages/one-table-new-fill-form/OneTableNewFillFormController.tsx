import _ from 'lodash';
import { createRef } from 'react';
import { toJS } from '@formily/reactive';
import { Download } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Divider } from '@metroDesign/divider';
import { toastApi } from '@metroDesign/toast';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, skip } from 'rxjs/operators';
import { IPaginationParams } from '@mdtBsControllers/data-list-controller';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IFormSpecRefHandle } from '../../components/form-view';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import type { IOneTableNewOperatorDataComm } from '../../interfacts';
import i18n from '../../languages';
import { DataTableActionEnum } from '../../pages/one-table-new-data-table';
import { doOneTableNewOperatorBatchFillForm, doOneTableNewOperatorSubmitTask } from '../../shared/onetablenew';
import {
  BooleanEnum,
  COLUMN_FLAG,
  DataTableTypeEnum,
  getProcessRowsOptions,
  processRows,
} from '../../utils/oneTableNewUtil';
import { OneTableNewFillFormDataTableController } from './OneTableNewFillFormDataTableController';
import { IOneTableNewFillFormModel } from './OneTableNewFillFormModel';

export interface ISubmitConfirmOptions {
  onlySaveCallBack: () => void;
  submitSuccessCallback: () => void;
  deleteDataCallback?: () => void;
}

export interface IControllerOptions {
  Model: IOneTableNewFillFormModel;
  itemData: IOneTableNewOperatorDataComm;
  submitConfirmOptionsFunc: (...args: any[]) => ISubmitConfirmOptions;
  onlyForm?: boolean; // 只展示填报表单
  defaultValue?: Record<string, any>; // 展示当前填报的数据
  paginationParams?: IPaginationParams;
  requestParams?: Record<string, any>;
}

interface ISaveResult {
  noChanged?: boolean;
  row?: Record<string, any>;
}

export interface IBlockUi {
  loading: boolean;
  callback?: () => void;
}

export class OneTableNewFillFormController extends RequestController {
  private Model: IOneTableNewFillFormModel;
  private itemData: IOneTableNewOperatorDataComm;
  private dataStatus$ = new BehaviorSubject<string[]>([]);
  // 当前编辑第几行
  private currendIndex$ = new BehaviorSubject(-1);
  private blockUi$ = new BehaviorSubject<IBlockUi>({ loading: false });
  // 是否有下一条
  private hasNext$ = new BehaviorSubject(false);
  // 是否有上一条
  private hasPre$ = new BehaviorSubject(false);
  // 数据源数据(目前先一次性加载1w条数据)
  private dataTableController: OneTableNewFillFormDataTableController;
  // 表单索引
  private formRef = createRef<IFormSpecRefHandle>();
  // 表单Id
  private formProps$: BehaviorSubject<Record<string, any>>;
  // 当前表单值
  private currentFormValues$ = new BehaviorSubject<Record<string, any>>({});
  private orginFormValues$ = new BehaviorSubject<Record<string, any>>({});
  private submitConfirmOptionsFunc?: () => ISubmitConfirmOptions;
  private showCompleteBtn?: boolean;
  private isFormManageLevelUser: boolean;
  private onlyForm?: boolean;
  private isSaving$ = new BehaviorSubject<boolean>(false);

  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    const { itemData } = options;
    this.itemData = itemData;
    this.onlyForm = options.onlyForm;
    this.submitConfirmOptionsFunc = options.submitConfirmOptionsFunc;
    const { isFormManageLevelUser, formOwner } = itemData;
    this.formProps$ = new BehaviorSubject<Record<string, any>>({
      ...itemData.formSpec,
      // 创建者不需要代理
      owner: DatlasAppController.getInstance().getUserId() !== formOwner.userId ? formOwner : undefined,
    });
    this.showCompleteBtn = isFormManageLevelUser ? false : true;
    this.isFormManageLevelUser = isFormManageLevelUser;
    const controller = new OneTableNewFillFormDataTableController({
      dataTableType: DataTableTypeEnum.FILL,
      itemData: itemData,
      modifyItemData: this.innerModifyItemData,
      requestParams: options.requestParams,
      dataPreviewOptions: {
        initialPageNum: options.paginationParams?.page_num,
        pkgId: itemData.pkgId,
        renderOtherHeaderBtns: () => {
          return (
            <>
              <Divider type="vertical" style={{ marginLeft: 16 }} />
              <Button size="small" icon={<Download />} onClick={this.openBatchDialog}>
                {i18n.chain.proMicroModules.oneTable.btnBatchFill}
              </Button>
            </>
          );
        },
      },
    });
    this.dataTableController = controller;
    this.initReletion(options.defaultValue);
  }

  public destroy() {
    super.destroy();
    this.submitConfirmOptionsFunc = undefined;
    this.formRef = null!;
    this.itemData = null!;
    this.blockUi$.complete();
    this.blockUi$.next({ loading: false });
    this.dataStatus$.complete();
    this.currendIndex$.complete();
    this.hasNext$.complete();
    this.hasPre$.complete();
    this.dataTableController.destroy();
    this.dataStatus$.complete();
    this.formProps$.complete();
    this.formProps$.next({});
    this.currentFormValues$.complete();
    this.currentFormValues$.next({});
    this.orginFormValues$.complete();
    this.orginFormValues$.next({});
    this.isSaving$.complete();
  }

  public getItemData() {
    return this.itemData;
  }

  public getOnlyForm() {
    return this.onlyForm;
  }

  public getShowCompleteBtn() {
    return this.showCompleteBtn;
  }

  public getIsSaving$() {
    return this.isSaving$;
  }

  public initOrginFormValue = (values: Record<string, any>) => {
    const val = toJS(values);
    this.orginFormValues$.next(val);
    this.currentFormValues$.next(val);
  };

  public changeFormValues = (values: Record<string, any>) => {
    this.currentFormValues$.next(toJS(values));
  };

  public getFormRef() {
    return this.formRef;
  }

  public getBlockUi$() {
    return this.blockUi$;
  }

  public getCurrentEditData$() {
    return this.orginFormValues$;
  }

  public getCurrentIndex$() {
    return this.currendIndex$;
  }

  public getFormProps$() {
    return this.formProps$;
  }

  public getDataStatus$() {
    return this.dataStatus$;
  }

  public getHasNext$() {
    return this.hasNext$;
  }

  public getHasPre$() {
    return this.hasPre$;
  }

  public getDataTableController() {
    return this.dataTableController;
  }

  public editPreData() {
    const index = this.currendIndex$.getValue() - 1;
    const tc = this.dataTableController;
    const nowPage = tc.getCurrentPageValue();
    const pageSize = tc.getPageSize();
    // 符合切换到上一页
    if (index === -1 && nowPage > 0) {
      this.blockUi$.next({
        loading: true,
        callback: () => {
          this.changeFormValueToNextIndex(pageSize - 1);
        },
      });
      tc.clickPagination(nowPage - 1);
    } else {
      this.changeFormValueToNextIndex(index);
    }
  }

  public async saveEditPreData() {
    await this.saveFormData();
    this.editPreData();
  }

  public editNextData() {
    const index = this.currendIndex$.getValue() + 1;
    const tc = this.dataTableController;
    const nowTotal = tc.getDataListValue().length;
    const hasNextPageValue = tc.getHasNextPageValue();
    // 符合切换到下一页
    if (index >= nowTotal && hasNextPageValue) {
      this.blockUi$.next({
        loading: true,
        callback: () => {
          this.changeFormValueToNextIndex(0);
        },
      });
      tc.clickPagination(tc.getCurrentPageValue() + 1);
    } else {
      this.changeFormValueToNextIndex(index);
    }
  }

  public async saveEditNextData() {
    await this.saveFormData();
    this.editNextData();
  }

  public openBatchDialog = () => {
    doOneTableNewOperatorBatchFillForm({
      itemData: this.itemData,
      tableColumns: this.dataTableController.getOriginTableColumns(),
      onBatchSuccess: () => {
        this.dataTableController.loadDataList();
      },
    });
  };

  public doFinished = async () => {
    if (this.isSaving$.getValue()) return;

    const status = this.dataStatus$.getValue();
    if (!_.isEmpty(status) && status[0] === 'error') {
      toastApi.error(status[1]);
      return;
    }

    doOneTableNewOperatorSubmitTask({
      itemData: this.itemData,
      showSubmitBtn: true,
      showBackBtn: true,
      showSaveBtn: true,
      ...(this.submitConfirmOptionsFunc?.() || {}),
    });
  };

  public saveFormData = async (resetForm?: boolean) => {
    if (this.isSaving$.getValue()) return;

    try {
      this.isSaving$.next(true);
      this.dataStatus$.next(['processing', i18n.chain.proMicroModules.datapkg.saving]);

      const values = await this.formRef.current?.getValues();
      if (!values) {
        this.updateDataStatus();
        return;
      }

      const result = values.id ? await this.updateRow(values) : await this.addNewRow(values);

      if (!result.row) {
        result.noChanged && toastApi.warning(i18n.chain.comTip.formNoChanged);
        this.updateDataStatus();
        return;
      }

      // 更新表单数据和状态
      !resetForm && this.updateFormData(result.row);
      this.dataStatus$.next(['success', i18n.chain.proMicroModules.saveedTip]);

      // 处理重置表单
      if (resetForm) {
        this.createNewFormView({});
        toastApi.success(i18n.chain.comTip.optSuccess);
      }
    } catch (error) {
      this.dataStatus$.next(['error', i18n.chain.proMicroModules.notSaveTip]);
    } finally {
      this.isSaving$.next(false);
    }
  };

  public deleteData = async () => {
    if (this.isSaving$.getValue()) return;

    const current = this.currentFormValues$.getValue();
    const enableDelete = this.currendIndex$.getValue() > -1;
    if (enableDelete) {
      await this.innerModifyItemData(current, DataTableActionEnum.Delete);
      this.submitConfirmOptionsFunc?.()?.deleteDataCallback?.();
    }
  };

  // 保存数据到服务器
  private async saveData(values: Record<string, any>): Promise<ISaveResult> {
    return values.id ? this.updateRow(values) : this.addNewRow(values);
  }

  private async updateRow(row: Record<string, any>) {
    const result: ISaveResult = {};
    const { updateRows } = processRows(
      [this.orginFormValues$.getValue()],
      [row],
      getProcessRowsOptions(this.isFormManageLevelUser),
    );
    if (!updateRows.length) {
      result.noChanged = true;
      return result;
    }
    const updateRow = await this.Model.updateRow(this.itemData.pkgId, updateRows[0]);
    if (!updateRow) return result;
    this.dataTableController.editDataInList(updateRow);
    result.row = updateRow;
    return result;
  }

  private async addNewRow(row: Record<string, any>) {
    const { addRows } = processRows([], [row], getProcessRowsOptions(this.isFormManageLevelUser));
    const result: ISaveResult = {};
    if (!addRows.values.length) {
      result.noChanged = true;
      return result;
    }
    const newRow = await this.Model.addRow(this.itemData.pkgId, addRows);
    if (!newRow) return result;
    const addIndex = this.getAddindex();
    this.currendIndex$.next(addIndex);
    this.dataTableController.addDataToListByIndex(newRow, addIndex, true);
    result.row = newRow;
    return result;
  }

  private updateFormData(formData: Record<string, any>) {
    this.orginFormValues$.next(formData);
    this.currentFormValues$.next(formData);
    this.formRef.current!.setValues(formData);
  }

  private getAddindex() {
    const index = this.currendIndex$.getValue();
    return index < 0 ? 0 : index;
  }

  private changeFormValueToNextIndex(ntIndex: number) {
    const listData = this.dataTableController.getDataListValue();
    let newIndex = Math.max(0, ntIndex);
    newIndex = Math.min(newIndex, listData.length - 1);
    const formData = listData[newIndex];
    if (_.isEqual(formData.id, this.currentFormValues$.getValue().id)) return;
    this.currendIndex$.next(newIndex);
    this.createNewFormView(formData);
  }

  private createNewFormView(formData: Record<string, any>) {
    this.orginFormValues$.next(formData);
    this.currentFormValues$.next(formData);
    const oldProps = this.formProps$.getValue();
    this.formProps$.next({ ...oldProps, formData, key: Math.random() });
  }

  private innerModifyItemData = async (item: Record<string, any>, action: string, noToast = false) => {
    if (action === DataTableActionEnum.Edit) {
      const index = _.findIndex(this.dataTableController.getDataListValue(), { id: item.id });
      this.changeFormValueToNextIndex(index);
      !noToast && toastApi.success(i18n.chain.proMicroModules.editRegeonTip);
      return;
    }
    const flag = DataTableActionEnum.Delete === action ? BooleanEnum.True : BooleanEnum.False;
    const { updateRows } = processRows(
      [item],
      [{ ...item, [COLUMN_FLAG]: flag }],
      getProcessRowsOptions(this.isFormManageLevelUser, { ignoreUpdateRowActionStatus: true }),
    );
    const updateRow = await this.Model.updateRow(this.itemData.pkgId, updateRows[0]);
    if (!updateRow) return;
    toastApi.success(i18n.chain.comTip.optSuccess);
    this.dataTableController.editDataInList(updateRow);
  };

  private initReletion(value?: Record<string, any>) {
    this.currentFormValues$.pipe(debounceTime(300)).subscribe(() => {
      if (!this.isSaving$.getValue()) {
        this.updateDataStatus();
      }
    });

    const tableCtrl = this.dataTableController;
    tableCtrl
      .getDataListLoading$()
      .pipe(skip(1))
      .subscribe((loading) => {
        if (!loading && value) {
          this.innerModifyItemData(value, DataTableActionEnum.Edit, true);
          return;
        }
        loading && this.currendIndex$.next(-1);
      });
    tableCtrl
      .getLoadingNext$()
      .pipe(skip(1))
      .subscribe((loading) => {
        const block = this.blockUi$.getValue();
        if (!loading && block.loading && block.callback) {
          block.callback();
          this.blockUi$.next({ loading: false });
        }
      });
    const pageSize = tableCtrl.getPageSize();
    combineLatest([this.currendIndex$, tableCtrl.getCurrentPage$(), tableCtrl.getPageTotal$()]).subscribe(
      ([index, currentPage, total]) => {
        const realIndex = currentPage * pageSize + index;
        this.hasPre$.next(realIndex > 0);
        this.hasNext$.next(realIndex + 1 < total);
      },
    );
    // 监听列表高亮改色
    this.orginFormValues$.subscribe((value) => {
      tableCtrl.setHighlightRows(value.id ? [value.id] : []);
    });
  }

  private updateDataStatus() {
    const currentValues = this.currentFormValues$.getValue();
    const originValues = this.orginFormValues$.getValue();

    if (!_.isEqualWith(originValues, currentValues, customizer)) {
      this.dataStatus$.next(['error', i18n.chain.proMicroModules.notSaveTip]);
    } else {
      this.dataStatus$.next([]);
    }
  }
}

function formatterVal(val: any) {
  if (!_.isPlainObject(val)) return val;
  let rslt: any = {};
  let hasNil = false;
  _.forEach(val, (kv, key) => {
    if (
      kv === undefined ||
      kv === '' ||
      kv === false ||
      kv === null ||
      ((_.isArray(kv) || _.isObjectLike(kv)) && _.isEmpty(kv))
    ) {
      hasNil = true;
    } else {
      rslt[key] = kv;
    }
  });
  return hasNil ? rslt : val;
}

// 自定义比较器函数
function customizer(objValue: any, othValue: any) {
  const fobj = formatterVal(objValue);
  const foth = formatterVal(othValue);
  if (objValue === fobj && othValue === foth) {
    return _.isEqual(fobj, foth);
  }
  return _.isEqualWith(fobj, foth, customizer);
}
