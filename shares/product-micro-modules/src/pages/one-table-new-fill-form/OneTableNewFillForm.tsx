import { FC } from 'react';
import { onFormMount } from '@formily/core';
import { ChevronDown, ChevronUp } from '@metro/icons';
import { Badge } from '@metroDesign/badge';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Col, Row } from '@metroDesign/grid';
import { type PopconfirmProps, Popconfirm } from '@metroDesign/popconfirm';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Spin } from '@metroDesign/spin';
import classnames from 'classnames';
import { LoadingWrapper } from '@mdtBsComm/components/loading-wrapper';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { FormView } from '../../components/form-view';
import i18n from '../../languages';
import { getDataStatusTagsComm } from '../../utils/oneTableNewUtil';
import { OneTableNewDataTableStatics } from '../one-table-new-data-table-statics';
import { OneTableNewFillFormController } from './OneTableNewFillFormController';
import './index.less';

interface IProps {
  controller: OneTableNewFillFormController;
}

const TableBlock: FC<IProps> = ({ controller }) => {
  return (
    <Col className="table-block" flex="auto">
      <OneTableNewDataTableStatics controller={controller.getDataTableController()} />
    </Col>
  );
};

const FormBlockHeader: FC<IProps> = ({ controller }) => {
  const dataStatus = useObservableState(controller.getDataStatus$());
  const hasPre = useObservableState(controller.getHasPre$());
  const hasNext = useObservableState(controller.getHasNext$());
  const { id: currentId } = useObservableState(controller.getCurrentEditData$());
  const onlyForm = controller.getOnlyForm();

  const dsEle = dataStatus.length ? <Badge status={dataStatus[0] as 'default'} text={dataStatus[1]} /> : null;
  const editing = dataStatus?.[0] === 'error';

  const commonPopProps: PopconfirmProps = {
    title: i18n.chain.proMicroModules.notSaveTip,
    description: i18n.chain.proMicroModules.notSaveTipText,
    cancelText: i18n.chain.proMicroModules.noSave,
    okText: i18n.chain.comButton.save,
    type: 'warning',
    needConfirm: editing,
    noConfirmAction: 'onCancel',
  };

  const dt = currentId ? (
    <div className="data-tip">
      {i18n.chain.proMicroModules.editCurrentDataId}
      <span>{currentId}</span>
    </div>
  ) : null;

  return (
    <div className="header">
      <div className="title">{i18n.chain.proMicroModules.fillFormData}</div>
      {dt}
      <div
        className={classnames('tip', {
          'tip-only-form': onlyForm,
        })}
      >
        {dsEle}
      </div>
      <div>
        <Popconfirm
          {...commonPopProps}
          disabled={!hasPre}
          onCancel={() => controller.editPreData()}
          onConfirm={async () => controller.saveEditPreData()}
        >
          <Button ghost icon={<ChevronUp />} disabled={!hasPre}>
            {i18n.chain.proMicroModules.previousRow}
          </Button>
        </Popconfirm>
        <Popconfirm
          {...commonPopProps}
          disabled={!hasNext}
          onCancel={() => controller.editNextData()}
          onConfirm={async () => controller.saveEditNextData()}
        >
          <Button ghost icon={<ChevronDown />} disabled={!hasNext}>
            {i18n.chain.proMicroModules.nextRow}
          </Button>
        </Popconfirm>
      </div>
    </div>
  );
};

const FormBlockForm: FC<IProps> = ({ controller }) => {
  const formProps = useObservableState(controller.getFormProps$());
  const onlyForm = controller.getOnlyForm();

  return (
    <div className={classnames('form', { 'only-form': onlyForm })}>
      <Scrollbar style={{ height: '100%' }}>
        <FormView
          {...formProps}
          effects={() => {
            onFormMount((form) => {
              controller.initOrginFormValue(form.values);
            });
          }}
          ref={controller.getFormRef()}
          onChange={controller.changeFormValues}
        />
      </Scrollbar>
    </div>
  );
};

const FormFooter: FC<IProps> = ({ controller }) => {
  const cur = useObservableState(controller.getCurrentEditData$());
  const status = getDataStatusTagsComm(cur)[0];
  const curIndex = useObservableState(controller.getCurrentIndex$());
  const onlyForm = controller.getOnlyForm();
  const enableDelete = onlyForm && curIndex > -1 && !(status?.length && status[1] === 'error');
  const isSaving = useObservableState(controller.getIsSaving$());

  const completeBtnEle = controller.getShowCompleteBtn() ? (
    <Button onClick={controller.doFinished} disabled={isSaving}>
      {i18n.chain.proMicroModules.doFinished}
    </Button>
  ) : null;

  const deleteBenEle = enableDelete ? (
    <Popconfirm
      title={i18n.chain.proMicroModules.oneTable.tip.deleteData}
      description={i18n.chain.proMicroModules.oneTable.tip.confirmDeleteData}
      type="error"
      onConfirm={controller.deleteData}
    >
      <Button danger disabled={isSaving}>
        {i18n.chain.comButton.delete}
      </Button>
    </Popconfirm>
  ) : null;

  return (
    <LoadingWrapper>
      <Flex className="footer" justify="flex-end" gap="small">
        {deleteBenEle}
        <Button type="primary" onClick={async () => controller.saveFormData()} disabled={isSaving}>
          {i18n.chain.comButton.save}
        </Button>
        <Button onClick={async () => controller.saveFormData(true)} disabled={isSaving}>
          {i18n.chain.proMicroModules.saveAndAdd}
        </Button>
        {completeBtnEle}
      </Flex>
    </LoadingWrapper>
  );
};

const FormBlock: FC<IProps> = ({ controller }) => {
  const flex = controller.getOnlyForm() ? undefined : '40%';
  return (
    <Col className="form-block" flex={flex}>
      <FormBlockHeader controller={controller} />
      <FormBlockForm controller={controller} />
      <FormFooter controller={controller} />
    </Col>
  );
};

const LoadingBlock: FC<IProps> = ({ controller }) => {
  const block = useObservableState(controller.getBlockUi$());
  return block.loading ? <Spin className="block-ui" spinning brand /> : null;
};

export const OneTableNewFillForm: FC<IProps> = ({ controller }) => {
  const loading = useObservableState(controller.getDataTableController().getDataListLoading$());
  const onlyForm = controller.getOnlyForm();

  const View = (
    <Row className="one-table-new-fill-form">
      {controller.getOnlyForm() ? null : <TableBlock controller={controller} />}
      <FormBlock controller={controller} />
      <LoadingBlock controller={controller} />
    </Row>
  );

  return onlyForm ? <Spin spinning={onlyForm && loading}>{View}</Spin> : View;
};
