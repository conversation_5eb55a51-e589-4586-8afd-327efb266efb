import _ from 'lodash';
import * as cronjsMatcher from '@datasert/cronjs-matcher';
import { toastApi } from '@metroDesign/toast';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { IFloworkFormPost } from '@mdtApis/interfaces';
import { dayjs } from '@mdtBsComm/utils/dayUtil';
import { randomUuid } from '@mdtBsComm/utils/stringUtil';
import { RequestController } from '@mdtBsControllers/request-controller';
import { QUESTION_COMP_KEY, QUESTION_COMPONENT_DESCRIPTION_TEXT } from '@mdtProComm/constants';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import { ONE_TABLE_INFO } from '../../datlas/datlasConfig';
import { IOnetableNewFormConfig } from '../../interfacts';
import i18n from '../../languages';
import { doOneTableNewOperatorPublish } from '../../shared/onetablenew';
import { generateFormEditorSchema } from '../../utils/generateFormEditorSchemaUtil';
import {
  filterFormEmptyValues,
  FormTypeEnum,
  formValuesCommValidate,
  getExtraMetaComm,
  getSourcePkgId,
  modifyResidentInfoDefaultSetting,
  removeBindPkgId,
  transformAffiliatedOrgsToBackend,
  transformFeatureFlagsToBackend,
  transformFeatureFlagsToFrontEnd,
} from '../../utils/oneTableNewUtil';
import type { IOnPageCancelCb, IOnPageSuccessCb } from '../drawer-within-page';
import { FormEditorController, IFormEditorSchema } from '../form-editor/index';
import type { IOneTableNewReportCreateModel } from './OneTableNewReportCreateModel';
import { IReportConfigData } from './report-config';
import { MenuEnum, ReportSettingController } from './report-setting';

export enum ModeEnum {
  DESIGN = 'form-design',
  SETTING = 'form-setting',
}

export interface IReportCreateConfig {
  formType: FormTypeEnum; // 表单类型
  fromPkgId?: string;
  // 复制的id
  fromFormId?: string;
  // 编辑的id
  formId?: string;
}

export interface IControllerOptions extends IReportCreateConfig {
  Model: IOneTableNewReportCreateModel;
  onSuccessCb?: IOnPageSuccessCb;
  onCancelCb?: IOnPageCancelCb;
  onSaveSuccessCb?: IOnPageSuccessCb;
}

class OneTableNewReportCreateController extends RequestController {
  private Model: IOneTableNewReportCreateModel;
  private reportSettingController: ReportSettingController;
  private editorCtrl: FormEditorController;
  private activeMode$ = new BehaviorSubject(ModeEnum.DESIGN);
  private isSaving$ = new BehaviorSubject(false);
  private isPublishing$ = new BehaviorSubject(false);
  private formName$ = new BehaviorSubject('');
  private onSuccessCb?: IOnPageSuccessCb;
  private onCancelCb?: IOnPageCancelCb;
  private onSaveSuccessCb?: IOnPageSuccessCb;
  private sourcePkgId?: string;
  private formType: FormTypeEnum;
  private formId?: string;

  public constructor(options: IControllerOptions) {
    super();
    const { Model, onSuccessCb, onCancelCb, onSaveSuccessCb, ...reset } = options;
    this.Model = Model;
    this.formType = options.formType;
    this.onSuccessCb = onSuccessCb;
    this.onCancelCb = onCancelCb;
    this.onSaveSuccessCb = onSaveSuccessCb;
    this.editorCtrl = new FormEditorController({
      theme: DatlasAppController.getInstance().getTheme(),
      validate: formValuesCommValidate,
      defaultSettingFunc: modifyResidentInfoDefaultSetting,
    });
    this.reportSettingController = new ReportSettingController({ formType: options.formType });
    this.init(reset);
    this.listenActiveMode();
  }

  public getReportSettingController() {
    return this.reportSettingController;
  }

  public getEditorCtrl() {
    return this.editorCtrl;
  }

  public destroy() {
    super.destroy();
    this.Model = null!;
    this.editorCtrl.destroy();
    this.activeMode$.complete();
    this.isSaving$.complete();
    this.isPublishing$.complete();
    this.formName$.complete();
    this.onSuccessCb = undefined;
    this.onCancelCb = undefined;
    this.onSaveSuccessCb = undefined;
  }

  public getIsSaving$() {
    return this.isSaving$;
  }

  public getIsPublishing$() {
    return this.isPublishing$;
  }

  public getActiveMode$() {
    return this.activeMode$;
  }

  public handleModeChange = (val: string) => {
    this.activeMode$.next(val as ModeEnum);
  };

  public handleCancel = () => {
    this.onCancelCb?.();
  };

  public handlePublish = async () => {
    // 检查周期表单的时间间隔
    if (!(await this.validateCycleTimerInterval(ONE_TABLE_INFO.periodicMinInterval))) return;

    this.isSaving$.next(true);
    const saveData = await this.modifyForm();
    this.isSaving$.next(false);
    if (!saveData) return;
    // 更新formId，下次保存执行更新操作
    this.formId = saveData.id;
    doOneTableNewOperatorPublish({ formId: saveData.id, formData: saveData, onSuccessCallback: this.onSuccessCb });
  };

  public handleSave = async () => {
    this.isSaving$.next(true);
    const saveData = await this.modifyForm();
    this.isSaving$.next(false);
    if (!saveData) return;
    // 更新formId，下次保存执行更新操作
    this.formId = saveData.id;
    toastApi.success(i18n.chain.comTip.saveSuccess);
    this.onSaveSuccessCb?.();
  };

  // 验证周期表单的时间间隔
  private async validateCycleTimerInterval(minInterval = 30): Promise<boolean> {
    const configData = await this.reportSettingController.getConfigController()!.getValue();

    if (this.formType === FormTypeEnum.PERIODIC && configData?.cycle_timer) {
      const futureMatches = cronjsMatcher.getFutureMatches(configData?.cycle_timer, { timezone: 'UTC' });

      if (futureMatches.length >= 2) {
        const firstDate = dayjs(futureMatches[0]);
        const secondDate = dayjs(futureMatches[1]);
        const intervalMinutes = secondDate.diff(firstDate, 'minute');
        if (intervalMinutes < minInterval) {
          this.activeMode$.next(ModeEnum.SETTING);
          this.reportSettingController.handleMenuChange(MenuEnum.CONFIG);
          toastApi.warning(i18n.chain.proMicroModules.oneTable.tip.periodicMinInterval(minInterval));
          return false;
        }
      }
    }
    return true;
  }

  private async modifyForm() {
    const originInfoData = await this.reportSettingController.getInfoFormRef()?.current?.getValues();
    const infoData = filterFormEmptyValues(originInfoData);
    const editorSchema = this.editorCtrl.getEditorSchemaToSave() as IFormEditorSchema;
    const configData = await this.reportSettingController.getConfigController()!.getValue();
    if (!this.validate(infoData, configData, editorSchema)) return;

    const { name, description, ...rest } = infoData;
    // 追加form_type，方便检索过滤
    const extraMeta = _.merge({}, rest, configData, getExtraMetaComm(this.formType, this.sourcePkgId));
    transformFeatureFlagsToBackend(extraMeta, true);
    transformAffiliatedOrgsToBackend(extraMeta);
    const data: IFloworkFormPost = {
      name,
      description,
      form_spec: editorSchema,
      extra_meta: extraMeta,
      form_type: 'mdt_formily',
      process_type: 'one_table',
    };
    // 更新表单
    const resp = await (this.formId
      ? this.Model.updateForm(this.formId, data).toPromise()
      : this.Model.createNewForm(data).toPromise());
    return resp.data;
  }

  private validate(infoData: any, configData: any, editorSchema: IFormEditorSchema | string) {
    const reportCtrl = this.getReportSettingController();
    // schema校验
    if (_.isString(editorSchema)) {
      this.activeMode$.next(ModeEnum.DESIGN);
      toastApi.warning(editorSchema);
      return false;
    }

    // form表单不能全是text, 没有其他字段
    const properties = editorSchema.formilySchema.schema?.properties;
    const isAllText = _.every(properties, (it: any) => it[QUESTION_COMP_KEY] === QUESTION_COMPONENT_DESCRIPTION_TEXT);
    if (isAllText) {
      toastApi.warning(i18n.chain.proMicroModules.fillDesign.allTextCompTip);
      return false;
    }

    // 配置项错误
    if (_.isEmpty(infoData)) {
      this.activeMode$.next(ModeEnum.SETTING);
      reportCtrl.handleMenuChange(MenuEnum.INFO);
      toastApi.error(i18n.chain.proMicroModules.oneTable.tip.reportInfoError);
      return false;
    }

    // 信息项校验
    if (this.formType === FormTypeEnum.PERIODIC && _.isEmpty(configData?.cycle_timer)) {
      this.activeMode$.next(ModeEnum.SETTING);
      reportCtrl.handleMenuChange(MenuEnum.CONFIG);
      toastApi.error(i18n.chain.proMicroModules.oneTable.tip.reportConfigError);
      return false;
    }

    return true;
  }

  private async init(options: IReportCreateConfig) {
    let { fromPkgId, formId, fromFormId, formType } = options;

    let formConfig: IOnetableNewFormConfig | undefined;
    if (fromPkgId) {
      const [pkgData, columns] = await combineLatest(
        this.Model.queryDatapkgDetail(fromPkgId),
        this.Model.queryDatapkgColumns(fromPkgId),
      ).toPromise();
      // 根据数据包rename
      const pkgName = _.get(pkgData, 'name', fromPkgId);
      // 生成schema
      const formEditorSchema = generateFormEditorSchema({
        pkgDetail: pkgData!,
        columns: columns || [],
        includeDatapkgId: false,
      });
      formConfig = {
        name: `${pkgName}${_.replace(randomUuid(), 'uuid', '')}`,
        description: '',
        formSpec: formEditorSchema,
        extraMeta: getExtraMetaComm(formType, fromPkgId),
      };
    }
    this.formId = formId;
    // 从api获取
    if (!formConfig && (formId || fromFormId)) {
      formConfig = await this.Model.getForm((formId || fromFormId)!).toPromise();
      // 如果是复制的，则需要删除fromFormId的
      if (fromFormId && formConfig) {
        // 加入随机，防止name重复，后端会校验
        formConfig.name = `${formConfig.name}(${i18n.chain.comText.copy}${_.replace(randomUuid(), 'uuid', '')})`;
        removeBindPkgId(formConfig.extraMeta);
      }
    }
    this.sourcePkgId = getSourcePkgId(formConfig?.extraMeta);
    // 构造数据
    let formEditorSchema: IFormEditorSchema;
    let configData: IReportConfigData = {};
    let reportInfo: Record<string, any> = {};
    if (formConfig) {
      formEditorSchema = formConfig.formSpec as IFormEditorSchema;
      const formKeys = _.keys(_.get(ONE_TABLE_INFO.reportConfig.formSpec, 'formilySchema.schema.properties', {}));
      reportInfo = {
        ..._.pick(formConfig.extraMeta, formKeys),
        name: formConfig.name,
        description: formConfig.description,
      };
      configData = _.omit(formConfig.extraMeta, formKeys);
    }
    transformFeatureFlagsToFrontEnd(configData, true);
    this.reportSettingController.initData(formType, reportInfo, configData);
    requestAnimationFrame(() => {
      this.editorCtrl.initSchema(formEditorSchema!);
    });
  }

  private listenActiveMode() {
    this.activeMode$.subscribe((mode) => {
      this.reportSettingController.getShowView$().next(mode === ModeEnum.SETTING);
    });
  }
}

export { OneTableNewReportCreateController };
