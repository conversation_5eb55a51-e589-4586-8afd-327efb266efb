import { FC } from 'react';
import { ChevronDown, Close, RightForward } from '@metro/icons';
import { Button, ButtonGroup } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { Flex } from '@metroDesign/flex';
import { Popconfirm } from '@metroDesign/popconfirm';
import { Tabs } from '@metroDesign/tabs';
import { ToastContainer } from '@metroDesign/toast';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ResidentInfo } from '@mdtProFormEditor/metro-form-design/components';
import i18n from '../../languages';
import { FormEditor } from '../form-editor';
import { ModeEnum, OneTableNewReportCreateController } from './OneTableNewReportCreateController';
import { ReportSetting } from './report-setting';
import './index.less';

export interface IProps {
  controller: OneTableNewReportCreateController;
}

const ToolLeft: FC<IProps> = ({ controller }) => {
  const formName = useObservableState(controller.getReportSettingController().getFormName$());
  const isOnlyEditForm = controller.isOnlyEditForm();

  // 调整名称显示
  const title = isOnlyEditForm
    ? `${i18n.chain.proMicroModules.oneTable.editForm}(${formName})`
    : formName || i18n.chain.comTip.unnamed;

  return (
    <div className="tool-left">
      <div className="modal-drawer_title">
        <Popconfirm
          title={i18n.chain.proMicroModules.oneTable.exitCreateConfirm}
          description={i18n.chain.proMicroModules.oneTable.exitCreateConfirmDesc}
          onConfirm={controller.handleCancel}
          type="warning"
        >
          <Button onlyIcon icon={<Close />} ghost />
        </Popconfirm>
        <div className="title-wrap">{title}</div>
      </div>
    </div>
  );
};

const ToolCenter: FC<IProps> = ({ controller }) => {
  const activeMode = useObservableState(controller.getActiveMode$());
  const isOnlyEditForm = controller.isOnlyEditForm();
  if (isOnlyEditForm) {
    return (
      <div className="tool-center-tip">
        <ToastContainer type="warning" message={i18n.chain.proMicroModules.oneTable.editFormWaring} withIcon={false} />
      </div>
    );
  }

  return (
    <div className="tool-center">
      <Tabs
        activeKey={activeMode}
        items={[
          {
            label: i18n.chain.proMicroModules.oneTable.reportEdit,
            key: ModeEnum.DESIGN,
          },
          {
            label: <RightForward />,
            key: 'slot',
            disabled: true,
          },
          {
            label: i18n.chain.proMicroModules.oneTable.reportConfig,
            key: ModeEnum.SETTING,
          },
        ]}
        onChange={controller.handleModeChange}
      />
    </div>
  );
};

const ToolRight: FC<IProps> = ({ controller }) => {
  const isSaving = useObservableState(controller.getIsSaving$());
  const isPublishing = useObservableState(controller.getIsPublishing$());
  const isOnlyEditForm = controller.isOnlyEditForm();

  if (isOnlyEditForm) {
    return (
      <Flex className="tool-right" gap="small">
        <Button
          type="primary"
          className="cancel-btn"
          onClick={controller.handleSaveEditForm}
          loading={isSaving}
          disabled={isPublishing}
        >
          {i18n.chain.proMicroModules.oneTable.saveEditForm}
        </Button>
      </Flex>
    );
  }

  return (
    <Flex className="tool-right" gap="small">
      <Button className="cancel-btn" onClick={controller.handleSave} loading={isSaving} disabled={isPublishing}>
        {i18n.chain.proMicroModules.oneTable.saveForm}
      </Button>
      <ButtonGroup disabled={isPublishing || isSaving}>
        <Button type="primary" onClick={controller.handlePublish} loading={isPublishing}>
          {i18n.chain.proMicroModules.oneTable.publishJob}
        </Button>
        <Dropdown
          menu={{
            items: [{ label: i18n.chain.proMicroModules.oneTable.scheduledJob, key: 'scheduling', disabled: true }],
          }}
        >
          <Button type="primary" icon={<ChevronDown />} onlyIcon withoutBorder />
        </Dropdown>
      </ButtonGroup>
    </Flex>
  );
};

const Tool: FC<IProps> = ({ controller }) => {
  return (
    <div className="create-report-tool drawer-within-page-inner-tool">
      <ToolLeft controller={controller} />
      <ToolCenter controller={controller} />
      <ToolRight controller={controller} />
    </div>
  );
};

const FormEditorView: FC<IProps> = ({ controller }) => {
  const activeMode = useObservableState(controller.getActiveMode$());
  const designShowCls = activeMode !== ModeEnum.SETTING ? 'design-show' : '';

  return (
    <FormEditor
      controller={controller.getEditorCtrl()}
      className={`create-report_iframe ${designShowCls}`}
      headerLess
      customerDesignerComponents={{
        components: { ResidentInfo },
      }}
    />
  );
};

export const OneTableNewReportCreate: FC<IProps> = ({ controller }) => {
  return (
    <div className="one-table-new-report-create-page">
      <Tool controller={controller} />
      <FormEditorView controller={controller} />
      <ReportSetting controller={controller.getReportSettingController()} />
    </div>
  );
};
