import { createRef } from 'react';
import { RequestController } from '@mdtBsControllers/request-controller';
import type { IFormSpecRefHandle } from '../../../components/form-spec';
import { FormTypeEnum, getFormTypeSpec } from '../../../utils/oneTableNewUtil';
import type { IFormEditorSchema } from '../../form-editor';

export type IReportConfigData = Record<string, any>;

export interface IReportConfigControllerOptions {
  formType: FormTypeEnum;
  configData?: IReportConfigData;
}

export class ReportConfigController extends RequestController {
  private formRef = createRef<IFormSpecRefHandle>();
  private configSettingSpec: IFormEditorSchema;
  private configData?: IReportConfigData;

  public constructor({ configData, formType }: IReportConfigControllerOptions) {
    super();
    this.configData = configData;
    const spec = getFormTypeSpec(formType);
    this.configSettingSpec = spec;
  }

  public destroy() {
    super.destroy();
    this.formRef = null!;
    this.configData = undefined;
  }

  public getConfigSettingSpec() {
    return this.configSettingSpec;
  }

  public async getValue(): Promise<undefined | IReportConfigData> {
    return this.formRef.current?.getValues();
  }

  public getFormRef() {
    return this.formRef;
  }

  public getConfigData() {
    return this.configData;
  }
}
