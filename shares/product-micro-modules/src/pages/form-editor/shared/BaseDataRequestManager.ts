import _ from 'lodash';
import { Engine } from '@designable/core';
import { IRequestOwner, IRequestRequestConfig } from '@mdtApis/interfaces';
import { CITIZEN_DATA } from '@mdtBsBffServices/one-table';
import { parseStrToObj, replaceTextParams } from '@mdtBsComm/utils/stringUtil';
import { postOuterApiAsync } from '@mdtBsServices/datlasbff';
import { postQlTempExecutionAsync } from '@mdtBsServices/qlangs';
import { OneTableBffService } from '@mdtProComm/bff-services';
import { DatlasAppController } from '../../../datlas/app/DatlasAppController';
import i18n from '../../../languages';
import { bffProxyConfigData } from '../../../shared/enums';
import { isValidIdNo } from '../../../utils/idNoUtil';
import { ONE_TABLE_CITIZEN_REQUEST_NAME } from '../../../utils/oneTableUtil';
import { NodeData } from '../service/data-source/NodeData';
import { innerApiRequestMap } from '../service/innerApiRequests';
import { dsRequest } from '../service/request';
import { BFF_REQUEST_FLAG } from '../util';
import { disableDataSourceBffProxy, enableDataSourceBffProxy } from './dataSourceUtil';
import { ApiRequestMethodEnum, DataRequestTypeEnum, InnerApiEnum, RequestDataTypeEnum } from '.';
import { getRunQlang } from '.';

export type IDefaultHandler = (config: any, variable: any, dataType?: string) => Promise<any>;

export abstract class BaseDataRequestManager<T = NodeData> {
  public engine?: Engine;
  public owner?: IRequestOwner;
  public dataMap: Map<string, T> = new Map();

  public variables: Record<string, any> = {};
  protected requestCache: Map<string, any> = new Map();

  private handlers: Record<string, IDefaultHandler> = {
    [DataRequestTypeEnum.API]: async (config, variable, dataType) => {
      return this.requestApiData(config, variable, dataType);
    },
    [DataRequestTypeEnum.QLANG]: async (config, variable, dataType) => {
      return this.executeQlang(config, variable, dataType);
    },
    [DataRequestTypeEnum.LOW_CODE_QLANG]: async (config, variable, dataType) => {
      return this.executeQlang(config.sql, variable, dataType);
    },
    [DataRequestTypeEnum.INNER_API]: async (config, variable, dataType) => {
      return this.requestInnerApiData(config, variable, dataType);
    },
  };

  protected constructor(engine?: Engine, owner?: IRequestOwner) {
    this.engine = engine;
    this.owner = owner;
    const app = DatlasAppController.getInstance();
    this.setVariables({
      datlasUserId: app.getUserId(),
      datlasAppId: app.getAppId(),
      datlasToken: app.getUserToken(),
    });
  }

  public async requestData(allSetting: any, configType: string, variable: any, dataType?: string): Promise<any> {
    const config = allSetting[configType];
    if (!config) {
      return null;
    }

    try {
      if (!this.handlers[configType]) return null;
      return await this.handlers[configType](config, variable, dataType);
    } catch (error: any) {
      return {
        isError: true,
        message: error?.message || i18n.chain.proMicroModules.workflow.edit.serviceError,
      };
    }
  }

  public destroy() {
    this.engine = undefined;
    this.variables = {};
    this.requestCache.clear();
    this.dataMap.forEach((it: any) => it.destroy());
    this.dataMap.clear();
  }

  public setVariables(data: Record<string, any>) {
    _.forEach(data, (val, key) => {
      this.variables[key] = val;
    });
  }

  public registerHandler(configType: string, handler: IDefaultHandler) {
    this.handlers[configType] = handler;
  }

  public setData(comId: string, data: any) {
    this.dataMap.set(comId, data);
  }

  public getData(comId: string) {
    return this.dataMap.get(comId);
  }

  public removeData(id: string) {
    this.dataMap.delete(id);
  }

  protected async executeQlang(config: any, variable: any, dataType?: string) {
    let result: any = dataType ? (dataType === RequestDataTypeEnum.ARRAY ? [] : {}) : undefined;
    if (config.sql) {
      const qlang = replaceTextParams(getRunQlang(config.sql), variable);
      result = await this.getCachedPromiseResult({ qlang }, () =>
        postQlTempExecutionAsync(
          { qlang, result_type: 'record' },
          {
            params: { page_size: 5000 },
            quiet: true,
            enableBffProxy: enableDataSourceBffProxy(config, this.owner),
            disableBffProxy: disableDataSourceBffProxy(config, this.owner),
            owner: this.owner,
          },
        ).then((resp) => (resp.success ? _.cloneDeep(resp.data) : { isError: true, message: resp.msg })),
      );
    } else {
      result = { isError: true, message: i18n.chain.proMicroModules.workflow.edit.needSql };
    }
    return result;
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  protected async requestApiData(config: any, variable: any, dataType?: string) {
    let result: any = dataType ? (dataType === RequestDataTypeEnum.ARRAY ? [] : {}) : undefined;
    let url: string = config.url;
    if (!url) {
      result = { isError: true, message: i18n.chain.proMicroModules.workflow.edit.needUrl };
      return result;
    }
    if (_.startsWith(config.url, BFF_REQUEST_FLAG)) {
      return this.requestGraphqlData(config, variable);
    }
    if (!/^[a-zA-z]+:\/\/\S*$/.test(config.url)) {
      url = new URL(url, window.location.origin).toString();
    }
    try {
      url = replaceTextParams(url, variable);
      const isConfigProxy = config.proxy === true;
      const conf: IRequestRequestConfig = {
        headers: parseStrToObj(replaceTextParams(config.headers.value, variable)),
      };

      const body =
        config.method === ApiRequestMethodEnum.POST
          ? parseStrToObj(replaceTextParams(config.body.value, variable))
          : undefined;

      result = await this.getCachedPromiseResult(
        {
          url,
          method: config.method,
          headers: conf.headers,
          body,
        },
        () => {
          if (isConfigProxy) {
            return postOuterApiAsync(_.pick({ ...config, url }, bffProxyConfigData), {
              ...conf,
              enableBffProxy: false,
              disableBffProxy: true,
              quiet: true,
            });
          } else {
            if (config.cookie) {
              conf.withCredentials = true;
            }
            if (config.method === ApiRequestMethodEnum.GET) {
              return dsRequest.get(url, conf);
            } else {
              return dsRequest.post(url, body, conf);
            }
          }
        },
      );
    } catch (error: any) {
      result = { isError: true, message: error?.message || i18n.chain.proMicroModules.workflow.edit.serviceError };
    }
    result?.isError && (result.requestError = true);
    return result;
  }

  protected async requestInnerApiData(config: any, variable?: any, dataType?: string) {
    let result: any = dataType ? (dataType === RequestDataTypeEnum.ARRAY ? [] : {}) : undefined;
    const api = config.api as InnerApiEnum;
    if (!api) return { isError: true, message: i18n.chain.proMicroModules.workflow.edit.chooseApi };

    const request = innerApiRequestMap[api];
    if (request) {
      return this.getCachedPromiseResult({ api, config, variable }, () => request({ ...config }, this.owner, variable));
    }

    return result;
  }

  private async requestGraphqlData(config: any, variablesValueMap: any) {
    const requestName = _.replace(config.url, `${BFF_REQUEST_FLAG}`, '');
    const body: any = parseStrToObj(replaceTextParams(config.body.value, variablesValueMap));

    if (requestName === ONE_TABLE_CITIZEN_REQUEST_NAME) {
      const idNo = body.idNo;
      if (!isValidIdNo(idNo)) return {};

      return this.getCachedPromiseResult({ requestName, body }, () =>
        OneTableBffService.queryCitizenInfo({
          respData: CITIZEN_DATA,
          idNo,
        }).then((resp) => {
          return resp.success ? resp.page_data || {} : {};
        }),
      );
    }
    return {};
  }

  private async getCachedPromiseResult<T>(cacheKey: Record<string, any>, requestFn: () => Promise<T>): Promise<T> {
    const key = JSON.stringify(this.normalizeRequestKey(cacheKey));

    let cachedResult = this.requestCache.get(key);
    if (!cachedResult) {
      try {
        const resultPromise = requestFn();
        this.requestCache.set(key, resultPromise);

        const result = await resultPromise;
        this.requestCache.set(key, Promise.resolve(result));
        return result;
      } catch (error) {
        this.requestCache.delete(key);
        throw error;
      }
    }

    return cachedResult;
  }

  // 规范化请求键，只保留关键信息
  private normalizeRequestKey(cacheKey: Record<string, any>): Record<string, any> {
    if (!cacheKey) return {};

    const normalized: Record<string, any> = {};

    // 对API请求，只考虑URL、方法和请求体
    if (cacheKey.url) {
      normalized.url = cacheKey.url;
      normalized.method = cacheKey.method || 'GET';

      // 如果有请求体，仅保留关键数据
      if (cacheKey.body) {
        // 移除不影响结果的字段，如时间戳、随机ID等
        const bodyClone = _.cloneDeep(cacheKey.body);
        delete bodyClone.timestamp;
        delete bodyClone.requestId;
        normalized.body = bodyClone;
      }
    }

    // 对内部API，保留API名称和关键参数
    if (cacheKey.api) {
      normalized.api = cacheKey.api;
      // 过滤掉无关参数
      if (cacheKey.config) {
        const configClone = _.cloneDeep(cacheKey.config);
        delete configClone.requestId;
        normalized.config = configClone;
      }
    }

    // 对QLang查询，只保留查询语句
    if (cacheKey.qlang) {
      normalized.qlang = cacheKey.qlang;
    }

    return normalized;
  }
}
