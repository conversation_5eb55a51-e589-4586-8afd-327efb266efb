.dn-reactions-setter {
  width: 100%;
  min-height: 623px;
  overflow: hidden;

  .reaction-runner {
    .metro-collapse-content-box {
      padding: 12px 0 !important;
    }
  }

  .reaction-state {
    .metro-collapse-content-box {
      padding: 12px 0 !important;
    }
  }

  .dn-field-property-setter {
    display: flex;
    height: 300px;

    &-coder-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
      flex-grow: 2;
      height: 100%;
      padding-left: 10px;
    }

    &-coder-start {
      flex-grow: 0;
      height: 31px;
      margin-bottom: 4px;
      color: var(--metro-text-1);
      font-weight: 300;
      font-size: 18px;
      line-height: 30px;
      opacity: 0.96;
    }

    &-coder-end {
      flex-grow: 0;
      height: 31px;
      margin-top: 4px;
      margin-bottom: 4px;
      color: var(--metro-text-1);
      font-weight: 300;
      font-size: 18px;
      line-height: 30px;
      opacity: 0.96;
    }

    &-coder {
      flex-grow: 2;
      min-width: 0;
      padding-left: 10px;
    }
  }
}
