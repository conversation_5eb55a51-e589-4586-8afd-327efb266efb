import _ from 'lodash';
import { ILabelValue } from '@mdtBsComm/interfaces';
import i18n from '../../../../../../languages';

const ColumnCompName = 'ArrayTable.Column';

export const getDataInnerApiCascaderSchema = (options: ILabelValue[]) => {
  return {
    type: 'object',
    properties: {
      isObjectMode: {
        type: 'boolean',
        'x-component': 'Checkbox',
        'x-component-props': {
          children: i18n.chain.proMicroModules.workflow.edit.objMode,
          className: 'is-object-mode',
        },
      },
      list: {
        'x-decorator': 'FormItem',
        type: 'array',
        'x-component': 'ArrayTable',
        'x-component-props': { withVerticalBorder: false, size: 'small' },
        items: {
          type: 'object',
          properties: {
            column0: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { width: 50, title: 'Sort', align: 'center' },
              properties: {
                sort: {
                  type: 'void',
                  'x-component': 'ArrayTable.SortHandle',
                },
              },
            },
            column1: {
              type: 'void',
              'x-component': ColumnCompName,
              'x-component-props': { title: `Label` },
              properties: {
                label: {
                  type: 'string',
                  'x-component': 'Select',
                  'x-component-props': {
                    options,
                    placeholder: i18n.chain.comPlaceholder.select,
                    bordered: false,
                    size: 'small',
                  },
                },
              },
            },
            column2: {
              type: 'void',
              'x-component': ColumnCompName,
              'x-component-props': { title: `Value` },
              'x-display': 'none',
              properties: {
                value: {
                  type: 'string',
                  'x-component': 'Select',
                  'x-component-props': {
                    options,
                    placeholder: i18n.chain.comPlaceholder.select,
                    bordered: false,
                    size: 'small',
                  },
                },
              },
            },
            delete: {
              type: 'void',
              'x-component': ColumnCompName,
              'x-component-props': {
                dataIndex: 'operations',
                width: 5,
                fixed: 'right',
              },
              properties: {
                item: {
                  type: 'void',
                  'x-component': 'FormItem',
                  properties: {
                    remove: {
                      type: 'void',
                      'x-component': 'ArrayTable.Remove',
                    },
                  },
                },
              },
            },
          },
        },
        properties: {
          add: {
            type: 'void',
            'x-component': 'ArrayTable.Addition',
            'x-component-props': {
              title: i18n.chain.proMicroModules.workflow.edit.addOne,
              block: 'false',
              size: 'small',
              primary: true,
              isLink: true,
            },
          },
        },
      },
    },
  };
};

export const isValidSettingData = (formData: Record<string, any>) => {
  const { isObjectMode, list } = formData || {};
  if (_.isEmpty(list)) return false;
  if (isObjectMode) {
    return _.every(list, (it) => it.label && it.value);
  }
  return true;
};
