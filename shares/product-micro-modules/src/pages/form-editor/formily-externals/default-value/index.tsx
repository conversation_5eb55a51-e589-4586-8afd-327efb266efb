import { FC } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, observer } from '@formily/react';
import { Scrollbar } from '@metroDesign/scrollbar';
import { Space } from '@metroDesign/space';
import { DefaultValueView } from './view';
import './style.less';

const DEFAULT_VALUE_SCHEMA_KEY = 'defaultConfig';

export const DefaultValue: FC = observer(() => {
  return (
    <Space className="designer-global-default-value" direction="vertical">
      <Scrollbar style={{ height: '100%' }}>
        <ObjectField name={DEFAULT_VALUE_SCHEMA_KEY} component={[DefaultValueView]} />
      </Scrollbar>
    </Space>
  );
});
