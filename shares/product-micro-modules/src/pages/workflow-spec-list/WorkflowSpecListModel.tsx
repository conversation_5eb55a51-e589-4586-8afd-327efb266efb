import _ from 'lodash';
import { BehaviorSubject, from } from 'rxjs';
import { switchMap, takeWhile } from 'rxjs/operators';
import { IWorkflowSpec, IWorkflowSpecsQuery } from '@mdtApis/interfaces';
import { IRequestCancelToken } from '@mdtApis/interfaces/request';
import { DATE_FORMATTER_1, formateDate } from '@mdtBsComm/utils/dayUtil';
import {
  deleteWorkflowSpecAsync,
  disableWorkflowSpecAsync,
  enableWorkflowSpecAsync,
  getWorkflowSpecAsync,
  queryWorkflowSpecsAsync,
  queryWorkflowSpecsPaginationAsync,
} from '@mdtBsServices/flowork';
import { WfSpecTypeEnum } from '@mdtProComm/constants';
import { CommonModel } from '@mdtProComm/models/CommonModel';
import { DatasetsModel } from '@mdtProComm/models/DatasetsModel';
import i18n from '../../languages';
import { WorkFlowFormModel } from '../../pages-in-micro/workflow/_util/WorkFlowFormModel';
import { WorkflowEditModel } from '../workflow-edit';
import { WorkflowSpecCreateModel } from '../workflow-spec-create';
import { WorkflowSpecDatapkgModel } from '../workflow-spec-datapkg';
import { WorkflowSpecDetailModel } from '../workflow-spec-detail';
import { ITableData } from './WorkflowSpecListController';

export class WorkflowSpecListModel {
  public static getSubModels() {
    return {
      editModel: WorkflowEditModel,
      detailModel: WorkflowSpecDetailModel,
      formModel: WorkFlowFormModel,
      createModel: WorkflowSpecCreateModel,
      datasetModel: DatasetsModel,
    };
  }
  public static transformToTableData(item: IWorkflowSpec, nameMap: Record<string, string>): ITableData {
    return {
      id: item.id,
      name: item.name!,
      user: nameMap[item.user_id] || `${item.user_id}`,
      lastRunTime: formateDate(item.update_time * 1000, DATE_FORMATTER_1),
      result: [i18n.chain.proMicroModules.workflow.status.noRun],
      type: item.process_type as WfSpecTypeEnum,
      active: new BehaviorSubject(item.active),
      formSpec: item.form_spec,
    };
  }

  public static queryFirstPageList(params: IWorkflowSpecsQuery, cancelToken?: IRequestCancelToken) {
    return from(
      queryWorkflowSpecsPaginationAsync({ cancelToken, params: { fetch_total_count: true, ...params } }),
    ).pipe(
      takeWhile((v) => !v.canceled),
      switchMap((resp) => {
        const { total_count, dataResult } = resp.data || { total_count: 0, dataResult: [] };
        return CommonModel.getUserIdNameMap(
          _.map(dataResult, (it) => it.user_id),
          cancelToken,
        ).then((nameMap) => {
          return [total_count, _.map(dataResult, (it) => this.transformToTableData(it, nameMap))] as [
            number,
            ITableData[],
          ];
        });
      }),
    );
  }

  // 获取下一页flow列表
  public static getNextFlowList(params: IWorkflowSpecsQuery, cancelToken?: IRequestCancelToken) {
    return from(queryWorkflowSpecsAsync({ cancelToken, params })).pipe(
      takeWhile((resp) => !resp.canceled),
      switchMap((resp) => {
        return CommonModel.getUserIdNameMap(
          _.map(resp.data, (it) => it.user_id),
          cancelToken,
        ).then((nameMap) => {
          return _.map(resp.data, (it) => this.transformToTableData(it, nameMap));
        });
      }),
    );
  }

  public static deleteWorkflowSpec(specId: string) {
    return deleteWorkflowSpecAsync(specId).then((resp) => resp.success);
  }

  public static getWorkflowSpecXml(specId: string) {
    return getWorkflowSpecAsync(specId, { params: { with_xml: true } }).then((resp) => resp.data?.bpmn_xml || '');
  }

  public static getWorkflowSpec(specId: string) {
    return getWorkflowSpecAsync(specId, {}).then((resp) =>
      resp.data ? this.transformToTableData(resp.data, {}) : undefined,
    );
  }

  public static getFillModel() {
    return WorkflowSpecDatapkgModel;
  }

  public static async changeStatus(checked: boolean, record: ITableData) {
    const id = record.id;
    return checked ? this.enableWorkflowSpec(id) : this.disableWorkflowSpec(id);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public static async checkPkgExist(pkgId: any) {
    return false;
  }

  private static enableWorkflowSpec(specId: string) {
    return enableWorkflowSpecAsync(specId).then((resp) => resp.data);
  }

  private static disableWorkflowSpec(specId: string) {
    return disableWorkflowSpecAsync(specId).then((resp) => resp.data);
  }
}

export type IWorkflowSpecListModel = typeof WorkflowSpecListModel;
