import { map } from 'rxjs/operators';
import { DatapkgModel } from '@mdtProComm/models/DatapkgModel';
import { IUserLazySelectorModel, UserLazySelectorModel } from '../../containers/user-lazy-selector';

export class WorkflowPropertyConfigModel {
  public static userSelectorModel: IUserLazySelectorModel;

  public static getUserSelectorMode(appId: number) {
    !this.userSelectorModel &&
      (this.userSelectorModel = new UserLazySelectorModel({
        appId,
      }));

    return this.userSelectorModel;
  }

  public static queryDatapkgColumns(pkgId: string) {
    return DatapkgModel.queryDatapkgColumns(pkgId).pipe(
      map((resp) => {
        return resp.success ? resp.data! : [];
      }),
    );
  }
}

export type IWorkflowPropertyConfigModel = typeof WorkflowPropertyConfigModel;
