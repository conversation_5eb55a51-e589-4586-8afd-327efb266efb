import _ from 'lodash';
import { ChevronDown, MoreHoriz } from '@metro/icons';
import { Button } from '@metroDesign/button';
import { Dropdown } from '@metroDesign/dropdown';
import { toastApi } from '@metroDesign/toast';
import { BehaviorSubject } from 'rxjs';
import { skip } from 'rxjs/operators';
import { RequestController } from '@mdtBsControllers/request-controller';
import { OneTableNewDataStateEnum } from '@mdtProComm/constants';
import type { IProps as ICardIconProps } from '../../components/one-table-new-card-icon';
import i18n from '../../languages';
import {
  doOneTableNewOperatorAddManageCollaborate,
  doOneTableNewOperatorAddTaskCollaborate,
  doOneTableNewOperatorCancelSubmit,
  doOneTableNewOperatorCompleteFlow,
  doOneTableNewOperatorCopyCreate,
  doOneTableNewOperatorDeleteDraft,
  doOneTableNewOperatorDeleteWorkflow,
  doOneTableNewOperatorDraftEdit,
  doOneTableNewOperatorFillForm,
  doOneTableNewOperatorIssueTask,
  doOneTableNewOperatorPreviewForm,
  doOneTableNewOperatorPublish,
  doOneTableNewOperatorRefuseTask,
  doOneTableNewOperatorSubmitTask,
  doOneTableNewOperatorTransferManage,
  doOneTableNewOperatorTransferTask,
} from '../../shared/onetablenew';
import { DataTableTypeEnum, DetailFromPageEnum, TaskPersonalStatusEnum } from '../../utils/oneTableNewUtil';
import { type IControllerOptions as IDatapkgDataPreviewControllerOptions } from '../datapkg-data-preview';
import {
  OneTableNewDataTableStatics,
  OneTableNewDataTableStaticsController,
} from '../one-table-new-data-table-statics';
import { openOneTableNewPeriodicManagementDrawer } from '../one-table-new-periodic-management/drawer-one-table-new-periodic-management';
import { OperatorComponentEnum, TabEnumKey } from './_util/constants';
import {
  Collaborate,
  CollaborateController,
  CollaborateModel,
  DeliveryManagement,
  DeliveryManagementController,
  DeliveryManagementModel,
  FlowInfo,
  FlowInfoController,
  FormInfo,
  FormInfoController,
  FormInfoModel,
} from './components';
import { OneTableNewReportDetailDataTableController } from './OneTableNewReportDetailDataTableController';
import { type IOneTableNewReportDetailModel, IFormData, ITaskData } from './OneTableNewReportDetailModel';

interface IHeaderConfig {
  iconConfig?: ICardIconProps;
  name?: string;
  isCollaborate?: boolean;
  isEndless?: boolean;
  isPeriodic?: boolean;
  approvalStatus?: OneTableNewDataStateEnum;
  reason?: string;
  personStatus?: TaskPersonalStatusEnum;
}

interface IOperatorItem {
  type: OperatorComponentEnum;
  props: Record<string, any>;
}

export interface IControllerOptions {
  Model: IOneTableNewReportDetailModel;
  fromPage: DetailFromPageEnum;
  excludeTabs?: TabEnumKey[];
  excludeOperators?: string[];
  formId?: string;
  rootWfId?: string;
  assignWfId?: string;
  onClose?: any;
  fillOnlyForm?: boolean;
  showFormInfoEditBtn?: boolean;
  onReload?: () => void;
  onReloadExtra?: () => void;
  dataPreviewOptions?: Partial<IDatapkgDataPreviewControllerOptions>;
}

export class OneTableNewReportDetailController extends RequestController {
  protected deliveryManagementController?: any;
  protected dataTableController?: OneTableNewDataTableStaticsController;

  private Model: IOneTableNewReportDetailModel;
  private tabsConfig?: Record<string, any>;
  private headerConfig$ = new BehaviorSubject<IHeaderConfig>({});
  private operators$ = new BehaviorSubject<IOperatorItem[]>([]);
  private excludeOperators?: string[];
  private dataPreviewOptions?: IControllerOptions['dataPreviewOptions'];
  private onClose?: any;
  private onReload?: () => void;
  private onReloadExtra?: () => void;

  private loading$ = new BehaviorSubject(true);
  private activeTabKey$ = new BehaviorSubject<TabEnumKey | undefined>(undefined);
  private dataError$ = new BehaviorSubject(false);
  private isRunning$ = new BehaviorSubject(false);
  private formName$ = new BehaviorSubject('');
  private fromPage: DetailFromPageEnum;
  private showFormInfoEditBtn?: boolean;

  private isDraftDetail?: boolean;
  private isGrantedDetail?: boolean;
  private formItem?: IFormData;
  private taskItem?: Omit<ITaskData, 'formData'>;
  private pageId = ''; // 临时存储key值需要
  private fillOnlyForm?: boolean;

  private tabsVisible = {
    [TabEnumKey.FLOW_INFO]: true,
    [TabEnumKey.DELIVERY_MANAGEMENT]: true,
    [TabEnumKey.COLLABORATE]: true,
    [TabEnumKey.DATA_TABLE]: true,
    [TabEnumKey.FORM_INFO]: true,
  };

  // tab页的controller
  private formInfoController?: FormInfoController;
  private flowInfoController?: FlowInfoController;
  private collaborateController?: CollaborateController;

  public constructor(options: IControllerOptions) {
    super();
    const {
      Model,
      onClose,
      onReload,
      onReloadExtra,
      excludeTabs,
      excludeOperators,
      fillOnlyForm,
      dataPreviewOptions,
      showFormInfoEditBtn,
      ...rest
    } = options;
    this.Model = Model;
    this.onClose = onClose;
    this.fillOnlyForm = fillOnlyForm;
    this.excludeOperators = excludeOperators || [];
    this.dataPreviewOptions = dataPreviewOptions || {};
    this.fromPage = rest.fromPage;
    this.showFormInfoEditBtn = showFormInfoEditBtn;
    this.onReload = onReload;
    this.onReloadExtra = onReloadExtra;
    this.initTabsVisible(excludeTabs);
    this.init(rest);
    this.listenRuningStatus();
  }

  public destroy() {
    super.destroy();
    this.onClose = undefined;
    this.onReload = undefined;
    this.onReloadExtra = undefined;
    this.activeTabKey$.complete();
    this.loading$.complete();
    this.dataError$.complete();
    this.operators$.complete();
    this.operators$.next([]);
    this.formName$.complete();
    this.isRunning$.complete();
    this.headerConfig$.complete();
    this.headerConfig$.next({});
    this.formItem = undefined;
    this.taskItem = undefined;
    this.tabsConfig = undefined;
    this.formInfoController?.destroy();
    this.dataTableController?.destroy();
    this.deliveryManagementController?.destroy();
    this.flowInfoController?.destroy();
    this.collaborateController?.destroy();
  }

  public getFormName$() {
    return this.formName$;
  }

  public getFormData() {
    return this.formItem;
  }

  public getFromPage() {
    return this.fromPage;
  }

  public getActiveTabKey$() {
    return this.activeTabKey$;
  }

  public getOperaters$() {
    return this.operators$;
  }

  public getLoading$() {
    return this.loading$;
  }

  public getDataError$() {
    return this.dataError$;
  }

  public enableFill = () => {
    const { isFormManageLevelUser, isRunning } = this.formItem || {};
    // 不在运行中, 则false
    if (!isRunning) return false;
    // 运行中且是管理员
    if (isFormManageLevelUser) return true;
    // 其他填报人员
    const { personStatus, approvalStatus } = this.taskItem || {};
    if (personStatus === TaskPersonalStatusEnum.RUNNING) {
      return !(
        approvalStatus === OneTableNewDataStateEnum.SUBMITTED || approvalStatus === OneTableNewDataStateEnum.APPROVED
      );
    }
    return personStatus === TaskPersonalStatusEnum.UNHANDLED;
  };

  public setActiveTabKey(key: TabEnumKey) {
    sessionStorage.setItem(this.pageId, key);
    this.activeTabKey$.next(key);
  }

  public getFormInfoController = () => {
    if (!this.formInfoController) {
      const { formId, rootWfId, originItem } = this.formItem!;
      this.formInfoController = new FormInfoController({
        formId: formId,
        Model: FormInfoModel,
        showEditBtn: this.showFormInfoEditBtn,
        successCallback: async () => {
          await this.Model.notifyFormAdminTask(rootWfId);
          const formData = await this.Model.getDraftForm(formId, this.formItem);
          this.dataError$.next(!formData);
          if (!formData) return;
          this.formItem = formData;
          this.formName$.next(formData.formName);
          this.formInfoController!.loadData(formData.originItem);
        },
        editFormSuccessCallback: async () => {
          const formData = await this.Model.getDraftForm(formId, this.formItem);
          this.dataError$.next(!formData);
          if (!formData) return;
          this.formItem = formData;
          this.resetDataTableTab();
        },
      });
      this.formInfoController.loadData(originItem);
    }
    return this.formInfoController;
  };

  public getDataTableController() {
    if (!this.dataTableController) {
      const itemData = this.formItem!;
      const extraParams = this.isGrantedDetail ? { row_permission_params: { check_granted: true } } : undefined;
      this.dataTableController = new OneTableNewReportDetailDataTableController({
        itemData,
        requestParams: extraParams,
        staticsExtraBodyParams: extraParams,
        queryDownstreamUsersOptions: { returnEmpty: this.isGrantedDetail },
        dataPreviewOptions: {
          ...this.dataPreviewOptions,
          pkgId: itemData.pkgId,
        },
        modifyItemData: this.innerModifyItemData,
        dataTableType: DataTableTypeEnum.PRIVIEW,
      });
    }
    return this.dataTableController;
  }

  public getDeliveryManagement() {
    return DeliveryManagement;
  }

  public getDeliveryManagementController() {
    if (!this.deliveryManagementController) {
      const options = this.getDeliverManageMentOptions();
      this.deliveryManagementController = new DeliveryManagementController(options);
    }
    return this.deliveryManagementController;
  }

  public getDeliverManageMentOptions() {
    const itemData = this.formItem!;
    return {
      itemData,
      endDate: itemData.endDate,
      Model: DeliveryManagementModel,
      issueDataSuccess: this.reloadDataTableData,
      approveDataSuccess: this.reloadDataAndFlow,
      rejectDataSuccess: this.reloadDataAndFlow,
      cancelTaskSuccess: this.reloadDataAndFlow,
    };
  }

  public getFlowInfoController() {
    if (!this.flowInfoController) {
      const itemData = this.formItem!;
      this.flowInfoController = new FlowInfoController({ itemData });
      this.flowInfoController.loadData();
    }
    return this.flowInfoController;
  }

  public getCollaborateController() {
    if (!this.collaborateController) {
      const itemData = this.formItem!;
      this.collaborateController = new CollaborateController({
        itemData,
        Model: CollaborateModel,
        successCallback: () => {
          this.reloadCollaborate();
          this.reloadDataAndFlow(); // 数据及流程会更新
        },
      });
      this.reloadCollaborate();
    }
    return this.collaborateController;
  }

  public getHeaderConfig$() {
    return this.headerConfig$;
  }

  public getTabsProps() {
    return this.tabsConfig;
  }

  public getOperators$() {
    return this.operators$;
  }

  public clickFillForm = (defaultValue?: Record<string, any>, onPreviewClose?: any) => {
    const itemData = this.formItem!;
    doOneTableNewOperatorFillForm({
      itemData,
      onlyForm: this.fillOnlyForm,
      defaultValue,
      // paginationParams: this.getDataTableController().getPaginationParams(),
      // requestParams: {
      //   operator_filter: this.getDataTableController().getFilterParams().operator_filter,
      //   orderby: this.getDataTableController().getFilterParams().orderby,
      // },
      submitConfirmOptionsFunc: (onClose?: () => void) => {
        const closeAndReloadDataTable = () => {
          onClose?.();
          onPreviewClose?.();
          this.reloadDataTableData();
          this.setActiveTabKey(TabEnumKey.DATA_TABLE);
        };
        return {
          onlySaveCallBack: closeAndReloadDataTable,
          deleteDataCallback: closeAndReloadDataTable,
          submitSuccessCallback: () => {
            closeAndReloadDataTable();
            this.reloadFlowInfo();
            // 会改变状态
            this.initTaskDetail(itemData.assignWfId);
          },
        };
      },
    });
  };

  public innerModifyItemData = async (item: Record<string, any>, action: string) => {
    const itemData = this.formItem!;
    if (action === DataTableTypeEnum.PRIVIEW) {
      doOneTableNewOperatorPreviewForm({
        itemData,
        defaultValue: item,
        showEdit: this.enableFill(),
        paginationParams: this.getDataTableController().getPaginationParams(),
        // 在预览的时候，需要传递operator_filter和orderby
        requestParams: {
          operator_filter: this.getDataTableController().getFilterParams().operator_filter,
          orderby: this.getDataTableController().getFilterParams().orderby,
        },
        previewCallback: (value, onClose) => {
          this.clickFillForm(value, onClose);
        },
      });
    }
  };

  public viewPeriodicHistory = () => {
    openOneTableNewPeriodicManagementDrawer({
      itemData: this.formItem!,
      fromPage: this.fromPage,
    });
  };

  private reloadDataAndFlow = () => {
    this.reloadDataTableData();
    this.reloadFlowInfo();
  };

  private async init(options: Pick<IControllerOptions, 'fromPage' | 'formId' | 'rootWfId' | 'assignWfId'>) {
    const { fromPage, formId, rootWfId, assignWfId } = options;
    this.pageId = `_mdt_tab_${fromPage}_${assignWfId || rootWfId || formId}`;

    if (fromPage === DetailFromPageEnum.GRANTED) {
      this.isGrantedDetail = true;
      // 报表概览来的
      await this.initGrantedDetail(rootWfId!);
    } else if (fromPage === DetailFromPageEnum.TASK) {
      // 任务中心来的
      await this.initTaskDetail(assignWfId!);
    } else if (fromPage === DetailFromPageEnum.MANAGEMENT) {
      // 报表管理中心来的
      if (rootWfId) {
        await this.initManagementDetail(rootWfId!);
      } else {
        this.isDraftDetail = true;
        await this.initDraftDetail(formId!);
      }
    }
    this.loading$.next(false);
  }

  // 初始化ManagementDetail
  private async initGrantedDetail(rootWfId: string) {
    const grantedData = await this.Model.getGrantedForm(rootWfId);
    this.dataError$.next(!grantedData);
    if (!grantedData) return;
    const formData = grantedData.formData;
    this.formItem = formData;
    this.formName$.next(formData.formName);
    this.isRunning$.next(formData.isRunning);
    this.initHeaderConfig();
    this.initTabsConfig();
    this.initActiveTabKey();
    this.initGrantedOperators();
  }

  // 初始化TaskDetail
  private async initGrantedOperators() {
    const versionItem = this.getVersionItem(this.formItem!);
    // 周期需要展示切换周期按钮
    this.operators$.next([versionItem].filter(Boolean) as IOperatorItem[]);
  }

  // 初始化TaskDetail
  private async initTaskDetail(assignWfId: string) {
    const taskData = await this.Model.getTaskForm(assignWfId);
    this.dataError$.next(!taskData);
    if (!taskData) return;
    const formData = taskData.formData;
    this.taskItem = taskData;
    this.formItem = formData;
    this.formName$.next(formData.formName);
    this.isRunning$.next(formData.isRunning);
    this.initHeaderConfig();
    this.initTabsConfig();
    this.initActiveTabKey();
    if (formData.isFormManageLevelUser) {
      this.initManagementOperators();
    } else {
      this.initTaskOperators();
    }
  }

  private getVersionItem(itemData: IFormData) {
    return itemData.isPeriodic
      ? {
          type: OperatorComponentEnum.BUTTON,
          props: {
            children: i18n.chain.proMicroModules.oneTable.btnViewHistory,
            ghost: true,
            onClick: () => this.viewPeriodicHistory(),
            className: 'version-item',
          },
        }
      : null;
  }

  // eslint-disable-next-line sonarjs/cognitive-complexity
  private initTaskOperators() {
    const itemData = this.formItem!;
    const versionItem = this.getVersionItem(itemData);
    // 流程结束，不展示按钮
    if (!itemData.isRunning) {
      this.operators$.next([versionItem].filter(Boolean) as IOperatorItem[]);
      return;
    }

    const { personStatus, approvalStatus } = this.taskItem!;
    const { assignWfId, isFormManageLevelUser, isCollaborate, isNeedApproval } = itemData;
    // 这个会改变个人状态
    const clickIssueTask = () =>
      doOneTableNewOperatorIssueTask({
        itemData,
        successCallback: () => {
          this.setActiveTabKey(TabEnumKey.DELIVERY_MANAGEMENT);
          this.reloadDeliveryManagementData();
          this.initTaskDetail(assignWfId);
          this.reloadDataAndFlow();
        },
      });

    // 这个会改变个人状态
    const clickSubmitTask = () =>
      doOneTableNewOperatorSubmitTask({
        itemData,
        showCancelBtn: true,
        showSubmitBtn: true,
        submitSuccessCallback: () => {
          this.setActiveTabKey(TabEnumKey.DATA_TABLE);
          this.reloadDataTableData();
          this.initTaskDetail(assignWfId);
          this.reloadFlowInfo();
        },
      });

    // 这个不会改变状态
    const clickCollaborate = () => {
      const successCallback = () => {
        this.setActiveTabKey(TabEnumKey.COLLABORATE);
        this.reloadCollaborate();
        this.reloadFlowInfo();
      };
      isFormManageLevelUser
        ? doOneTableNewOperatorAddManageCollaborate({ itemData, successCallback })
        : doOneTableNewOperatorAddTaskCollaborate({ itemData, successCallback });
    };

    // 界面会关闭
    const clickForward = () =>
      doOneTableNewOperatorTransferTask({
        itemData,
        successCallback: this.onDetailCloseAndReload,
      });

    // 界面会关闭
    const clickRefuseTask = () =>
      doOneTableNewOperatorRefuseTask({
        itemData,
        successCallback: this.onDetailCloseAndReload,
      });

    // 会改变个人状态
    const clickCancelSubmit = () =>
      doOneTableNewOperatorCancelSubmit({
        itemData,
        successCallback: () => {
          this.initTaskDetail(assignWfId);
          this.reloadFlowInfo();
        },
      });

    // 填报按钮
    let fillBtn: IOperatorItem | null = this.enableFill()
      ? {
          type: OperatorComponentEnum.BUTTON,
          props: {
            children: i18n.chain.proMicroModules.oneTable.btnGet,
            onClick: () => this.clickFillForm(),
          },
        }
      : null;
    // 下发按钮
    let issueBtn: IOperatorItem | null = {
      type: OperatorComponentEnum.BUTTON,
      props: {
        children: i18n.chain.proMicroModules.oneTable.btnIssued2,
        onClick: clickIssueTask,
      },
    };
    // 提交按钮
    let subBtnLabel = i18n.chain.proMicroModules.oneTable.btnSubmitJob4;
    if (isNeedApproval) {
      subBtnLabel = i18n.chain.proMicroModules.oneTable.btnSubmitJob3;
      if (approvalStatus === OneTableNewDataStateEnum.REJECTED) {
        subBtnLabel = i18n.chain.proMicroModules.oneTable.btnSubmitJob5;
      }
    }
    let submitBtn: IOperatorItem | null = {
      type: OperatorComponentEnum.BUTTON,
      props: { children: subBtnLabel, type: 'primary', onClick: clickSubmitTask },
    };

    const constructUnhandledBtns = () => {
      const runningOperators: (IOperatorItem | null)[] = [versionItem, fillBtn, issueBtn];
      isFormManageLevelUser && (submitBtn = null);
      runningOperators.push(submitBtn);

      if (!isCollaborate && !isFormManageLevelUser) {
        let collector = {
          key: 'collector',
          label: i18n.chain.proMicroModules.oneTable.btnCollector,
          onClick: clickCollaborate,
          tooltip: i18n.chain.proMicroModules.oneTable.btnCollectorTooltip,
        };
        let forward = {
          key: 'forword',
          label: i18n.chain.proMicroModules.oneTable.btnForword4,
          onClick: clickForward,
          tooltip: i18n.chain.proMicroModules.oneTable.btnForword4Tooltip,
        };
        let remvoke = {
          key: 'remvoke',
          label: i18n.chain.proMicroModules.oneTable.btnRevoke,
          onClick: clickRefuseTask,
          tooltip: i18n.chain.proMicroModules.oneTable.btnRevokeTooltip,
        };

        const dropdownItems = this.excludeOperatorsFunc([collector, forward, remvoke]);
        runningOperators.push(
          { type: OperatorComponentEnum.DIVIDER, props: { type: 'vertical' } },
          {
            type: OperatorComponentEnum.DROPDOWN,
            props: { menu: { items: dropdownItems }, children: <Button ghost icon={<MoreHoriz />} onlyIcon /> },
          },
        );
      }
      this.operators$.next(runningOperators.filter(Boolean) as IOperatorItem[]);
    };

    // 已处理
    if (personStatus === TaskPersonalStatusEnum.RUNNING) {
      const runningOperators: (IOperatorItem | null)[] = [versionItem];
      if (approvalStatus === OneTableNewDataStateEnum.SUBMITTED) {
        issueBtn = null;
        submitBtn = null;
        if (!isCollaborate) {
          submitBtn = {
            type: OperatorComponentEnum.BUTTON,
            props: {
              children: i18n.chain.proMicroModules.oneTable.btnUnsubmit,
              onClick: clickCancelSubmit,
            },
          };
        }
        runningOperators.push(fillBtn, issueBtn, submitBtn);
        this.operators$.next(runningOperators.filter(Boolean) as IOperatorItem[]);
        return;
      }

      if (approvalStatus === OneTableNewDataStateEnum.APPROVED) {
        this.operators$.next(runningOperators.filter(Boolean) as IOperatorItem[]);
        return;
      }

      constructUnhandledBtns();
      return;
    }

    // 待处理
    if (personStatus === TaskPersonalStatusEnum.UNHANDLED) {
      constructUnhandledBtns();
    }
  }

  private excludeOperatorsFunc(data: any[], operatorKey = 'key') {
    return _.filter(
      _.map(data, (item) => (_.includes(this.excludeOperators, item[operatorKey]) ? null : item)),
      Boolean,
    );
  }

  // 初始化ManagementDetail
  private async initManagementDetail(rootWfId: string) {
    const managementData = await this.Model.getManagementForm(rootWfId);
    this.dataError$.next(!managementData);
    if (!managementData) return;
    const formData = managementData.formData;
    this.formItem = formData;
    this.formName$.next(formData.formName);
    this.isRunning$.next(formData.isRunning);
    this.initHeaderConfig();
    this.initTabsConfig();
    this.initActiveTabKey();
    this.initManagementOperators();
  }

  private initManagementOperators() {
    const itemData = this.formItem!;

    const clickIssueTask = () =>
      doOneTableNewOperatorIssueTask({
        itemData,
        successCallback: () => {
          this.setActiveTabKey(TabEnumKey.DELIVERY_MANAGEMENT);
          this.reloadDeliveryManagementData();
          this.reloadFlowInfo();
          this.reloadDataTableData();
        },
      });

    const clickCopyCreate = () =>
      doOneTableNewOperatorCopyCreate({
        formType: itemData.formType,
        fromFormId: itemData.formId,
        onSuccessCb: this.onDetailCloseAndReload,
        onCancelCb: this.onDetailCloseAndReloadExtra,
      });

    const clickTransfer = () =>
      doOneTableNewOperatorTransferManage({
        itemData,
        successCallback: this.onDetailCloseAndReload,
      });

    const clickCompleteFlow = () =>
      doOneTableNewOperatorCompleteFlow({
        itemData,
        successCallback: () => {
          this.initManagementDetail(itemData.rootWfId);
          this.setActiveTabKey(TabEnumKey.FLOW_INFO);
          this.reloadFlowInfo();
        },
      });

    const clickAddMore = () =>
      doOneTableNewOperatorAddManageCollaborate({
        itemData,
        successCallback: () => {
          this.setActiveTabKey(TabEnumKey.COLLABORATE);
          this.reloadCollaborate();
          this.reloadFlowInfo();
        },
      });

    const clickDeleteWorkflow = () =>
      doOneTableNewOperatorDeleteWorkflow({
        itemData,
        successCallback: () => {
          toastApi.success(i18n.chain.comTip.deleteSuccess);
          this.onDetailCloseAndReload();
        },
      });

    const versionItem = this.getVersionItem(itemData);
    // 流程结束，不展示按钮
    if (!itemData.isRunning) {
      const dropdownItems = this.excludeOperatorsFunc([
        { label: i18n.chain.proMicroModules.oneTable.btnCopyCreate, key: 'copyCreate', onClick: clickCopyCreate },
        {
          label: i18n.chain.proMicroModules.oneTable.btnCleanWf,
          key: 'clean',
          danger: true,
          onClick: clickDeleteWorkflow,
        },
      ]);
      const operators = [
        versionItem,
        {
          type: OperatorComponentEnum.DROPDOWN,
          props: { menu: { items: dropdownItems }, children: <Button ghost icon={<MoreHoriz />} onlyIcon /> },
        },
      ].filter(Boolean) as IOperatorItem[];
      this.operators$.next(operators);
      return;
    }

    const { isCollaborate } = itemData;
    // 填报
    const fillItem = this.enableFill()
      ? {
          type: OperatorComponentEnum.BUTTON,
          props: { children: i18n.chain.proMicroModules.oneTable.btnGet, onClick: () => this.clickFillForm() },
        }
      : null;

    // 新增协同
    const addMoreItem = isCollaborate
      ? null
      : {
          label: i18n.chain.proMicroModules.oneTable.btnCollector,
          tooltip: i18n.chain.proMicroModules.oneTable.btnCollectorTooltip2,
          key: 'addMore',
          onClick: clickAddMore,
        };

    // 复制
    const copyCreate = {
      label: i18n.chain.proMicroModules.oneTable.btnCopyCreate,
      key: 'copyCreate',
      onClick: clickCopyCreate,
    };

    // 转交报表
    const transferItem = isCollaborate
      ? null
      : {
          label: i18n.chain.proMicroModules.oneTable.btnTransferForm,
          tooltip: i18n.chain.proMicroModules.oneTable.btnTransferFormTip,
          key: 'transfer',
          onClick: clickTransfer,
        };

    // 结束流程
    const completeFlow = isCollaborate
      ? null
      : {
          label: i18n.chain.proMicroModules.oneTable.finishJob,
          key: 'completeFlow',
          danger: true,
          onClick: clickCompleteFlow,
        };

    const dropdownItems = this.excludeOperatorsFunc(
      [addMoreItem, copyCreate, transferItem, completeFlow].filter(Boolean),
    );

    const operators = _.filter(
      [
        versionItem,
        fillItem,
        {
          type: OperatorComponentEnum.BUTTON,
          props: { children: i18n.chain.proMicroModules.oneTable.btnIssued2, onClick: clickIssueTask },
        },
        { type: OperatorComponentEnum.DIVIDER, props: { type: 'vertical' } },
        {
          type: OperatorComponentEnum.DROPDOWN,
          props: { menu: { items: dropdownItems }, children: <Button ghost icon={<MoreHoriz />} onlyIcon /> },
        },
      ],
      Boolean,
    ) as IOperatorItem[];
    this.operators$.next(operators);
  }

  private reloadDeliveryManagementData = () => {
    this.getDeliveryManagementController().getListController().loadDataList();
  };

  private reloadDataTableData = () => {
    this.getDataTableController().loadDataList();
  };

  private reloadFlowInfo = () => {
    this.getFlowInfoController().loadData();
  };

  private reloadCollaborate = () => {
    this.getCollaborateController().loadData();
  };

  // 初始化DraftDetail
  private async initDraftDetail(formId: string) {
    const formData = await this.Model.getDraftForm(formId);
    this.dataError$.next(!formData);
    if (!formData) return;
    this.formItem = formData;
    this.formName$.next(formData.formName);
    this.initHeaderConfig();
    this.initTabsConfig();
    this.initActiveTabKey();
    this.initDraftOperators();
  }

  private initDraftOperators() {
    const { formId, formType, pkgId } = this.formItem!;

    if (pkgId) {
      this.operators$.next([]);
      return;
    }

    // 点击编辑
    const clickEdit = () => {
      doOneTableNewOperatorDraftEdit({
        formId,
        formType,
        onSuccessCb: this.onDetailCloseAndReloadExtra,
        onCancelCb: async () => {
          await this.initDraftDetail(formId);
          this.formInfoController?.loadData(this.formItem?.originItem);
        },
      });
    };
    // 点击立即发布
    const clickPublish = () => {
      doOneTableNewOperatorPublish({ formId, onSuccessCallback: this.onDetailCloseAndReloadExtra });
    };

    const buttonGroupDropdownItems = [
      { label: i18n.chain.proMicroModules.oneTable.scheduledJob, key: 'scheduling', disabled: true },
    ];

    const clickDel = () => {
      doOneTableNewOperatorDeleteDraft({ formId, successCallback: this.onDetailCloseAndReload });
    };
    const dropdownItems = [
      { label: i18n.chain.proMicroModules.oneTable.deleteForm, key: 'deleteForm', onClick: clickDel },
    ];

    const operators: IOperatorItem[] = [
      {
        type: OperatorComponentEnum.BUTTON,
        props: {
          children: i18n.chain.proMicroModules.oneTable.reportEdit,
          onClick: clickEdit,
        },
      },
      {
        type: OperatorComponentEnum.BUTTON_GROUP,
        props: {
          children: (
            <>
              <Button
                type="primary"
                onClick={clickPublish}
                showTooltip
                tooltipProps={{ placement: 'bottom' }}
                tooltipTitle={i18n.chain.proMicroModules.oneTable.publishTextTip}
              >
                {i18n.chain.proMicroModules.oneTable.publishJob}
              </Button>
              <Dropdown menu={{ items: buttonGroupDropdownItems }}>
                <Button type="primary" icon={<ChevronDown />} onlyIcon withoutBorder />
              </Dropdown>
            </>
          ),
        },
      },
      { type: OperatorComponentEnum.DIVIDER, props: { type: 'vertical' } },
      {
        type: OperatorComponentEnum.DROPDOWN,
        props: { menu: { items: dropdownItems }, children: <Button ghost icon={<MoreHoriz />} onlyIcon /> },
      },
    ];
    this.operators$.next(operators);
  }

  private initActiveTabKey() {
    let { items } = this.tabsConfig!;
    let key = items[0].key;
    if (!this.isDraftDetail) {
      const storyKey = sessionStorage.getItem(this.pageId);
      key = _.get(storyKey && _.find(items, { key: storyKey }), 'key', key);
    }
    this.activeTabKey$.next(key as TabEnumKey);
  }

  private resetDataTableTab() {
    const tab = _.find(this.tabsConfig?.items, { key: TabEnumKey.DATA_TABLE });
    if (tab) {
      this.dataTableController?.destroy();
      this.dataTableController = undefined;
      tab.children = <OneTableNewDataTableStatics key={Math.random()} controller={this.getDataTableController()} />;
    }
  }

  private initTabsConfig() {
    const DeliveryManagementView = this.getDeliveryManagement();
    const tabConfigs = [
      {
        key: TabEnumKey.DATA_TABLE,
        label: i18n.chain.proMicroModules.oneTable.fillDataDetail,
        children: () => <OneTableNewDataTableStatics controller={this.getDataTableController()} />,
      },
      {
        key: TabEnumKey.DELIVERY_MANAGEMENT,
        label: i18n.chain.proMicroModules.oneTable.btnReApproval,
        children: () => <DeliveryManagementView controller={this} />,
      },
      {
        key: TabEnumKey.FLOW_INFO,
        label: i18n.chain.proMicroModules.oneTable.flowDetail,
        children: () => <FlowInfo controller={this} />,
      },
      {
        key: TabEnumKey.FORM_INFO,
        label: i18n.chain.proMicroModules.oneTable.reportConfig,
        children: () => <FormInfo controller={this} />,
      },
      {
        key: TabEnumKey.COLLABORATE,
        label: i18n.chain.proMicroModules.oneTable.collaborate,
        children: () => <Collaborate controller={this} />,
      },
    ];

    let items = tabConfigs.filter(({ key }) => this.tabsVisible[key]);

    if (this.isDraftDetail) {
      items = tabConfigs.filter(({ key }) => this.tabsVisible[key] && _.eq(key, TabEnumKey.FORM_INFO));
    }

    this.tabsConfig = {
      items: items.map((item) => ({ ...item, children: item.children() })),
      onChange: (key: TabEnumKey) => this.setActiveTabKey(key),
    };
  }

  private initHeaderConfig() {
    const { isEndless, isPeriodic, isCollaborate } = this.formItem!;
    const other = _.pick(this.taskItem, 'approvalStatus', 'reason', 'personStatus');
    this.headerConfig$.next({
      iconConfig: { isPeriodic, isEndless },
      isEndless,
      isPeriodic,
      isCollaborate,
      ...other,
    });
  }

  private initTabsVisible(excludeTabs?: TabEnumKey[]) {
    _.forEach(excludeTabs, (key) => {
      this.tabsVisible[key] = false;
    });
  }

  private onDetailCloseAndReloadExtra = () => {
    this.onReloadExtra?.();
    this.onDetailCloseAndReload();
  };

  private onDetailCloseAndReload = () => {
    this.onReload?.();
    this.onClose?.();
  };

  private listenRuningStatus() {
    this.isRunning$.pipe(skip(1)).subscribe((running) => {
      const {
        [TabEnumKey.COLLABORATE]: vc,
        [TabEnumKey.DELIVERY_MANAGEMENT]: vd,
        [TabEnumKey.FORM_INFO]: vf,
      } = this.tabsVisible;
      if (this.isGrantedDetail) {
        vd && this.getDeliveryManagementController().modifyShowApprovalBtn(false);
        return;
      }
      const { isCollaborate, isFormManageLevelUser } = this.formItem!;
      // 是管理员或者
      vc && this.getCollaborateController().changeShowEditBtn(running && (isFormManageLevelUser || !isCollaborate));
      // 只有运行中才有催办
      vd && this.getDeliveryManagementController().changeShowExpediteBtn(running);
      // 运行中且是管理员
      vf && this.getFormInfoController().changeShowEditBtn(running && isFormManageLevelUser);
    });
  }
}
