import type { IFloworkForm, IOnetableInvolvedForm, IOnetableManagedForm } from '@mdtApis/interfaces';
import {
  getFloworkFormAsync,
  postFloworkTaskCompleteAsync,
  queryOnetableInvolvedFormsAsync,
  queryOnetableManagedFormsAsync,
  queryOnetableManageTasksAsync,
} from '@mdtBsServices/flowork';
import { OneTableNewDataStateEnum } from '@mdtProComm/constants';
import type { IOneTableNewOperatorDataComm } from '../../interfacts';
import {
  FormTypeEnum,
  getBindPkgId,
  getEndDate,
  getFormOwner,
  getFormType,
  getFrontendPersonalStatus,
  getPeriodicVersionName,
  getPrimaryAssignee,
  getVersionGroup,
  getWorkflowIsRuning,
  isCollaborateTask,
  isEndlessForm,
  isFormManageLevelUser,
  isNeedApproval,
  isPeriodicForm,
  TaskPersonalStatusEnum,
  transformFeatureFlagsToFrontEnd,
  transformPersonalStatusToFrontend,
} from '../../utils/oneTableNewUtil';

export interface IFormData extends IOneTableNewOperatorDataComm {
  formType: FormTypeEnum;
  originItem: IFloworkForm;
  endDate: string;
  isRunning: boolean;
}

type IOtherFillData = Pick<
  IFormData,
  | 'isCollaborate'
  | 'assignWfId'
  | 'rootWfId'
  | 'primaryAssignee'
  | 'isFormManageLevelUser'
  | 'isNeedApproval'
  | 'isRunning'
  | 'versionName'
  | 'versionGroup'
>;

export interface IManagementData {
  formData: IFormData;
}

export interface ITaskData {
  formData: IFormData;
  personStatus: TaskPersonalStatusEnum;
  approvalStatus: OneTableNewDataStateEnum;
  reason?: string;
}

export class OneTableNewReportDetailModel {
  public static transformToFormData(item: IFloworkForm, reset: IOtherFillData): IFormData {
    const { id, name, extra_meta: extraMeta = {}, form_spec: formSpec } = item;
    const formType = getFormType(extraMeta);
    return {
      ...reset,
      formId: id,
      formName: name,
      formType: formType,
      formSpec: formSpec,
      formOwner: getFormOwner(item),
      pkgId: getBindPkgId(extraMeta),
      isPeriodic: isPeriodicForm(extraMeta),
      isEndless: isEndlessForm(extraMeta),
      endDate: getEndDate(extraMeta) || '--',
      originItem: item,
      featureFlags: transformFeatureFlagsToFrontEnd(extraMeta),
    };
  }

  public static transformToManagementData(item: IOnetableManagedForm): IManagementData {
    const assignWf = item.assign_workflow!;
    const rootWfId = item.root_workflow_id!;
    const data = assignWf.data || {};
    const isCollaborate = isCollaborateTask(data);
    const formData = this.transformToFormData(item.bind_form as IFloworkForm, {
      isCollaborate,
      rootWfId,
      assignWfId: item.assign_workflow_id!,
      primaryAssignee: getPrimaryAssignee(data),
      isFormManageLevelUser: isFormManageLevelUser(rootWfId, assignWf.parent_workflow_id),
      isNeedApproval: isNeedApproval(data),
      isRunning: getWorkflowIsRuning(assignWf.status),
      versionName: getPeriodicVersionName(item),
      versionGroup: getVersionGroup(item),
    });
    return { formData };
  }

  public static transformToTaskData(item: IOnetableInvolvedForm): ITaskData {
    const assignWf = item.assign_workflow!;
    const data = assignWf.data || {};
    const rootWfId = item.root_workflow_id;
    const isCollaborate = isCollaborateTask(data);
    const formData = this.transformToFormData(item.bind_form as IFloworkForm, {
      isCollaborate,
      rootWfId,
      assignWfId: item.assign_workflow_id!,
      primaryAssignee: getPrimaryAssignee(assignWf.data),
      isFormManageLevelUser: isFormManageLevelUser(rootWfId, assignWf.parent_workflow_id),
      isNeedApproval: isNeedApproval(data),
      isRunning: getWorkflowIsRuning(assignWf.status),
      versionName: getPeriodicVersionName(item),
      versionGroup: getVersionGroup(item),
    });
    const dataStatus = getFrontendPersonalStatus(item.onetable_personal_status!, assignWf.status);
    const [aps, reason] = transformPersonalStatusToFrontend(item, dataStatus);

    return {
      formData,
      approvalStatus: aps,
      reason,
      personStatus: dataStatus,
    };
  }

  public static async notifyFormAdminTask(rootWfId: string) {
    const resp = await queryOnetableManageTasksAsync(
      { workflow_id: rootWfId, task_status: ['ready'], workflow_status: ['ready'], orderby: '-update_time' },
      { quiet: true },
    );
    const taskId = resp.data?.[0].task_id;
    if (!taskId) return;
    await postFloworkTaskCompleteAsync(
      taskId,
      { approval_result: 'approved', form_data: { command: 'update' } },
      { quiet: true },
    );
  }

  public static async getDraftForm(formId?: string, reset?: IOtherFillData) {
    if (!formId) return;
    const resp = await getFloworkFormAsync(formId);
    return resp.data
      ? this.transformToFormData(resp.data, {
          isCollaborate: false,
          rootWfId: '',
          assignWfId: '',
          primaryAssignee: 0,
          isFormManageLevelUser: false,
          isNeedApproval: false,
          isRunning: false,
          versionName: '',
          versionGroup: 0,
          ...reset,
        })
      : undefined;
  }

  public static async getManagementForm(rootWfId?: string) {
    if (!rootWfId) return;
    const resp = await queryOnetableManagedFormsAsync({
      root_workflow_id: rootWfId,
      with_form_info: true,
      with_assign_workflow_info: true,
    });
    const data = resp.data?.[0];
    return data ? this.transformToManagementData(data) : undefined;
  }

  public static async getTaskForm(assignWfId?: string) {
    if (!assignWfId) return;
    const resp = await queryOnetableInvolvedFormsAsync({
      assign_workflow_id: assignWfId,
      with_form_info: true,
      with_assign_workflow_info: true,
    });
    const data = resp.data?.[0];
    return data ? this.transformToTaskData(data) : undefined;
  }
}
export type IOneTableNewReportDetailModel = typeof OneTableNewReportDetailModel;
