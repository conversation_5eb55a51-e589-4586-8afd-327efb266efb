import _ from 'lodash';
import { FC, useState } from 'react';
import { HelpOutlined } from '@metro/icons';
import { ActionSheet } from '@metro/mobile-components/dist/esm/action-sheet';
import { Popup } from '@metro/mobile-components/dist/esm/popup';
import { But<PERSON> } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { Toggle } from '@metroDesign/toggle';
import { Typography } from '@metroDesign/typography';
import { If } from '@mdtBsComm/components/if';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../../components/transform-id-to-name';
import { CardCurdWithSimpleSearch } from '../../../../containers/card-curd-with-simple-search';
import i18n from '../../../../languages';
import { buildDataDeliveryTooltip } from '../../../../utils/oneTableNewUtil';
import type { IProps } from '../../OneTableNewReportDetail';
import { type ITableData } from '../delivery-management/DeliveryManagementModel';
import { DeliveryManagementH5Controller } from './DeliveryManagementH5Controller';
import './index.less';

export const ListItemView: FC<{ data: ITableData; onClick?: any }> = ({ data, onClick }) => {
  const { primaryAssignee, userIds, dataStateDisplay, dealTime, reason } = data;
  const [popupVisible, setPopupVisible] = useState(false);
  const [showReason, setShowReason] = useState(false);

  const handleShowDataDelivery = (e: any) => {
    e.stopPropagation();
    setPopupVisible(true);
  };

  const handleClosePopup = () => {
    setPopupVisible(false);
  };

  const showReasonFunc = (e: any) => {
    e.stopPropagation();
    setShowReason(true);
  };

  return (
    <Flex className="one-table-new-delivery-management-h5-item-view" onClick={onClick}>
      <Flex flex="1 1 auto" vertical gap={6} align="flex-start">
        <Flex justify="space-between" gap="small" block>
          <Space size={4} className="item-tag" wrap>
            {_.map(userIds, (user, index) => (
              <Tag key={user} {...(!index && { color: 'primary', type: 'light', bordered: false })}>
                <TransformIdToName id={user} type="user_id" />
              </Tag>
            ))}
          </Space>

          <Tag color={dataStateDisplay[1]} type="light" bordered={false} onClick={showReasonFunc}>
            {dataStateDisplay[0]}
            {reason ? <HelpOutlined /> : null}
          </Tag>
        </Flex>
        <Typography.Text strong className="item-title">
          <TransformUserIdToOrgName placement="right" id={primaryAssignee} />
        </Typography.Text>
        <Typography.Text type="secondary">
          {i18n.chain.proMicroModules.oneTable.tableColumns.issuedTime}:{dealTime}
        </Typography.Text>
        {data.dataDeliveryInfo ? (
          <Button.Link primary onClick={handleShowDataDelivery}>
            {i18n.chain.proMicroModules.oneTable.tableColumns.deliveryDataCount(
              data.dataDeliveryInfo.stat_include_downstream?.total_assign_rows,
              `${data.dataDeliveryInfo.percent}%`,
            )}
          </Button.Link>
        ) : (
          '-'
        )}
        <Popup visible={popupVisible} onClose={handleClosePopup} closeOnMaskClick>
          <div className="one-table-new-delivery-management-h5-item-view-delivery-popup">
            <Typography.Text strong className="item-title">
              <TransformUserIdToOrgName placement="right" id={primaryAssignee} />
            </Typography.Text>
            <pre>{buildDataDeliveryTooltip(data.dataDeliveryInfo)}</pre>
          </div>
        </Popup>
        <Popup visible={showReason} onClose={() => setShowReason(false)} closeOnMaskClick>
          <Flex
            className="one-table-new-delivery-management-h5-item-view-reason-popup"
            gap="middle"
            vertical
            align="flex-start"
          >
            <Tag color={dataStateDisplay[1]} type="light" bordered={false}>
              {dataStateDisplay[0]}
            </Tag>
            <Typography.Text>{i18n.chain.proMicroModules.oneTable.reasonTip(reason)}</Typography.Text>
          </Flex>
        </Popup>
      </Flex>
    </Flex>
  );
};

const Tools: FC<{ controller: DeliveryManagementH5Controller }> = ({ controller }) => {
  const showExpediteBtn = useObservableState(controller.getShowExpediteBtn$());
  const showDeliveryCancelOrRejectList = useObservableState(controller.getShowDeliveryCancelOrRejectList$());
  const tableLoading = useObservableState(controller.getListController().getDataListLoading$());
  const isEmpty = useObservableState(controller.getListController().getDataListEmpty$());

  const expediteBtn = showExpediteBtn ? (
    <Button.Link primary onClick={() => controller.doOperatorExpediteAll()}>
      {i18n.chain.proMicroModules.oneTable.expediteAll}
    </Button.Link>
  ) : null;

  return (
    <Flex className="delivery-management-tools" justify="space-between" align="center">
      {!isEmpty ? (
        <Toggle
          checked={showDeliveryCancelOrRejectList}
          onChange={(checked) => controller.changeShowDeliveryCancelOrRejectList(checked)}
          unCheckedChildren={i18n.chain.proMicroModules.oneTable.viewAll}
          checkedChildren={i18n.chain.proMicroModules.oneTable.hadIsuuedTip}
          loading={!!tableLoading}
        />
      ) : (
        <Typography.Text strong>{i18n.chain.proMicroModules.oneTable.hadIsuuedTip}</Typography.Text>
      )}
      <Space>
        {expediteBtn}
        <Button.Link primary onClick={() => controller.viewFlowMapFunc()}>
          {i18n.chain.proMicroModules.oneTable.viewFlowMap}
        </Button.Link>
      </Space>
    </Flex>
  );
};

export const DeliveryManagementH5: FC<IProps> = ({ controller }) => {
  const ctrl = controller.getDeliveryManagementController() as DeliveryManagementH5Controller;
  const listCtrl = ctrl.getListController();
  const isEmpty = useObservableState(listCtrl.getDataListEmpty$());
  const actionVisible = useObservableState(ctrl.getActionVisible$());
  const isTableLoading = useObservableState(listCtrl.getDataListLoading$());
  const showEmptyAdd = isEmpty && !isTableLoading;

  return (
    <div className="delivery-management">
      <If
        data={!showEmptyAdd}
        else={
          <div className="delivery-management-newadd">
            <Typography.Text type="secondary" strong>
              {i18n.chain.proMicroModules.oneTable.notIssueyet}
            </Typography.Text>
          </div>
        }
      >
        <Tools controller={ctrl} />
        <CardCurdWithSimpleSearch controller={listCtrl} />
        <ActionSheet visible={actionVisible} actions={ctrl.getActions()} onClose={() => ctrl.closeAction()} />
      </If>
    </div>
  );
};
