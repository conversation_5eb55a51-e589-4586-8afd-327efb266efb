import { BehaviorSubject } from 'rxjs';
import { IWorkflow } from '@mdtApis/interfaces';
import { DatlasAppController } from '../../../../datlas/app/DatlasAppController';
import type { IOneTableNewOperatorDataComm } from '../../../../interfacts';
import i18n from '../../../../languages';
import {
  doOneTableNewOperatorEditCollaborate,
  doOneTableNewOperatorEditManageCollaborate,
} from '../../../../shared/onetablenew';
import { ICollaborateModel } from './CollaborateModel';

interface IControllerOptions {
  Model: ICollaborateModel;
  itemData: IOneTableNewOperatorDataComm;
  successCallback: () => void;
}

class CollaborateController {
  private Model: ICollaborateModel;
  private currentUserId: number;
  private successCallback: IControllerOptions['successCallback'];
  private itemData: IOneTableNewOperatorDataComm;

  private admin$ = new BehaviorSubject<number>(0);
  private users$ = new BehaviorSubject<number[]>([]);
  private isSelfAdmin$ = new BehaviorSubject<boolean>(false);
  private showEditBtn$ = new BehaviorSubject<boolean>(false);

  public constructor(options: IControllerOptions) {
    this.Model = options.Model;
    this.itemData = options.itemData;
    this.successCallback = options.successCallback;
    this.currentUserId = DatlasAppController.getInstance().getUserId();
  }

  public destroy() {
    this.Model = null!;
    this.itemData = null!;
    this.admin$.complete();
    this.users$.complete();
    this.isSelfAdmin$.complete();
    this.showEditBtn$.complete();
    this.successCallback = null!;
  }

  public getShowEditBtn$() {
    return this.showEditBtn$;
  }

  public changeShowEditBtn(show?: boolean) {
    this.showEditBtn$.next(!!show);
  }

  public getSelfTip = (userId: number): [string, Record<string, any>] => {
    return userId === this.currentUserId
      ? [i18n.chain.proMicroModules.oneTable.self, { type: 'light', color: 'primary', bordered: false }]
      : ['', { type: 'solid' }];
  };

  public getIsSelfValue() {
    return this.isSelfAdmin$.getValue();
  }

  public getIsSelfAdmin$() {
    return this.isSelfAdmin$;
  }

  public getAdmin$() {
    return this.admin$;
  }

  public getUsers$() {
    return this.users$;
  }

  public onEdit = () => {
    const { itemData } = this;
    const params = {
      itemData,
      users: this.users$.getValue(),
      successCallback: this.successCallback!,
    };
    itemData.isFormManageLevelUser
      ? doOneTableNewOperatorEditManageCollaborate(params)
      : doOneTableNewOperatorEditCollaborate(params);
  };

  public loadData = async (flowData?: IWorkflow['data']) => {
    const [admin, ...reset] = await this.Model.queryCollaborate(this.itemData.assignWfId, flowData);
    this.admin$.next(admin);
    this.users$.next(reset);
    this.isSelfAdmin$.next(admin === this.currentUserId);
  };
}

export { CollaborateController };
