import _ from 'lodash';
import { FC } from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { Toggle } from '@metroDesign/toggle';
import { Typography } from '@metroDesign/typography';
import { If } from '@mdtBsComm/components/if';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { DataListCompTableWithCurd } from '@mdtBsComponents/data-list-comp-table-curd';
import { OneTableNewDataStateEnum } from '@mdtProComm/index';
import { OneTableNewCardIcon } from '../../../../components/one-table-new-card-icon';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../../components/transform-id-to-name';
import i18n from '../../../../languages';
import { getReasonAndTitle } from '../../../../utils/oneTableNewUtil';
import { OneTableNewDataTableStatics } from '../../../one-table-new-data-table-statics';
import type { IProps } from '../../OneTableNewReportDetail';
import approval from './_resources/approval.svg';
import reject from './_resources/reject.svg';
import { DeliveryManagementBaseController } from './DeliveryManagementBaseController';
import { DeliveryManagementController } from './DeliveryManagementController';
import { ITableData } from './DeliveryManagementModel';
import './index.less';

interface IDMProps {
  controller: DeliveryManagementBaseController;
  item: ITableData;
  onClose: () => void;
  approvalAgain?: boolean;
  needApproval?: boolean;
  isEndless?: boolean;
  isPeriodic?: boolean;
  showApprovalBtn?: boolean;
}

export const DeliveryManagementDetail: FC<IDMProps> = ({
  controller,
  item,
  onClose,
  approvalAgain,
  needApproval,
  isEndless,
  isPeriodic,
  showApprovalBtn = true,
}) => {
  const dataTableController = controller.getDataTableController();
  const { dataState, approvalStatusDisplay } = item;

  const tagEle =
    needApproval && dataState === OneTableNewDataStateEnum.SUBMITTED ? (
      <Tag type="light" color={approvalStatusDisplay[1]}>
        {approvalStatusDisplay[0]}
      </Tag>
    ) : null;

  const iconSrc =
    needApproval &&
    ((dataState === OneTableNewDataStateEnum.APPROVED && approval) ||
      (dataState === OneTableNewDataStateEnum.REJECTED && reject));

  const [title, reason] = getReasonAndTitle(item.reason, item.dataState as OneTableNewDataStateEnum);
  const reasonEle = title ? (
    <Space>
      <Typography.Text strong type="secondary">
        {title}
      </Typography.Text>
      <Typography.Text strong>{reason || '--'}</Typography.Text>
    </Space>
  ) : null;

  const footerEle =
    showApprovalBtn && needApproval && (approvalAgain || item.dataState === OneTableNewDataStateEnum.SUBMITTED) ? (
      <div className="detail-footer">
        <Space>
          <Button type="primary" danger onClick={() => controller.doOperatorReject(item, onClose)}>
            {i18n.chain.proMicroModules.oneTable.btnRefuse}
          </Button>
          <Button type="primary" onClick={() => controller.doOperatorApproval(item, onClose)}>
            {i18n.chain.proMicroModules.oneTable.btnPass}
          </Button>
        </Space>
      </div>
    ) : null;

  const usersEle = _.map(item.userIds, (user, index) => (
    <Tag key={user} {...(!index && { color: 'primary', type: 'light', bordered: false })}>
      <TransformIdToName id={user} type="user_id" />
    </Tag>
  ));

  return (
    <div className="delivery-management-detail">
      <div className="detail-title">
        <Space>
          <OneTableNewCardIcon isEndless={isEndless} isPeriodic={isPeriodic} />
          <Typography.Title level={5} style={{ marginBottom: 0 }}>
            {controller.getFormName()}
          </Typography.Title>
          {tagEle}
        </Space>
      </div>
      <div className="detail-description">
        <Flex justify="flex-start" gap="middle">
          <Typography.Text strong type="secondary">
            {i18n.chain.proMicroModules.oneTable.tableColumns.userName}
          </Typography.Text>
          <Space wrap size={6}>
            {usersEle}
          </Space>
        </Flex>
        <Space>
          <Typography.Text strong type="secondary">
            {i18n.chain.proMicroModules.oneTable.tableColumns.departName}
          </Typography.Text>
          <Tag>
            <TransformUserIdToOrgName placement="right" id={item.primaryAssignee} />
          </Tag>
        </Space>
        <Space>
          <Typography.Text strong type="secondary">
            {i18n.chain.proMicroModules.oneTable.tableColumns.issuedTime}
          </Typography.Text>
          <Typography.Text strong>{item.dealTime}</Typography.Text>
        </Space>
        {reasonEle}
        {iconSrc ? <img alt="" src={iconSrc} /> : null}
      </div>
      <Typography.Title level={5} style={{ marginBottom: 0 }}>
        {i18n.chain.proMicroModules.oneTable.fillDataDetail}
      </Typography.Title>
      {dataTableController ? <OneTableNewDataTableStatics controller={dataTableController} /> : null}
      {footerEle}
    </div>
  );
};

interface IControllers {
  controller: DeliveryManagementController;
}

const Tools: FC<IControllers> = ({ controller }) => {
  const showExpediteBtn = useObservableState(controller.getShowExpediteBtn$());
  const showDeliveryCancelOrRejectList = useObservableState(controller.getShowDeliveryCancelOrRejectList$());
  const tableLoading = useObservableState(controller.getListController().getDataListLoading$());
  const isEmpty = useObservableState(controller.getListController().getDataListEmpty$());

  const expediteBtn = showExpediteBtn ? (
    <Button.Link primary onClick={() => controller.doOperatorExpediteAll()}>
      {i18n.chain.proMicroModules.oneTable.expediteAll}
    </Button.Link>
  ) : null;

  return (
    <Space className="delivery-management-tools">
      <Typography.Text strong>{i18n.chain.proMicroModules.oneTable.hadIsuuedTip}</Typography.Text>
      <Flex align="center" gap="middle">
        {!isEmpty ? (
          <Toggle
            checked={showDeliveryCancelOrRejectList}
            onChange={(checked) => controller.changeShowDeliveryCancelOrRejectList(checked)}
            checkedChildren={i18n.chain.proMicroModules.oneTable.hiddenDeliveryCancelOrRejectList}
            unCheckedChildren={i18n.chain.proMicroModules.oneTable.viewDeliveryCancelOrRejectList}
            loading={!!tableLoading}
          />
        ) : null}
        {expediteBtn}
        <Button.Link primary onClick={() => controller.doOperatorViewFlowMap()}>
          {i18n.chain.proMicroModules.oneTable.viewFlowMap}
        </Button.Link>
      </Flex>
    </Space>
  );
};

export const DeliveryManagement: FC<IProps> = ({ controller }) => {
  const ctrl = controller.getDeliveryManagementController() as DeliveryManagementController;
  const listCtrl = ctrl.getListController();
  const isEmpty = useObservableState(listCtrl.getDataListEmpty$());
  const isTableLoading = useObservableState(listCtrl.getDataListLoading$());
  const showEmptyAdd = isEmpty && !isTableLoading;

  return (
    <div className="delivery-management">
      <If
        data={!showEmptyAdd}
        else={
          <div className="delivery-management-newadd">
            <Typography.Text type="secondary" strong>
              {i18n.chain.proMicroModules.oneTable.notIssueyet}
            </Typography.Text>
          </div>
        }
      >
        <Tools controller={ctrl} />
        <DataListCompTableWithCurd controller={listCtrl} />
      </If>
    </div>
  );
};
