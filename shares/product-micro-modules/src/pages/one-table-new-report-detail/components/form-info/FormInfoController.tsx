import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { IFloworkForm } from '@mdtApis/interfaces';
import { ONE_TABLE_INFO } from '../../../../datlas/datlasConfig';
import { doOneTableNewOperatorEditSetting } from '../../../../shared/onetablenew';
import { FormTypeEnum, getFormTypeSpec } from '../../../../utils/oneTableNewUtil';
import type { IFormInfoModel } from './FormInfoModel';

interface IControllerOptions {
  Model: IFormInfoModel;
  formId: string;
  successCallback?: () => void;
  cancelCallback?: () => void;
}

class FormInfoController {
  private Model: IFormInfoModel;
  private formId: string;
  private formType?: FormTypeEnum;
  private formSpec?: Record<string, any>;
  private flowSpec?: Record<string, any>;

  private loading$ = new BehaviorSubject<boolean>(true);
  private formData$ = new BehaviorSubject<any>({});
  private flowData$ = new BehaviorSubject<any>({});
  private formOriginalData$ = new BehaviorSubject<IFloworkForm | undefined>(undefined);
  private showEditBtn$ = new BehaviorSubject<boolean>(false);
  private successCallback?: () => void;

  public constructor(options: IControllerOptions) {
    this.Model = options.Model;
    this.formId = options.formId;
    this.successCallback = options.successCallback;
  }

  public destroy() {
    this.Model = null!;
    this.formSpec = undefined;
    this.flowSpec = undefined;
    this.successCallback = undefined;
    this.formData$.complete();
    this.flowData$.complete();
    this.loading$.complete();
    this.showEditBtn$.complete();
    this.formOriginalData$.complete();
  }

  public getShowEditBtn$() {
    return this.showEditBtn$;
  }

  public changeShowEditBtn(show?: boolean) {
    this.showEditBtn$.next(!!show);
  }

  public getLoading$() {
    return this.loading$;
  }

  public getFormData$() {
    return this.formData$;
  }

  public getFlowData$() {
    return this.flowData$;
  }

  public getFormOriginalData$() {
    return this.formOriginalData$;
  }

  public getFlowSpec() {
    return this.flowSpec;
  }

  public getFormSpec() {
    return this.formSpec;
  }

  public edit = () => {
    doOneTableNewOperatorEditSetting({
      formId: this.formId,
      formType: this.formType!,
      reportInfo: this.formData$.getValue(),
      configData: this.flowData$.getValue(),
      successCallback: this.successCallback,
    });
  };

  public async loadData(flowForm?: IFloworkForm) {
    const { formData, flowData, data, formType } = await this.Model.getForm(this.formId, flowForm).toPromise();
    this.formType = formType;
    this.formData$.next(formData);
    this.flowData$.next(flowData);
    this.formOriginalData$.next(data);
    this.generateFormSpec();
    this.generateFlowSpec();
    this.loading$.next(false);
  }

  private generateFormSpec() {
    const { formilySchema, allSettingValues } = ONE_TABLE_INFO.reportConfig.formSpec;
    const varProperties: any = {};
    _.map(Object.keys(formilySchema.schema.properties), (field) => {
      const cur = formilySchema.schema.properties[field];
      const xDecoratorProps = cur['x-decorator-props'] || {};
      varProperties[field] = {
        ...cur,
        'x-decorator-props': {
          ...xDecoratorProps,
          layout: 'vertical',
        },
      };
    });
    const girdSchema = {
      properties: {
        moduleTitle: {
          type: 'void',
          'x-component': 'DescriptionText',
          'x-component-props': {
            textContent: '基础信息',
            textMode: 'h3',
            style: { borderTop: `1px solid var(--metro-divider-0)`, paddingTop: 16, marginBottom: 12 },
          },
        },
        gridLayout: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-component-props': {
            maxColumns: 4,
            strictAutoFit: true,
          },
          properties: varProperties,
        },
      },
    };
    this.formSpec = {
      formilySchema: {
        schema: girdSchema,
        form: {
          ...formilySchema.form,
          layout: 'horizontal',
          labelWidth: '100%',
        },
      },
      allSettingValues,
    };
  }

  private generateFlowSpec() {
    this.flowSpec = getFormTypeSpec(this.formType!);
  }
}

export { FormInfoController };
