import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { RequestController } from '@mdtBsControllers/request-controller';
import { TaskStatusEnum } from '../../containers/table-one-table-mission-center';
import { <PERSON><PERSON><PERSON>AppController } from '../../datlas/app/DatlasAppController';
import i18n from '../../languages';
import { FlowTypeEnum } from '../../utils/oneTableUtil';
import {
  IDrawerReportDetailControllerOptions,
  openReportDetailPage,
} from '../one-table-report-detail/drawer-one-table-report-detail';
import type { IListItem, IOneTableDashbordModel, IStatistics } from './OneTableDashbordModelBff';

interface IControllerOptions {
  Model: IOneTableDashbordModel;
}

export interface IStatisticStatus extends IStatistics {
  loading: boolean;
}

interface IListStatus {
  loading: boolean;
  total?: number;
  list?: IListItem[];
}

export class OneTableDashbordController extends RequestController {
  private statistics$ = new BehaviorSubject<IStatisticStatus>({ loading: true });
  private reportList$ = new BehaviorSubject<IListStatus>({ loading: true });
  private taskList$ = new BehaviorSubject<IListStatus>({ loading: true });

  private Model: IOneTableDashbordModel;

  public constructor(options: IControllerOptions) {
    super();
    this.Model = options.Model;
    this.initDate();
  }

  public destroy() {
    super.destroy();
    this.statistics$.complete();
    this.Model = null!;
  }

  public changeRouter(router: string) {
    const app = DatlasAppController.getInstance();
    const rt = app.getRouterController();
    rt.gotoPath(router);
  }

  public getReportList$() {
    return this.reportList$;
  }

  public getTaskList$() {
    return this.taskList$;
  }

  public getStatistics$() {
    return this.statistics$;
  }

  public getAllKeys = (): (keyof IStatisticStatus)[] => {
    return ['finishedReport', 'finishedOrder', 'finishedForm', 'finishedReview'];
  };

  public getEnableFormManagement = () =>
    !!DatlasAppController.getInstance().getUserPermissionController().getMenuPermission().enableFormManagement;

  public getHasPermissionKeys = () => {
    const enableFormManagement = this.getEnableFormManagement();
    const keys = this.getAllKeys();
    if (!enableFormManagement) {
      _.remove(keys, (key) => key === 'finishedReport');
    }
    return keys;
  };

  public viewDetail = (item: IListItem) => {
    const { flowType } = item;
    let opt: Omit<IDrawerReportDetailControllerOptions, 'specId' | 'workflowId'>;
    if (flowType === FlowTypeEnum.REPORT_MANAGEMENT) {
      opt = {
        showDataModifyBtn: true,
        showDataFinishBtn: true,
        flowInfoOptions: {
          flowType: FlowTypeEnum.REPORT_MANAGEMENT,
        },
      };
    } else if (flowType === FlowTypeEnum.ACCEPT_ORDER_TASK) {
      opt = {
        flowInfoOptions: {
          flowType: FlowTypeEnum.ACCEPT_ORDER_TASK,
          taskId: item.taskId!,
          hasDataPermission: item.status !== TaskStatusEnum.TO_APPROVAL,
        },
      };
    } else if (flowType === FlowTypeEnum.FILL_TASK) {
      opt = {
        showDataModifyBtn: true,
        showDataFinishBtn: true,
        dataFinishBtnText: i18n.chain.proMicroModules.oneTable.btnSubmit,
        flowInfoOptions: {
          flowType: FlowTypeEnum.FILL_TASK,
          parentWorkflowId: item.parentWorkflowId!,
          taskId: item.taskId!,
        },
      };
    } else if (flowType === FlowTypeEnum.APPROVAL_TASK) {
      opt = {
        flowInfoOptions: {
          flowType: FlowTypeEnum.APPROVAL_TASK,
          taskId: item.taskId!,
        },
      };
    }

    openReportDetailPage(
      {
        specId: item.fillSpecId,
        workflowId: item.workflowId,
        enableFlow: true,
        enableData: true,
        ...opt!,
      },
      {
        title: item.name,
        onClose: () => {
          if (flowType === FlowTypeEnum.REPORT_MANAGEMENT) {
            this.Model.getReportList().then((val) => {
              this.reportList$.next({
                ...val,
                loading: false,
              });
            });
          } else {
            this.Model.getTaskList().then((val) => {
              this.taskList$.next({
                ...val,
                loading: false,
              });
            });
          }
        },
      },
    );
  };

  private initDate() {
    this.Model.getStatistics().then((val) => {
      this.statistics$.next({
        ...val,
        loading: false,
      });
    });

    this.Model.getReportList().then((val) => {
      this.reportList$.next({
        ...val,
        loading: false,
      });
    });

    this.Model.getTaskList().then((val) => {
      this.taskList$.next({
        ...val,
        loading: false,
      });
    });
  }
}
