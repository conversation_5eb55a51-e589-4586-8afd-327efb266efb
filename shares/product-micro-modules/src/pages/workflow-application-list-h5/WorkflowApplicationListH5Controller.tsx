import { BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { IWorkflowsQuery, IWorkflowsQueryPost } from '@mdtApis/interfaces';
import { RequestController } from '@mdtBsControllers/request-controller';
import { CardCurdWithSimpleSearchController } from '../../containers/card-curd-with-simple-search';
import { DatlasAppController } from '../../datlas/app/DatlasAppController';
import { WORKFLOW_INFO } from '../../datlas/datlasConfig';
import {
  ApplyStatusEnum,
  getWfQueryData,
  IApplicationListControllerOptions,
  IApplicationTableData,
  IWorkflowApplicationListModel,
} from '../workflow-application-list';
import { DrawerWorkflowDetailController } from '../workflow-detail';
import { ISearchBarData } from './CompSearchBarH5';
import { OptionCard } from './WorkflowApplicationListH5';

export type IApplicationCardData = IApplicationTableData;

export interface ICount {
  ready?: number;
  completed?: number;
}
export class WorkflowApplicationListH5Controller extends RequestController {
  private Model: IWorkflowApplicationListModel;
  private showBottomMenu?: boolean;
  private count$ = new BehaviorSubject<ICount>({});
  private status$ = new BehaviorSubject<ApplyStatusEnum>(ApplyStatusEnum.TO_PROCESS);
  private searchData$ = new BehaviorSubject<ISearchBarData>({});
  private listController: CardCurdWithSimpleSearchController<IApplicationCardData>;
  private detailController = new DrawerWorkflowDetailController();

  public constructor(options: IApplicationListControllerOptions) {
    super();
    this.Model = options.Model;
    this.showBottomMenu = options.showBottomMenu;
    this.listController = new CardCurdWithSimpleSearchController<IApplicationCardData>({
      dataListCompCardCurdControllerOptions: {
        dataListCompControllerOptions: {
          dataListControllerOptions: {
            loadDataListFunc: () => {
              const query = this.getQueryParams();
              return this.Model.queryFirstPageData(query.params, query.cancelToken).pipe(
                tap(() => {
                  this.refreshCount();
                }),
              );
            },
            loadNextPageDataListFunc: (params) => {
              const query = this.getQueryParams();
              return this.Model!.queryNextPageData(query.params, params, query.cancelToken);
            },
            equalItemKey: 'workflowId',
          },
          compOptions: () => this.initListOptions(),
        },
        cardItemViewController: () => this,
      },
    });

    this.listController.listenBackendFilter(this.status$, this.searchData$);
    this.listController.loadDataList();
    this.queryCompleteCount();
  }

  public destroy() {
    super.destroy();
    this.status$.complete();
    this.count$.complete();
    this.searchData$.complete();
    this.searchData$.next({});
    this.listController.destroy();
    this.detailController.destroy();
    this.Model = null!;
  }

  public getSearchData$() {
    return this.searchData$;
  }

  public changeSearchData = (val: ISearchBarData) => {
    this.searchData$.next(val);
  };

  public getShowBottomMenu = () => {
    return !!this.showBottomMenu;
  };

  public getDetailController() {
    return this.detailController;
  }

  public getUserName() {
    return DatlasAppController.getInstance().getUserName();
  }

  public getListController() {
    return this.listController;
  }

  public getCount$() {
    return this.count$;
  }

  public getStatus$() {
    return this.status$;
  }

  public changeStatus = (val: string) => {
    this.status$.next(val as ApplyStatusEnum);
  };

  public handleClickOption(item: IApplicationCardData) {
    this.detailController.open({
      wfId: item.workflowId,
      wfSpecId: item.wfSpecId,
      onSuccessCb: () => {
        this.listController.loadDataList();
      },
    });
  }

  public querySpecList = () => {
    return this.Model.querySpecList();
  };

  private getQueryParams = () => {
    const cancelToken = this.getCancelToken();
    const searchData = this.searchData$.getValue();
    const specId = WORKFLOW_INFO.limitSpec || searchData.specId;

    const params: IWorkflowsQueryPost = {
      status: this.status$.getValue() as 'ready',
      create_time_min: searchData.createTimeMin,
      create_time_max: searchData.createTimeMax,
      initiator_is_me: true,
      with_form_data: true,
      with_spec: 'simple' as 'simple',
      fuzzy_name: searchData.searchStr || undefined,
      workflow_spec_id: specId || undefined,
      workflow_data_query: getWfQueryData(searchData.searchKeyDataList),
      process_type: WORKFLOW_INFO.needOneTableWorkflowAndTask ? undefined : ['normal', 'approval', 'datapkg_form'],
    };

    return { cancelToken, params };
  };

  private initListOptions() {
    return {
      itemGap: 0,
      itemHeight: 75,
      itemWidth: '100%',
      useVirtual: false,
      itemKey: 'value',
      CardItemView: OptionCard,
    };
  }

  private queryCompleteCount() {
    this.getListController()
      .getDataListLoading$()
      .subscribe((loading) => {
        const count = this.count$.getValue();
        if (!loading && !count?.completed) {
          const params: IWorkflowsQuery = {
            status: 'completed',
            initiator_is_me: true,
            with_form_data: true,
            with_spec: 'simple' as 'simple',
            workflow_spec_id: WORKFLOW_INFO.limitSpec || undefined,
          };
          this.Model.queryFirstPageData(params, this.getCancelToken()).subscribe((val) => {
            const total = val[0];
            const nCount = {
              ...this.count$.getValue(),
              completed: total,
            };
            this.count$.next(nCount);
          });
        }
      });
  }

  private refreshCount() {
    requestAnimationFrame(() => {
      const total = this.getListController().getPageTotal$().getValue();
      const status = this.status$.getValue();
      const nCount = {
        ...this.count$.getValue(),
        [status]: total,
      };
      this.count$.next(nCount);
    });
  }
}
