import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import Input from '@mdtDesign/input';
import Select from '@mdtDesign/select';
import { resourceTypeOptions } from '@mdtProComm/utils/resourceUtil';
import { TableCurdWithSimpleSearch } from '../../containers/table-curd-with-simple-search';
import i18n from '../../languages';
import { ResourceShareMeController } from './ResourceShareMeController';
import './index.less';

interface IProps {
  controller: ResourceShareMeController;
}

const Operations: FC<IProps> = ({ controller }) => {
  const searchVal = useObservableState(controller.getSearchVal$());
  const filterVal = useObservableState(controller.getFilterValVal$());
  return (
    <div className="search-wrap">
      <Input
        prefixIcon="search"
        value={searchVal}
        onChange={(e) => controller.changeSearchVal$(e.target.value)}
        placeholder={i18n.chain.proMicroModules.resource.searchResource}
        allowClear={false}
      />
      <Select
        value={filterVal}
        onChange={(val) => controller.changeFilterValVal$(val)}
        options={resourceTypeOptions}
        placeholder={i18n.chain.comPlaceholder.select}
      />
    </div>
  );
};

export const ResourceShareMe: FC<IProps> = ({ controller }) => {
  return (
    <div className="module_table-resource-share-me">
      <Operations controller={controller} />
      <TableCurdWithSimpleSearch controller={controller} />
    </div>
  );
};
