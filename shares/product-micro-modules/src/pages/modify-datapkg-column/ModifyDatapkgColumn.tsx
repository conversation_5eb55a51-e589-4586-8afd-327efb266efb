import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { randomUuid } from '@mdtBsComm/utils/stringUtil';
import { DataListCompTableWithCurd } from '@mdtBsComponents/data-list-comp-table-curd';
import { ModalWithBtnsComp } from '@mdtBsComponents/modal-with-btns-comp';
import { LinkButton } from '@mdtDesign/button';
import Spin from '@mdtDesign/spin';
import TextAlert from '@mdtDesign/text-alert';
import i18n from '../../languages';
import { ModifyDatapkgColumnController } from './ModifyDatapkgColumnController';
import './index.less';

interface IProps {
  controller: ModifyDatapkgColumnController;
}

export const SaveBtn: FC<IProps> = ({ controller }) => {
  const saving = useObservableState(controller.getSaving$());

  return saving ? (
    <LinkButton disabled={saving}>
      <Spin size="small" />
      {i18n.chain.proMicroModules.datapkg.saving}
    </LinkButton>
  ) : (
    <>
      <LinkButton leftIcon="reset" onClick={controller.handleCancel} status="plain" className="cancel-edit">
        {i18n.chain.comButton.cancel}
      </LinkButton>
      <LinkButton leftIcon="save" onClick={controller.handleSave}>
        {i18n.chain.comButton.save}
      </LinkButton>
    </>
  );
};

export const ModifyDatapkgColumnTool: FC<IProps> = ({ controller }) => {
  const isEditMode = useObservableState(controller.getIsEditMode$());
  const showCreateBtn = controller.getShowCreateBtn();

  if (!controller.getEnableEdit()) return null;

  const addBtn = showCreateBtn ? (
    <LinkButton className="add-field" leftIcon="add" status="plain" onClick={controller.handleAddColumn}>
      {i18n.chain.proMicroModules.datapkg.addField}
    </LinkButton>
  ) : null;

  const editBtn = isEditMode ? (
    <SaveBtn controller={controller} />
  ) : (
    <>
      {addBtn}
      <LinkButton leftIcon="edit" onClick={controller.handleEdit}>
        {i18n.chain.proMicroModules.datapkg.edit}
      </LinkButton>
    </>
  );

  const editTip = isEditMode ? (
    <TextAlert size="compact" message={i18n.chain.proMicroModules.datapkg.editFieldDesc} status="warning" />
  ) : null;

  return (
    <div className="module_table-pkg-column-with-edit-tool">
      <span>{editTip}</span>
      <span>{editBtn}</span>
      <ModalWithBtnsComp controller={controller.getModifyController() as any} />
    </div>
  );
};

export const ModifyDatapkgColumn: FC<IProps> = ({ controller }) => {
  useObservableState(controller.getIsEditMode$());
  return (
    <DataListCompTableWithCurd
      key={randomUuid()}
      controller={controller}
      className="module_table-pkg-column-with-edit"
    />
  );
};
