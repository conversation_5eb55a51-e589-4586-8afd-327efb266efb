.search-bar-wrap {
  margin-bottom: 5px;

  .filter-search-wrap {
    display: flex;
    justify-content: space-between;
  }

  .filter-wrap {
    width: 100%;

    .metro-form-view {
      width: 100%;
      max-width: none;
      padding-bottom: 0;
    }

    .metro-form-view .metro-formily-item-label {
      margin-bottom: 0;
    }

    .metro-formily-item-feedback-layout-loose {
      margin-bottom: 0;
    }

    form {
      display: grid;
      grid-gap: 10px;
      grid-template-columns: 310px 130px 120px;
      align-items: center;
      justify-content: flex-start;
    }
  }

  .search-input-wrap {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: auto min-content;
    align-items: start;

    .search-input {
      width: 240px;
    }
  }

  .high-search-wrap {
    margin-top: 10px;
    padding: 1px 10px 10px;
    background: var(--metro-bg-1);
    border-radius: 6px;

    .metro-form-view {
      width: 100%;
      max-width: none;
      padding: 10px 0;
    }

    .metro-formily-item-feedback-layout-loose {
      margin-bottom: 0;
    }

    form {
      display: grid;
      grid-gap: 10px 30px;
      grid-template-columns: repeat(2, 48%);
      align-items: center;
      justify-content: start;
    }

    .metro-select {
      width: auto;
    }

    .spec-tip {
      margin: 10px 0 5px;
      color: var(--metro-text-1);
      font-size: 14px;
    }

    .high-spec-id {
      display: grid;
      grid-gap: 10px;
      grid-template-columns: 100px 300px auto;
      align-items: center;
      justify-content: start;

      .high-spec-label {
        margin: 0 5px 0 0;
        color: var(--dmc-text-700-color);
        font-size: 14px;
      }
    }
  }

  .high-tool {
    text-align: right;
  }
}
