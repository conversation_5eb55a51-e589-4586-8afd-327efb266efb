import { FC } from 'react';
import { TooltipText } from '@metroDesign/tooltip';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { ModalWithBtnsCompDialog } from '@mdtBsComponents/modal-with-btns-comp-dialog';
import { ModalWithBtnsCompEmotion } from '@mdtBsComponents/modal-with-btns-comp-emotion';
import Button from '@mdtDesign/button';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import { Dropmenu, MenuItemProps } from '@mdtDesign/dropdown';
import { RadioGroup } from '@mdtDesign/radio';
import Tooltip from '@mdtDesign/tooltip';
import { CardCurdWithSimpleSearch } from '../../containers/card-curd-with-simple-search';
import i18n from '../../languages';
import { useCardListResourceSharerContext, useCardListResourceSharerProvider } from './ResourceShareMenuContext';
import { IGroup, ModeEnum, ResourceShareMenuController } from './ResourceShareMenuController';
import './index.less';

const menus = [
  {
    label: i18n.chain.proMicroModules.resource.chooseUser,
    value: ModeEnum.USER,
  },
  {
    label: i18n.chain.proMicroModules.resource.chooseGroup,
    value: ModeEnum.GROUP,
  },
];

interface IProps {
  controller: ResourceShareMenuController;
  className?: string;
}

interface IUserCardProps {
  item: TreeLabelValueItemInterface;
  controller: ResourceShareMenuController;
}
interface IGroupCardProps {
  item: IGroup;
  controller: ResourceShareMenuController;
}

export const UserItem: FC<IUserCardProps> = ({ item, controller }) => {
  const selected = useObservableState(controller.getReceiver$());
  const cls = `card-item ${selected?.key === item.key ? 'card-item-selected' : ''}`;
  return (
    <div className={cls} onClick={() => controller.changeUser(item)}>
      <TooltipText text={item.title}>{item.title}</TooltipText>
    </div>
  );
};

export enum GroupOperationType {
  UPDATE = 'update',
  DELETE = 'delete',
}

const groupMenus: MenuItemProps[] = [
  {
    title: i18n.chain.proMicroModules.resource.groupManage,
    key: GroupOperationType.UPDATE,
    divider: true,
  },
  {
    title: <div style={{ color: 'var(--dmc-red-600-color)' }}>{i18n.chain.proMicroModules.resource.delGroup}</div>,
    key: GroupOperationType.DELETE,
  } as any,
];

export const GroupItem: FC<IGroupCardProps> = ({ item, controller }) => {
  const selected = useObservableState(controller.getReceiver$());
  const cls = `card-item group-card-item ${selected?.key === item.id ? 'card-item-selected' : ''}`;
  return (
    <div className={cls} onClick={() => controller.changeGroup(item)}>
      <Tooltip placement="bottom" title={item.description}>
        <span className="group-name">{item.name}</span>
      </Tooltip>
      <Dropmenu
        noSelected
        menus={groupMenus}
        icon="more"
        // trigger="hover"
        onClickMenu={(e) => controller.onClickGroupMoreBtn(e.key as GroupOperationType, item)}
      />
    </div>
  );
};

const Menus = () => {
  const { menuResourceShareController: controller } = useCardListResourceSharerContext();
  const activeMode = useObservableState(controller.getActiveMode$());

  return <RadioGroup radioType="nav" value={activeMode} options={menus} onChange={controller.handleMenuChange} />;
};

const CardList = () => {
  const { menuResourceShareController: controller } = useCardListResourceSharerContext();
  const activeMode = useObservableState(controller.getActiveMode$());

  return ModeEnum.USER === activeMode ? (
    <CardCurdWithSimpleSearch controller={controller.getUserController()} />
  ) : (
    <>
      <CardCurdWithSimpleSearch className="group-list" controller={controller.getGroupController()} />
      <Button className="create-group" type="assist" leftIcon="add-2" onClick={controller.handleCreateGroup}>
        {i18n.chain.proMicroModules.resource.createGroup}
      </Button>
      <ModalWithBtnsCompDialog controller={controller.getModifyGroupController()} />
      <ModalWithBtnsCompEmotion controller={controller.getDeleteController()} />
    </>
  );
};

export const ResourceShareMenu: FC<IProps> = ({ controller, className = '' }) => {
  const Provider = useCardListResourceSharerProvider();
  const value = { menuResourceShareController: controller };

  return (
    <Provider value={value}>
      <div className={`module_menu-resource-share ${className}`}>
        <Menus />
        <CardList />
      </div>
    </Provider>
  );
};
