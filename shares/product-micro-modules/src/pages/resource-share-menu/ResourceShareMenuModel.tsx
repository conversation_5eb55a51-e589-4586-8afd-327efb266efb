import { Observable } from 'rxjs';
import { IPaginationQuery, IUserQuery } from '@mdtApis/interfaces';
import { IBusinessResult } from '@mdtBsComm/interfaces';
import { TreeLabelValueItemInterface } from '@mdtDesign/check-list';
import { IFormData } from '../../containers/drawer-modify-form-role-group';
import { IUserLazySelectorModel } from '../../containers/user-lazy-selector';
import { IGroup } from './ResourceShareMenuController';

export interface IResourceShareMenuModel {
  getUserSelectorModel: () => IUserLazySelectorModel;
  queryUsers: (params: IUserQuery) => Observable<[number, TreeLabelValueItemInterface[]]>;
  queryNextUsers: (params: IPaginationQuery) => Observable<TreeLabelValueItemInterface[]>;
  queryGroups: () => Observable<IGroup[]>;
  queryGroupUsers: (groupId: number) => Observable<TreeLabelValueItemInterface[]>;
  modifyGroup: (formData: IFormData, oData?: IGroup) => Observable<IBusinessResult<IGroup>>;
  deleteGroup: (item: IGroup) => Observable<IBusinessResult<IGroup>>;
}
