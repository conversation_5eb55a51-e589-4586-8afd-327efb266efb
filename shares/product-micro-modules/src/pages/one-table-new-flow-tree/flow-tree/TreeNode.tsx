import { FC, useEffect, useState } from 'react';
import { Add, Remove } from '@metro/icons';
import { Badge } from '@metroDesign/badge';
import type { BadgeStatusColor } from '@metroDesign/badge/Badge';
import { Button } from '@metroDesign/button';
import { Flex } from '@metroDesign/flex';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { Tooltip } from '@metroDesign/tooltip';
import { Typography } from '@metroDesign/typography';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../components/transform-id-to-name';
import i18n from '../../../languages';
import { NODE_HEIGHT, NODE_WIDTH } from './constants';
import { type INode, FlowTreeController } from './FlowTreeController';
import '@antv/x6-react-components/es/style/components.css';

const nodeStyle = { width: NODE_WIDTH, height: NODE_HEIGHT };

export const TreeNode: FC<{ nodeItem: INode; controller: FlowTreeController }> = ({ nodeItem, controller }) => {
  const { data } = nodeItem;
  const [isOpen, setOpen] = useState(false);
  const dataOpen: boolean = controller.nodeIsOpen(data.wfId);

  useEffect(() => {
    setOpen(dataOpen);
  }, [dataOpen]);

  const meEle = data.isRoot ? (
    <Tag className="metag" size="small" bordered={false} color="warning">
      {i18n.chain.proMicroModules.oneTable.h5.myTask}
    </Tag>
  ) : null;

  const btnProps: any = isOpen ? { success: true, icon: <Remove /> } : { type: 'primary', icon: <Add /> };
  const collectBtn = data.showBtn ? (
    <Button
      {...btnProps}
      size="small"
      className="collect-btn"
      onlyIcon
      onClick={() => {
        controller.toggleNode(nodeItem.id);
      }}
    />
  ) : null;
  const userNameEle = <TransformIdToName id={data.userId} type="user_id" />;

  const tagELe = (
    <Tag className="tag" type="light" size="small" bordered={false} color={data.tagColor}>
      <Space size={6}>
        <Badge size="small" status={data.tagColor as BadgeStatusColor} />
        {data.tagLabel}
      </Space>
    </Tag>
  );
  const tipEle = data.reason ? (
    <Tooltip overlay={data.reason} placement="top">
      {tagELe}
    </Tooltip>
  ) : (
    tagELe
  );

  // Display dataDeliveryInfo information if available
  const deliveryInfoEle = data.dataDeliveryInfo ? (
    <Typography.Text type="secondary" className="delivery-info">
      {i18n.chain.proMicroModules.oneTable.tableColumns.deliveryDataCount(
        data.dataDeliveryInfo.stat_include_downstream.total_assign_rows,
        `${data.dataDeliveryInfo?.percent}%`,
      )}
    </Typography.Text>
  ) : null;

  return (
    <Flex vertical justify="space-between" className="one-table-new-tree-node" style={nodeStyle}>
      <Tooltip title={<Tag color="processing">{userNameEle}</Tag>} placement="topLeft">
        <Typography.Text ellipsis strong className="user-name">
          {userNameEle}
        </Typography.Text>
      </Tooltip>
      <Typography.Text ellipsis type="secondary" className="org-name">
        <TransformUserIdToOrgName id={data.userId} placement="right" />
      </Typography.Text>
      <Space direction="vertical" size={2}>
        <Typography.Text type="secondary" className="time">
          {data.time}
        </Typography.Text>
        {deliveryInfoEle}
      </Space>
      {tipEle}
      {meEle}
      {collectBtn}
    </Flex>
  );
};
