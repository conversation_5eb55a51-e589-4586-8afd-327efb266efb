import _ from 'lodash';
import { HelpOutlined } from '@metro/icons';
import { Badge } from '@metroDesign/badge';
import type { BadgeStatusColor } from '@metroDesign/badge/Badge';
import { Button } from '@metroDesign/button';
import { Popconfirm } from '@metroDesign/popconfirm';
import { Space } from '@metroDesign/space';
import { Tag } from '@metroDesign/tag';
import { Tooltip } from '@metroDesign/tooltip';
import { BehaviorSubject, of } from 'rxjs';
import type { IOnetableDownstreamStat, IWorkflow, IWorkflowGenealogy } from '@mdtApis/interfaces';
import { TransformIdToName, TransformUserIdToOrgName } from '../../../components/transform-id-to-name';
import {
  IHeaderOptions,
  IVirtualizedTableProps,
  TableCurdWithSimpleSearchController,
} from '../../../containers/table-curd-with-simple-search';
import i18n from '../../../languages';
import { buildDataDeliveryTooltip } from '../../../utils/oneTableNewUtil';
import { IFlowData, INodeData, OneTableNewFlowTreeModel } from '../OneTableNewFlowTreeModel';
import { Header } from './FlowTable';

export interface IControllerOptions {
  datasource: IFlowData;
  showDeliveryCancelOrRejectList?: boolean;
}

export class FlowTableController {
  private path$: BehaviorSubject<INodeData[]>;
  private tableController: TableCurdWithSimpleSearchController<INodeData>;
  private flowDataMap: Record<string, IWorkflow> = {};
  private flowGenealogyMap: Record<string, IWorkflowGenealogy> = {};
  private flowStatsMap?: Record<string, IOnetableDownstreamStat & { root: IOnetableDownstreamStat }> = {};
  private rootItem: INodeData;
  private showDeliveryCancelOrRejectList?: boolean;

  public constructor({ datasource, showDeliveryCancelOrRejectList }: IControllerOptions) {
    const { flowDataMap, flowGenealogyMap } = datasource;
    this.flowDataMap = flowDataMap;
    this.flowGenealogyMap = flowGenealogyMap;
    this.flowStatsMap = datasource.flowStatsMap;
    this.showDeliveryCancelOrRejectList = showDeliveryCancelOrRejectList;
    this.rootItem = OneTableNewFlowTreeModel.transformFlowData(
      datasource.root,
      flowDataMap,
      flowGenealogyMap,
      this.flowStatsMap,
    );
    this.path$ = new BehaviorSubject<INodeData[]>([]);
    this.tableController = new TableCurdWithSimpleSearchController({
      dataListCompTableCurdControllerOptions: {
        dataListControllerOptions: {
          loadDataListFunc: () => this.queryTableData(),
        },
        tableOptions: () => this.initTableProps(),
      },
      headerOptions: () => this.loadHeaderOptions(),
    });
    this.path$.subscribe(() => {
      this.tableController.loadDataList();
    });
  }

  public destroy() {
    this.path$.complete();
    this.tableController?.destroy();
    this.tableController = null!;
    this.flowDataMap = null!;
    this.flowGenealogyMap = null!;
    this.flowStatsMap = null!;
    this.rootItem = null!;
  }

  public getRootItem(): INodeData {
    return this.rootItem;
  }

  public getTableController() {
    return this.tableController;
  }

  public getPath$() {
    return this.path$;
  }

  public resetPath() {
    if (_.isEmpty(this.path$.getValue())) return;
    this.path$.next([]);
  }

  public clickPathToIndex(item: INodeData) {
    let path = this.path$.getValue();
    const index = _.findIndex(path, item);
    path = index === -1 ? [] : _.slice(path, 0, index + 1);
    this.path$.next(path);
  }

  private queryTableData = () => {
    const { flowGenealogyMap, flowDataMap, flowStatsMap } = this;
    const last = _.last(this.path$.getValue()) || this.rootItem;
    const targets = _.get(flowGenealogyMap, `[${last.wfId}].targets`, []);
    const items = _.map(targets, (it) => {
      return OneTableNewFlowTreeModel.transformFlowData(it, flowDataMap, flowGenealogyMap, flowStatsMap);
    });
    const filterItems = this.showDeliveryCancelOrRejectList
      ? items
      : _.filter(items, (item) => !item.isCancelledOrRefused);
    return of([0, _.orderBy(filterItems, ['time'], 'desc')] as [number, INodeData[]]);
  };

  private addPath(item: INodeData) {
    const path = this.path$.getValue();
    path.push(item);
    this.path$.next([...path]);
  }

  // 渲染表头所需信息
  private initTableProps = (): IVirtualizedTableProps => {
    return {
      columns: [
        {
          code: 'userId',
          width: 350,
          name: i18n.chain.proMicroModules.oneTable.tableColumns.departName,
          render: (val: string, item: INodeData) => {
            const ele = <TransformUserIdToOrgName placement="right" id={item.userId} showFullName />;
            return item.showBtn ? (
              <Button.Link primary onClick={() => this.addPath(item)}>
                {ele}
              </Button.Link>
            ) : (
              ele
            );
          },
        },
        {
          code: 'userIds',
          width: 200,
          name: i18n.chain.proMicroModules.oneTable.tableColumns.userName,
          render: (val: string, item: INodeData) => {
            return _.map(item.userIds, (user, index) => (
              <Tag key={user} {...(!index && { color: 'primary', type: 'light', bordered: false })}>
                <TransformIdToName id={user} type="user_id" />
              </Tag>
            ));
          },
        },
        {
          code: 'dataDeliveryInfo',
          width: 180,
          name: i18n.chain.proMicroModules.oneTable.tableColumns.dataDeliveryInfo,
          render: (dataDeliveryInfo: IOnetableDownstreamStat & { root: IOnetableDownstreamStat; percent?: string }) => {
            if (!dataDeliveryInfo) {
              return '-';
            }

            const tooltipContent = buildDataDeliveryTooltip(dataDeliveryInfo);

            const displayContent = (
              <Space direction="vertical" size={2}>
                <Space size={4}>
                  <span>
                    {i18n.chain.proMicroModules.oneTable.tableColumns.deliveryDataCount(
                      dataDeliveryInfo?.stat_include_downstream?.total_assign_rows,
                      `${dataDeliveryInfo?.percent}%`,
                    )}
                  </span>
                  <HelpOutlined style={{ color: 'var(--metro-text-2)' }} />
                </Space>
              </Space>
            );

            return (
              <Popconfirm
                title={<pre style={{ fontSize: '12px' }}>{tooltipContent}</pre>}
                placement="left"
                showArrow
                showCancel={false}
                trigger={['hover']}
                okButtonProps={{ style: { display: 'none' } }}
              >
                {displayContent}
              </Popconfirm>
            );
          },
        },
        {
          code: 'time',
          width: 180,
          name: i18n.chain.proMicroModules.oneTable.tableColumns.issuedTime,
          align: 'center',
        },
        {
          code: 'taskStatus',
          width: 150,
          align: 'center',
          name: i18n.chain.proMicroModules.oneTable.tableColumns.taskStatus,
          render: (val: string, item: INodeData) => {
            const tag = (
              <Tag className="tag" type="light" size="small" bordered={false} color={item.tagColor}>
                <Space size={6}>
                  <Badge size="small" status={item.tagColor as BadgeStatusColor} />
                  {item.tagLabel}
                </Space>
              </Tag>
            );
            const reason = item.reason;
            return reason ? (
              <Tooltip overlay={reason} placement="left">
                {tag}
              </Tooltip>
            ) : (
              tag
            );
          },
        },
      ],
    };
  };

  private loadHeaderOptions(): IHeaderOptions {
    return {
      title: <Header controller={this} />,
      hideInput: true,
    };
  }
}
