import { FC } from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { ExcelUpload } from '@mdtProComm/components/excel-template';
import { FormView } from '../../components/form-view';
import { FilterList } from '../../containers/filter-list';
import i18n from '../../languages';
import { FormilyTableEditorController } from './FormilyTableEditorController';

export interface IProps {
  controller: FormilyTableEditorController;
}

export const FormilyTableEditor: FC<IProps> = ({ controller }) => {
  const schema = controller.getFormilySchema();

  return (
    <>
      <FormView ref={controller.formRef} formilySchema={schema} allSettingValues={schema.allSettingValues} />
      <FilterList controller={controller.getFilerController()} />
      <ExcelUpload
        btnText={i18n.chain.proMicroModules.oneTable.uploadExcelBtn}
        parseMode="object"
        onChange={controller.uploadExcel}
      />
      <Button className="page-formily-table-editor_download-excel" onClick={controller.downloadExcel}>
        {i18n.chain.proMicroModules.oneTable.downloadTemplate}
      </Button>
    </>
  );
};
