import { FC } from 'react';
import { ModalWithBtnsCompDrawer } from '@mdtBsComponents/modal-with-btns-comp-drawer';
import { useDrawerSqlDataTabelPreviewProvider } from './drawerSqlDataTabelPreviewContext';
import { DrawerSqlDataTabelPreviewController } from './DrawerSqlDataTabelPreviewController';

interface IProps {
  controller: DrawerSqlDataTabelPreviewController;
}
export const DrawerSqlDataTabelPreview: FC<IProps> = ({ controller }) => {
  const Provider = useDrawerSqlDataTabelPreviewProvider();
  const value = { drawerSqlDataTabelPreviewController: controller };

  return (
    <Provider value={value}>
      <ModalWithBtnsCompDrawer controller={controller} />
    </Provider>
  );
};
