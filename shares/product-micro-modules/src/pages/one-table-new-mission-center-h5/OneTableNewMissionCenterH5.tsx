import { FC } from 'react';
import { Badge } from '@metro/mobile-components/dist/esm/badge';
import { Tabs } from '@metro/mobile-components/dist/esm/tabs';
import { Flex } from '@metroDesign/flex';
import { Radio } from '@metroDesign/radio';
import { Typography } from '@metroDesign/typography';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { OneTableNewFormFilterToolH5 } from '../../components/one-table-new-form-filter-tool';
import { CardCurdWithSimpleSearch } from '../../containers/card-curd-with-simple-search';
import i18n from '../../languages';
import { TaskPersonalStatusEnum } from '../../utils/oneTableNewUtil';
import { OneTableNewMissionCenterH5Controller } from './OneTableNewMissionCenterH5Controller';
import './index.less';

interface IProps {
  controller: OneTableNewMissionCenterH5Controller;
}

export const OneTableNewMissionCenterH5: FC<IProps> = ({ controller }) => {
  const cardStatus = useObservableState(controller.getCardDataStatus$());
  const filterToolController = controller.getFilterToolController();
  const cardCtrl = controller.getCardController();
  const totalCount = useObservableState(cardCtrl.getPageTotal$());
  const unhandledTotalCount = useObservableState(controller.getUnhandledTotalCount$());

  const firstActiveKey = cardStatus !== TaskPersonalStatusEnum.UNHANDLED ? 'handled' : TaskPersonalStatusEnum.UNHANDLED;
  const secondActiveKey =
    cardStatus !== TaskPersonalStatusEnum.COMPLETE ? TaskPersonalStatusEnum.RUNNING : TaskPersonalStatusEnum.COMPLETE;
  return (
    <div className="one-table-new-mission-center-h5">
      <Flex justify="center">
        <Typography.Title level={4}>{i18n.chain.proMicroModules.oneTable.menu.missionCenter}</Typography.Title>
      </Flex>
      <OneTableNewFormFilterToolH5 controller={filterToolController} />
      <Tabs
        onChange={(key) => controller.changeCardDataStatus(key as TaskPersonalStatusEnum)}
        activeKey={firstActiveKey}
      >
        <Tabs.Tab
          title={
            <Flex align="center" gap="small">
              {i18n.chain.proMicroModules.oneTable.todoTaskDoing}
              {unhandledTotalCount ? <Badge shape="round" text={unhandledTotalCount} /> : null}
            </Flex>
          }
          key={TaskPersonalStatusEnum.UNHANDLED}
        />
        <Tabs.Tab title={i18n.chain.proMicroModules.oneTable.todoTaskDone} key="handled">
          <Radio.Group
            options={[
              {
                label: i18n.chain.proMicroModules.oneTable.filling,
                value: TaskPersonalStatusEnum.RUNNING,
              },
              {
                label: i18n.chain.proMicroModules.oneTable.filled,
                value: TaskPersonalStatusEnum.COMPLETE,
              },
            ]}
            onChange={(e) => controller.changeCardDataStatus(e.target.value as TaskPersonalStatusEnum)}
            value={secondActiveKey}
            optionType="button"
          />
        </Tabs.Tab>
      </Tabs>
      {totalCount && cardStatus !== TaskPersonalStatusEnum.UNHANDLED ? (
        <Typography.Text strong className="total">{`${i18n.chain.comText.total}${totalCount}`}</Typography.Text>
      ) : null}
      <CardCurdWithSimpleSearch controller={controller.getCardController()} />
    </div>
  );
};
