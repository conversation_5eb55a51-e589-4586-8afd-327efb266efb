import _ from 'lodash';
import { SelectorTypeEnum } from '../../../containers/user-lazy-selector';
import i18n from '../../../languages';
import { getGlobalVarsOptions, GlobalVarsEnum } from '../../../utils/bpmn-xml-util';

export interface ICommonVar {
  refType: RefTypeEnum;
  ref?: any;
  varRef?: string;
  expressionRef?: string;
  templateRef?: string;
  // 模版保存到后端时需要进行转换，但对于子流程的模版需要提前转换，因为常规的转换是基于当前流程的变量。
  templateRefTransformed?: string;
  typecast?: string;
  target?: string;
  [key: string]: any;
}

export enum RefTypeEnum {
  LITERAL = 'literal',
  VAR = 'var',
  EXPRESSION = 'expression',
  TEMPLATE = 'template',
}

export const refTyeOptions = [
  {
    label: i18n.chain.proMicroModules.workflow.edit.literalValue,
    value: RefTypeEnum.LITERAL,
  },
  {
    label: i18n.chain.proMicroModules.workflow.edit.globalVar,
    value: RefTypeEnum.VAR,
  },
  {
    label: i18n.chain.proMicroModules.workflow.edit.pythonExpr,
    value: RefTypeEnum.EXPRESSION,
  },
  {
    label: i18n.chain.proMicroModules.workflow.edit.template,
    value: RefTypeEnum.TEMPLATE,
  },
];

export enum TypecastEnum {
  INT = 'int',
  FLOAT = 'float',
  STR = 'str',
  BOOL = 'bool',
  DATE = 'date',
  DATETIME = 'datetime',
  CSVINT = 'csvint',
  CSVSTR = 'csvstr',
  JSON = 'json',
  TIMESTAMP = 'timestamp',
  TIMESTAMP_MS = 'timestamp_ms',
}

export const typecastOptions = [
  {
    label: i18n.chain.comText.default,
    value: '',
  },
  {
    label: i18n.chain.comDataType.int,
    value: TypecastEnum.INT,
  },
  {
    label: i18n.chain.comDataType.float,
    value: TypecastEnum.FLOAT,
  },
  {
    label: i18n.chain.comDataType.str,
    value: TypecastEnum.STR,
  },
  {
    label: i18n.chain.comDataType.bool,
    value: TypecastEnum.BOOL,
  },
  {
    label: i18n.chain.comDataType.date,
    value: TypecastEnum.DATE,
  },
  {
    label: i18n.chain.comDataType.datetime,
    value: TypecastEnum.DATETIME,
  },
  {
    label: i18n.chain.comDataType.intArray,
    value: TypecastEnum.CSVINT,
  },
  {
    label: i18n.chain.comDataType.strArray,
    value: TypecastEnum.CSVSTR,
  },
  {
    label: i18n.chain.comDataType.json,
    value: TypecastEnum.JSON,
  },
  {
    label: i18n.chain.comDataType.timeStamp,
    value: TypecastEnum.TIMESTAMP,
  },
  {
    label: i18n.chain.comDataType.timeStampMS,
    value: TypecastEnum.TIMESTAMP_MS,
  },
];

export enum UserRoleTypecastExtendEnum {
  ORG_LEADER = 'org_leader',
  ORG_ONETABLE_ADMIN = 'org_onetable_admin',
}

export const userRoleTypecastOptions = [
  {
    label: i18n.chain.comText.user,
    value: SelectorTypeEnum.USER,
  },
  {
    label: i18n.chain.comText.role,
    value: SelectorTypeEnum.ROLE,
  },
  {
    label: i18n.chain.comText.group,
    value: SelectorTypeEnum.GROUP,
  },
  {
    label: i18n.chain.comText.org,
    value: SelectorTypeEnum.ORGANIZATION,
  },
  {
    label: i18n.chain.comText.orgLeader,
    value: UserRoleTypecastExtendEnum.ORG_LEADER,
  },
  {
    label: i18n.chain.comText.orgOnetableAdmin,
    value: UserRoleTypecastExtendEnum.ORG_ONETABLE_ADMIN,
  },
];

export interface ICommonVarsSchemaOptions {
  title?: string;
  // 卡片标题
  cardTitle?: string;
  otherSchema?: Record<string, any>;
  otherSchemaOnTop?: boolean;
  removeTypecast?: boolean;
  customerRefSchema?: Record<string, any>;
  // 排除的全局变量
  excludeVars?: GlobalVarsEnum[];
  // wf spec xml
  xml?: string;
  maxItems?: number;
  isRefRequired?: boolean;
}

export const commonVarsStyle = {
  colon: false,
  labelAlign: 'right',
  labelWidth: 150,
};

export const getVisibleSchema = (values: RefTypeEnum[]) => {
  const conditions = _.map(values, (it) => `$deps[0] === "${it}"`);
  return {
    'x-reactions': {
      dependencies: ['.refType'],
      fulfill: {
        state: {
          visible: `{{${_.join(conditions, ' || ')}}}`,
        },
      },
    },
  };
};

export const getCommonVarsSchema = (options?: ICommonVarsSchemaOptions): any => {
  const {
    title,
    cardTitle,
    otherSchema,
    otherSchemaOnTop = false,
    xml,
    excludeVars,
    maxItems,
    isRefRequired = true,
    customerRefSchema,
    removeTypecast,
  } = options || ({} as any);
  const onlyOne = maxItems === 1;
  const { groupedOptions, tagInputOptions } = xml
    ? getGlobalVarsOptions(xml)
    : { groupedOptions: [], tagInputOptions: [] };
  const varOptions = excludeVars
    ? _.filter(groupedOptions, (it) => !_.includes(excludeVars, it.value))
    : groupedOptions;
  const optSchema = onlyOne
    ? {}
    : {
        index: {
          type: 'void',
          'x-component': 'ArrayCards.Index',
        },
        remove: {
          type: 'void',
          'x-component': 'ArrayCards.Remove',
        },
        moveUp: {
          type: 'void',
          'x-component': 'ArrayCards.MoveUp',
        },
        moveDown: {
          type: 'void',
          'x-component': 'ArrayCards.MoveDown',
        },
      };

  const refSchema = customerRefSchema
    ? {
        'x-decorator': 'FormItem',
        'x-decorator-props': commonVarsStyle,
        title: i18n.chain.proMicroModules.workflow.edit.value,
        required: isRefRequired,
        ...getVisibleSchema([RefTypeEnum.LITERAL]),
        ...customerRefSchema,
      }
    : {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': commonVarsStyle,
        title: i18n.chain.proMicroModules.workflow.edit.value,
        required: isRefRequired,
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
        ...getVisibleSchema([RefTypeEnum.LITERAL]),
      };

  const typecastSchema = removeTypecast
    ? {}
    : {
        typecast: {
          type: 'string',
          title: i18n.chain.proMicroModules.workflow.edit.dataTypeTransform,
          default: '',
          'x-decorator': 'FormItem',
          'x-decorator-props': commonVarsStyle,
          'x-component': 'Select',
          'x-component-props': {
            options: typecastOptions,
            showSearch: true,
            optionFilterProp: 'label',
          },
        },
      };

  const baseProperties = {
    refType: {
      type: 'string',
      title: i18n.chain.proMicroModules.workflow.edit.varCategory,
      default: RefTypeEnum.LITERAL,
      'x-decorator': 'FormItem',
      'x-decorator-props': commonVarsStyle,
      'x-component': 'Select',
      'x-component-props': {
        options: refTyeOptions,
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    ref: refSchema,
    varRef: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': commonVarsStyle,
      title: i18n.chain.proMicroModules.workflow.edit.value,
      required: isRefRequired,
      'x-component': 'Select',
      'x-component-props': {
        options: varOptions,
        placeholder: i18n.chain.comPlaceholder.select,
        showSearch: true,
        optionFilterProp: 'label',
      },
      ...getVisibleSchema([RefTypeEnum.VAR]),
    },
    expressionRef: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': commonVarsStyle,
      title: i18n.chain.proMicroModules.workflow.edit.value,
      required: isRefRequired,
      'x-component': 'Input',
      'x-component-props': {
        placeholder: i18n.chain.comPlaceholder.input,
      },
      ...getVisibleSchema([RefTypeEnum.EXPRESSION]),
    },
    templateRef: {
      type: 'string',
      'x-decorator': 'FormItem',
      'x-decorator-props': commonVarsStyle,
      title: i18n.chain.proMicroModules.workflow.edit.value,
      required: isRefRequired,
      'x-component': 'TagInputFormily',
      'x-component-props': {
        options: tagInputOptions,
      },
      ...getVisibleSchema([RefTypeEnum.TEMPLATE]),
    },
    ...typecastSchema,
  };

  const finalProperties = otherSchemaOnTop
    ? { ...otherSchema, ...baseProperties, ...optSchema }
    : { ...baseProperties, ...otherSchema, ...optSchema };

  return {
    title,
    type: 'array',
    default: onlyOne ? [{}] : undefined,
    'x-decorator': 'FormItem',
    'x-component': 'ArrayCards',
    'x-component-props': {
      title: cardTitle,
      className: onlyOne ? 'common-vars-only-one' : '',
    },
    maxItems,
    items: {
      type: 'object',
      properties: finalProperties,
    },
    properties: onlyOne
      ? undefined
      : {
          addition: {
            type: 'void',
            title: i18n.chain.comText.add,
            'x-component': 'ArrayCards.Addition',
          },
        },
  };
};
