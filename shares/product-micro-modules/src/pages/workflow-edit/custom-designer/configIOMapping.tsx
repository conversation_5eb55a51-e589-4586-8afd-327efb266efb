import _ from 'lodash';
import { FC, useEffect, useRef, useState } from 'react';
import { Form } from '@formily/core';
import { Badge } from '@metroDesign/badge';
import { Button } from '@metroDesign/button';
import { Divider } from '@metroDesign/divider';
import { drawerApi } from '@metroDesign/drawer';
import { Flex } from '@metroDesign/flex';
import { Input } from '@metroDesign/input';
import { Spin } from '@metroDesign/spin';
import { toastApi } from '@metroDesign/toast';
import { Tooltip } from '@metroDesign/tooltip';
import { Typography } from '@metroDesign/typography';
import { useAsyncEffect } from 'ahooks';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import { transformHtml2Backend } from '@mdtProComm/utils/wfTmplUtil';
import { FormView, IFormSpecRefHandle } from '../../../components/form-view';
import i18n from '../../../languages';
import { getConfigOfFuncParams } from '../service-task';
import WorkflowEditController from '../WorkflowEditController';
import { getCommonVarsSchema } from './commonVarsUtils';

const KEY_SERVICEINFO = 'MdtExternal__ServiceInfo';

const { TextArea } = Input;
const { Title, Paragraph } = Typography;

const commonFormSchema = {
  labelWidth: 220,
  labelWrap: true,
  colon: false,
  labelAlign: 'right',
};

export function openIOMappingConfig(
  value: string,
  callback: (newVal: string) => void,
  controller: WorkflowEditController,
) {
  drawerApi.open({
    title: i18n.chain.proMicroModules.workflow.visualParamsConfig || i18n.chain.proMicroModules.workflow.edit.inputVars,
    width: 900,
    okButtonProps: { style: { display: 'none' } },
    cancelButtonProps: { style: { display: 'none' } },
    closable: true,
    destroyOnClose: true,
    children: (onClose) => (
      <DrawerInner
        controller={controller}
        value={value}
        callback={(values: any) => {
          callback(values);
          onClose();
        }}
      />
    ),
  });
}

const ActiveDot: FC<any> = (props) => {
  return (
    <Badge
      {...props}
      status="success"
      text={
        <Typography.Text strong type="success">
          {i18n.chain.proMicroModules.workflow.active}
        </Typography.Text>
      }
    />
  );
};
const DrawerInner = (props: { controller: WorkflowEditController; callback: any; value: string }) => {
  const { controller, value, callback } = props;
  const [data, setData] = useState({ xml: '' });
  const [loading, setLoading] = useState(true);

  useAsyncEffect(async () => {
    const { xml } = await controller.getEditXml();
    setData({ xml });
    setLoading(false);
  }, []);

  if (loading) return <Spin brand fillParent />;

  return (
    <ConfigView
      value={parseStrToObj(value)}
      xml={data.xml}
      controller={controller}
      callback={(values: any) => {
        callback(JSON.stringify(values));
        return { success: true };
      }}
    />
  );
};

interface IConfigViewProps {
  value: Record<string, any>;
  callback: any;
  xml: string;
  controller: WorkflowEditController;
}

const getFunctionSchema = (
  serviceName: string,
  functionName: string,
): { properties: Record<string, any>; required: string[] } | null => {
  try {
    const serviceInfoStr = sessionStorage.getItem(KEY_SERVICEINFO);
    if (!serviceInfoStr) return null;

    const serviceInfo: any[] = JSON.parse(serviceInfoStr);
    const service = serviceInfo.find((s) => s.value === serviceName);
    if (!service) return null;

    const func = service.children.find((f: any) => f.value === functionName);
    if (!func || !func.schema) return null;

    return {
      properties: func.schema.properties || {},
      required: func.schema.required || [],
    };
  } catch (error) {
    console.error('Failed to parse service info:', error);
    return null;
  }
};

const getSchema = async (options: {
  xml: string;
  controller: WorkflowEditController;
  serviceName: string;
  functionName: string;
  selectedParams?: string[];
}) => {
  const { xml, serviceName, functionName, selectedParams = [] } = options;
  const schema = getFunctionSchema(serviceName, functionName);

  const properties = {
    inputVars: {
      title: '',
      ...getCommonVarsSchema({
        cardTitle: i18n.chain.proMicroModules.workflow.edit.inputVars,
        xml,
        otherSchemaOnTop: true,
        otherSchema: {
          target: {
            type: 'string',
            title: i18n.chain.proMicroModules.workflow.paramsName,
            required: true,
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              colon: false,
              labelAlign: 'right',
              labelWidth: 150,
            },
            'x-component': 'Select',
            'x-component-props': {
              placeholder: i18n.chain.proMicroModules.workflow.paramsNameTip,
              showSearch: true,
              options: schema
                ? Object.entries(schema.properties).map(([key]) => ({
                    label: key + (schema.required?.includes(key) ? i18n.chain.proMicroModules.workflow.required : ''),
                    value: key,
                    disabled: selectedParams.includes(key),
                  }))
                : [],
            },
          },
        },
      }),
    },
  };

  return {
    form: commonFormSchema,
    schema: { properties },
    effects: (form: Form) => {
      form.subscribe(({ type, payload }: { type: string; payload: { path: string; [key: string]: any } }) => {
        if (type === 'onFieldValueChange' && payload.path.includes('inputVars')) {
          autoFillRequiredParam(form, serviceName, functionName);
        }
      });
    },
  };
};

// 辅助函数：为新添加的数组项自动填充必填参数
const autoFillRequiredParam = (form: Form, serviceName: string, functionName: string): void => {
  const schema = getFunctionSchema(serviceName, functionName);
  if (!schema?.required?.length) return;

  const formValues = form.values || {};
  const inputVars = formValues.inputVars || [];

  const lastIndex = inputVars.length - 1;
  const lastItem = inputVars[lastIndex];

  if (lastItem && !lastItem.target) {
    const currentParams = inputVars.slice(0, -1).map((item: { target: string }) => item.target);
    const remainingRequired = _.filter(schema.required, (param) => !_.includes(currentParams, param));

    if (!_.isEmpty(remainingRequired)) {
      form.setFieldState(`inputVars.${lastIndex}.target`, (state) => {
        state.value = remainingRequired[0];
      });

      form.setFieldState(`inputVars.${lastIndex}.refType`, (state) => {
        state.value = 'literal';
      });
    }
  }
};

const ConfigView = (props: IConfigViewProps) => {
  const formRef = useRef<IFormSpecRefHandle>({} as any);
  const { value, callback, xml, controller } = props;
  const [formInfo, setFormInfo] = useState({
    data: {},
    schema: {},
  });

  const parsedValue = typeof value === 'string' ? parseStrToObj(value) : value;
  const [functionParams, setFunctionParams] = useState((parsedValue as any)?.functionParams || '');
  const serviceName = (parsedValue as any)?.serviceName || '';
  const functionName = (parsedValue as any)?.functionName || '';
  const hasSpecialConfig = !!getConfigOfFuncParams(`${serviceName}__${functionName}`);

  const isIoMapping = !_.isEmpty((formInfo.data as any)?.inputVars);
  const isFunctionParams = !isIoMapping && !_.isEmpty(functionParams);

  const [showAutoFill, setShowAutoFill] = useState(false);

  useEffect(() => {
    const initialIoMapping = (parsedValue as any)?.ioMapping || '';
    const initialParsedIoMapping =
      typeof initialIoMapping === 'string' ? parseStrToObj(initialIoMapping) : initialIoMapping;
    const initialHasIoMappingData = !_.isEmpty(initialParsedIoMapping?.inputVars);
    const initialHasFunctionParams = !_.isEmpty((parsedValue as any)?.functionParams);
    const showAutoFillCurrent = initialHasIoMappingData || initialHasFunctionParams;
    setShowAutoFill(showAutoFillCurrent);

    if (!showAutoFillCurrent) {
      setTimeout(() => {
        addRequiredParams();
      }, 0);
    }
  }, []);

  const showTraditionalParams = !_.isEmpty(functionParams);

  const updateSelectOptions = (selectedParams: string[]) => {
    const schema = getFunctionSchema(serviceName, functionName);
    if (!schema) return;

    const options = Object.entries(schema.properties).map(([key]) => {
      const label = key + (_.includes(schema.required, key) ? i18n.chain.proMicroModules.workflow.required : '');
      const labelDescription = schema.properties[key].description || '';
      return {
        label: (
          <Tooltip title={labelDescription} placement="left">
            <div style={{ width: '100%' }}>{label}</div>
          </Tooltip>
        ),
        value: key,
        disabled: _.includes(selectedParams, key),
      };
    });

    const formInstance = formRef.current.getInstance();
    if (formInstance) {
      formInstance.setFieldState('inputVars.*.target', (state) => {
        state.componentProps = {
          ...state.componentProps,
          options,
        };
      });
    }
  };

  const handleFormChange = (values: any) => {
    setFormInfo((prevState) => ({
      ...prevState,
      data: values || {},
    }));

    const selectedParams = values?.inputVars?.map((item: any) => item.target).filter(Boolean) || [];
    updateSelectOptions(selectedParams);
  };

  const addRequiredParams = () => {
    const formInstance = formRef.current.getInstance();
    if (!formInstance) return;
    const schema = getFunctionSchema(serviceName, functionName);

    if (!schema?.required?.length) {
      toastApi.info(i18n.chain.proMicroModules.workflow.noRequiredParams);
      return;
    }

    const formValues = formInstance.values || {};
    const inputVars = formValues.inputVars || [];

    const currentParams = _.map(inputVars, (item: any) => item.target).filter(Boolean);

    const remainingRequired = _.filter(schema.required, (param) => !currentParams.includes(param));

    if (!_.isEmpty(remainingRequired)) {
      const newItems = _.map(remainingRequired, (param) => ({
        refType: 'literal',
        target: param,
        ref: '',
      }));

      formInstance.setValues({
        ...formValues,
        inputVars: [...inputVars, ...newItems],
      });
    }
  };

  useEffect(() => {
    const ioMapping = (parsedValue as any)?.ioMapping || '';
    const parsedIoMapping = typeof ioMapping === 'string' ? parseStrToObj(ioMapping) : ioMapping;
    const selectedParams = parsedIoMapping?.inputVars?.map((item: any) => item.target).filter(Boolean) || [];

    getSchema({ xml, controller, serviceName, functionName, selectedParams }).then((result: any) => {
      const updatedSchema = {
        ...result,
        schema: {
          ...result.schema,
          'x-component-props': {
            submitText: '',
          },
        },
      };

      setFormInfo({
        data: parsedIoMapping || {},
        schema: updatedSchema,
      });
    });
  }, [serviceName, functionName]);

  const saveFunctionParams = () => {
    callback({
      _updateType: 'functionParams',
      value: functionParams,
    });
    return { success: true };
  };

  const saveIoMapping = async () => {
    try {
      const formInstance = formRef.current.getInstance();
      await formInstance.validate();

      const wfSpecValues = await formRef.current.getValues();
      if (!wfSpecValues) {
        console.error('Failed to get form values');
        return { success: false };
      }

      const values = {};
      _.assign(values, wfSpecValues);

      callback({
        value: transformToBackendData(values),
      });
      return { success: true };
    } catch (error) {
      console.error('Form validation failed:', error);
      return { success: false };
    }
  };

  return (
    <div style={{ padding: '0 16px' }}>
      <Title level={4}>
        <Flex gap={8}>
          {i18n.chain.proMicroModules.workflow.visualParamsConfig}
          {isIoMapping ? <ActiveDot /> : null}
        </Flex>
      </Title>
      <Paragraph>{i18n.chain.proMicroModules.workflow.notSupportForm}</Paragraph>

      <div style={{ marginBottom: 16, paddingBottom: 16, borderBottom: '1px solid var(--metro-border-1)' }}>
        <Flex>
          <Button type="primary" onClick={saveIoMapping} style={{ marginRight: 16 }}>
            {i18n.chain.proMicroModules.workflow.saveWithVisualParams}
          </Button>

          {showAutoFill && (
            <Button onClick={addRequiredParams} style={{ marginRight: 16 }}>
              {i18n.chain.proMicroModules.workflow.addRequiredParams}
            </Button>
          )}
        </Flex>
      </div>

      <FormView ref={formRef} formilySchema={formInfo.schema} formData={formInfo.data} onChange={handleFormChange} />

      {showTraditionalParams && (
        <>
          <Divider>{i18n.chain.proMicroModules.workflow.traditionalParamsConfig}</Divider>

          <Paragraph>
            <Flex gap={8}>
              {hasSpecialConfig
                ? i18n.chain.proMicroModules.workflow.specialParamsConfigTip
                : i18n.chain.proMicroModules.workflow.manualParamsConfigTip}

              {isFunctionParams ? <ActiveDot style={{ marginLeft: 8 }} /> : null}
            </Flex>
          </Paragraph>

          <Button type="secondary" onClick={saveFunctionParams} style={{ marginBottom: 16 }}>
            {i18n.chain.proMicroModules.workflow.saveWithTraditionalParams}
          </Button>

          <TextArea
            value={functionParams}
            onChange={(e) => {
              setFunctionParams(e.target.value);
            }}
            rows={8}
            style={{ marginBottom: 16 }}
          />
        </>
      )}
    </div>
  );
};

const transformToBackendData = (values: any): string => {
  if (!values.inputVars?.length) {
    return '';
  }
  _.forEach(values.outputVars, (it) => {
    if (!it.templateRef) return;
    it.templateRefTransformed = transformHtml2Backend(it.templateRef, (label) => label);
  });
  return JSON.stringify(values);
};
