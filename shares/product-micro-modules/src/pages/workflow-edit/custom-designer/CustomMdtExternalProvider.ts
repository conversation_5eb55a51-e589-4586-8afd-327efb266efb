import _ from 'lodash';
import inherits from 'inherits';
import MdtExternalProvider, {
  IBpmnCustomerNode,
  IClickFunctionParamsOptions,
  IFormSpecValue,
} from '@mdtBpmnPropertiesPanel/MdtExternalProvider';
import { IAfterSubmitConfig } from '@mdtBpmnPropertiesPanel/properties/AfterSubmitProps';
import { FormSpecTypeEnum } from '@mdtBpmnPropertiesPanel/properties/FormSpecProps';
import { TimerTypeEnum } from '@mdtBpmnPropertiesPanel/properties/TimerProps';
import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import toastApi from '@mdtDesign/toast';
import { DatlasAppController } from '../../../datlas/app/DatlasAppController';
import i18n from '../../../languages';
import { getInvalidVarNames } from '../../../utils';
import { getGlobalVarsOptions, mergeWithSubmitterSchema } from '../../../utils/bpmn-xml-util';
import { getConfigOfFuncParams } from '../service-task';
import WorkflowEditController from '../WorkflowEditController';
import { openAfterSubmitConfig } from './configAfterSubmit';
import { openCallActivityConfig } from './configCallActivity';
import { openConditionExpressionConfig } from './configConditionExpression';
import { openDetailPageSetting } from './configDetailPage';
import { openDynamicAssigneesConfig } from './configDynamicAssignees';
import { openFormDefaultValueConfig } from './configFormDefaultValue';
import { openFormSpecSourceConfig } from './configFormSpecSource';
import { openGlobalVariablesConfig } from './configGlobalVariables';
import { openInlineScriptConfig } from './configInlineScript';
import { openIOMappingConfig } from './configIOMapping';
import { openMultiInstanceConfig } from './configMultiInstance';
import { openScriptGlobalVarsSettingConfig } from './configScriptGlobalVarsSetting';
import { openServiceConfig } from './configServiceConfig';
import { openStandardLoopConfig } from './configStandardLoop';
import { openTimerConfig } from './configTimer';
import { openUserAssignmentConfig } from './configUserAssignment';
import { openWorkflowTmplConfig } from './configWorkflowTmpl';

const KEY_SERVICEINFO = 'MdtExternal__ServiceInfo';

export default function CustomMdtExternalProvider() {
  // @ts-ignore
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const that = this;
  const str = sessionStorage.getItem(KEY_SERVICEINFO) || '';
  that.preServiceInfo = parseStrToObj(str);
  // 如果不存在，则从服务端获取
  if (_.isEmpty(that.preServiceInfo)) {
    WorkflowEditController.getCurrentController()!
      .getModel()
      .getFloworkServices()
      .then((resp: any) => {
        that.preServiceInfo = resp;
        sessionStorage.setItem(KEY_SERVICEINFO, JSON.stringify(that.preServiceInfo));
      });
  }
}

inherits(CustomMdtExternalProvider, MdtExternalProvider);

CustomMdtExternalProvider.prototype.getServiceNameOptions = function () {
  const options = [{ label: '<none>', value: '' }];
  _.forEach(this.preServiceInfo, (it) => {
    options.push({ label: it.value, value: it.value });
  });
  return options;
};

CustomMdtExternalProvider.prototype.getFunctionNameOptions = function (serviceName: string) {
  const children = _.get(_.find(this.preServiceInfo, { value: serviceName }), 'children');
  return _.map(children, (it) => ({ label: it.value, value: it.value }));
};

CustomMdtExternalProvider.prototype.handleClickFunctionParams = async function ({
  serviceName,
  functionName,
  functionLabel,
  value,
  callback,
}: IClickFunctionParamsOptions) {
  const controller = WorkflowEditController.getCurrentController()!;
  const serviceFunctionName = `${serviceName}__${functionName}`;
  const config = getConfigOfFuncParams(serviceFunctionName);
  if (!config) {
    toastApi.warning(i18n.chain.proMicroModules.workflow.notSupportForm);
    return;
  }

  const data = await config.transformToFrontend(value, controller);
  const schema = await config.schemaFunc(controller);
  const params = {
    data,
    schema,
    title: functionLabel,
    serviceFunctionName,
  };
  const sb = controller.openFuncParams(params);
  sb.subscribe((rest) => {
    rest.success && callback(JSON.stringify(rest.result));
  });
};

CustomMdtExternalProvider.prototype.handleClickAssignment = function (
  value: string,
  callback: (newVal: string) => void,
) {
  openUserAssignmentConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleClickStarter = function (value: string, callback: (newVal: string) => void) {
  const valObj = parseStrToObj(value);
  const rest = _.isEmpty(valObj) ? [] : valObj;
  const sb = WorkflowEditController.getCurrentController()!.getUserSelectorController().openModal({ data: rest });
  sb.subscribe((rest) => {
    rest.success && callback(JSON.stringify(rest.result));
  });
};

CustomMdtExternalProvider.prototype.handleClickPkgSelector = async function (
  node: IBpmnCustomerNode,
  value: any,
  callback: (newVal: string) => void,
) {
  const spec = await openFormSpecSourceConfig(value.specType, WorkflowEditController.getCurrentController()!);
  CustomMdtExternalProvider.prototype.handleClickFormily(
    node,
    {
      specType: value.specType,
      spec,
    },
    callback,
  );
};

CustomMdtExternalProvider.prototype.handleClickFormily = async function (
  node: IBpmnCustomerNode,
  value: any,
  callback: (newVal: string) => void,
) {
  const app = DatlasAppController.getInstance();
  const { xml } = await WorkflowEditController.getCurrentController()!.getEditXml();
  const { groupedOptions } = getGlobalVarsOptions(xml);
  const otherVarOptions = _.flatten(
    _.map(
      _.filter(groupedOptions, (it) => it.value !== node.id),
      'options',
    ),
  );
  const otherKeys = _.map(otherVarOptions, 'value');

  if (value.specType === FormSpecTypeEnum.FORMILY_V2) {
    const spec = JSON.parse(value.spec || '{}');
    WorkflowEditController.getCurrentController()!
      .getFormEditorController()
      .openModal({
        theme: app.getTheme(),
        editorSchema: {
          formilySchema: _.pick(spec, ['form', 'schema']),
          allSettingValues: _.get(spec, 'allSettingValues'),
        },
        validate: (schema) => {
          const properties: any = schema.formilySchema.schema?.properties || {};
          const keys = _.keys(properties);
          const invalidVarNames = getInvalidVarNames(keys);
          if (!_.isEmpty(invalidVarNames)) {
            return i18n.chain.proMicroModules.workflow.edit.tipFieldFormat(_.join(invalidVarNames, '、'));
          }
          const sameKeys = _.intersection(otherKeys, keys);
          if (!_.isEmpty(sameKeys)) {
            const titles = _.map(sameKeys, (key) => {
              return properties[key].title;
            });
            return i18n.chain.proMicroModules.workflow.edit.tipFieldExist(_.join(titles, '、'));
          }
        },
      })
      .subscribe((rest) => {
        if (!rest.success) return;
        const editorSchema = mergeWithSubmitterSchema(rest.result, node.id);
        const data = {
          ..._.get(editorSchema, 'formilySchema'),
          allSettingValues: _.get(editorSchema, 'allSettingValues'),
        };
        callback(JSON.stringify(data));
      });
  } else {
    const params = {
      token: app.getTokenFromStorage().tk,
      schema: value.spec,
    };
    WorkflowEditController.getCurrentController()!
      .getFormilySelectorController()
      .openModal(params)
      .subscribe((rest: any) => {
        rest.success && callback(rest.result);
      });
  }
};

CustomMdtExternalProvider.prototype.handleConfigFormDefaultValue = function (
  node: IBpmnCustomerNode,
  value: IFormSpecValue,
  callback: (newVal: string) => void,
) {
  openFormDefaultValueConfig(node, value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigDynamicAssignees = async function (
  value: any,
  callback: (newVal: string) => void,
) {
  openDynamicAssigneesConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigInlineScript = async function (
  value: string,
  callback: (newVal: string) => void,
) {
  openInlineScriptConfig(value, callback);
};

CustomMdtExternalProvider.prototype.handleConfigScriptGlobalVars = async function (
  value: any,
  callback: (newVal: string) => void,
) {
  openScriptGlobalVarsSettingConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigGlobalVariables = async function (
  value: any,
  callback: (newVal: string) => void,
) {
  openGlobalVariablesConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleClickExpression = async function (
  value: any,
  callback: (newVal: string) => void,
) {
  openConditionExpressionConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigTimerValue = async function (
  timerType: TimerTypeEnum,
  value: any,
  callback: (newVal: string) => void,
) {
  openTimerConfig(timerType, value, callback);
};

CustomMdtExternalProvider.prototype.handleConfigWorkflowTmpl = async function (
  title: string,
  value: any,
  callback: (newVal: string) => void,
) {
  openWorkflowTmplConfig(title, value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigWorkflowDetail = async function (
  value: any,
  callback: (newVal: string) => void,
) {
  openDetailPageSetting(value, callback);
};

CustomMdtExternalProvider.prototype.handleConfigUserTaskDetail = async function (
  value: any,
  callback: (newVal: string) => void,
) {
  openDetailPageSetting(value, callback);
};

CustomMdtExternalProvider.prototype.handleConfigAfterSubmit = async function (
  value: string,
  callback: (newVal: IAfterSubmitConfig) => void,
) {
  openAfterSubmitConfig(value, callback);
};

CustomMdtExternalProvider.prototype.handleConfigServiceConfig = async function (
  value: string,
  callback: (newVal: string) => void,
) {
  openServiceConfig(value, callback);
};

CustomMdtExternalProvider.prototype.handleConfigMultiInstance = async function (
  value: string,
  callback: (newVal: string) => void,
) {
  openMultiInstanceConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigCallActivity = async function (
  value: string,
  callback: (newVal: string) => void,
) {
  openCallActivityConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigIOMapping = async function (
  value: string,
  callback: (newVal: string) => void,
) {
  openIOMappingConfig(value, callback, WorkflowEditController.getCurrentController()!);
};

CustomMdtExternalProvider.prototype.handleConfigStandardLoop = async function (
  value: string,
  callback: (newVal: string) => void,
) {
  openStandardLoopConfig(value, callback);
};

CustomMdtExternalProvider.prototype.hasSpecialFunctionConfig = function (serviceFunctionName: string): boolean {
  const config = getConfigOfFuncParams(serviceFunctionName);
  return !!config;
};
