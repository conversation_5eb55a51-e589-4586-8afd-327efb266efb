import { parseStrToObj } from '@mdtBsComm/utils/stringUtil';
import { MarkdownEditorFormily as MarkdownEditor } from '../../../components/markdown-editor';
import i18n from '../../../languages';
import { setFieldVisible } from '../../workflow-property-config';
import { IFuncParamsConfig } from './util';

enum MsgTypeEnum {
  TEXT = 'text',
  LINK = 'link',
  MARKDOWN = 'markdown',
}

const getMsgTypeOptions = () => {
  return [
    {
      label: i18n.chain.proMicroModules.workflow.edit.text,
      value: MsgTypeEnum.TEXT,
    },
    {
      label: i18n.chain.proMicroModules.workflow.edit.link,
      value: MsgTypeEnum.LINK,
    },
    {
      label: 'Markdown',
      value: MsgTypeEnum.MARKDOWN,
    },
  ];
};

const schemaFunc = async () => {
  const textSchema = {
    type: 'object',
    properties: {
      content: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.textContent,
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
    },
  };

  const linkSchema = {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.linkTitle,
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
      text: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.linkContent,
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input.TextArea',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
      messageUrl: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.linkUrl,
        required: true,
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          tooltip: i18n.chain.proMicroModules.workflow.edit.linkTip,
        },
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
      picUrl: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.linkImg,
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          tooltip: i18n.chain.proMicroModules.workflow.edit.linkImgTip,
        },
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
    },
  };

  const markdownSchema = {
    type: 'object',
    properties: {
      title: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.markdownTitle,
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
      text: {
        type: 'string',
        title: i18n.chain.proMicroModules.workflow.edit.markdownText,
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'MarkdownEditor',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
    },
  };

  const schema = {
    type: 'object',
    properties: {
      webhook: {
        type: 'string',
        required: true,
        title: i18n.chain.proMicroModules.workflow.edit.webhook,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-component-props': {
          placeholder: i18n.chain.comPlaceholder.input,
        },
      },
      body: {
        type: 'object',
        properties: {
          msgtype: {
            type: 'string',
            title: i18n.chain.proMicroModules.workflow.edit.webhookMsgType,
            required: true,
            default: MsgTypeEnum.TEXT,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: i18n.chain.comPlaceholder.select,
              options: getMsgTypeOptions(),
              showSearch: true,
              optionFilterProp: 'label',
            },
          },
          text: textSchema,
          link: linkSchema,
          markdown: markdownSchema,
        },
      },
    },
  };

  return {
    form: { layout: 'vertical', colon: false },
    schema,
  };
};

const transformToFrontend = async (value: string) => {
  const data: any = parseStrToObj(value);
  return data;
};

const transformToBackend = (val: any) => {
  return {
    ...val,
  };
};

const prepareFunc = async () => {
  return {
    effects: () => {
      const msgtypeKey = 'body.msgtype';
      setFieldVisible('body.text', msgtypeKey, MsgTypeEnum.TEXT);
      setFieldVisible('body.link', msgtypeKey, MsgTypeEnum.LINK);
      setFieldVisible('body.markdown', msgtypeKey, MsgTypeEnum.MARKDOWN);
    },
    components: { MarkdownEditor },
  };
};

export const sendDingtalkConfig: IFuncParamsConfig = {
  schemaFunc,
  prepareFunc,
  transformToFrontend,
  transformToBackend,
};
