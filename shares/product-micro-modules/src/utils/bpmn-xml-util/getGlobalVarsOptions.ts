import _ from 'lodash';
import { ILabelValue } from '@mdtBsComm/interfaces';
import { DbColumnTypeEnum } from '@mdtProComm/constants';
import { getWfGlobalVariables } from '@mdtProComm/utils/wfTmplUtil';
import { ITagInputOption } from '../../components/tag-input';
import i18n from '../../languages';
import { IFormFieldOption } from './common';
import { getExecutableNodeOptions, getUserTaskNodeOptions } from './getNodeOptions';
import { parseDataFromXml } from './parseDataFromXml';

export enum GlobalVarsEnum {
  SYSTERM = 'systerm',
  CUSTOMER = 'customer',
  NODE_EXECUTE_TIME = 'nodeExecuteTime',
  NODE_EXECUTOR = 'nodeExecutor',
}
export type ILabelValueOptions = ILabelValue & {
  options: IFormFieldOption[];
};
export interface IGlobalVarsOptions {
  // 用于全局变量赋值，使用标签输入组件
  tagInputOptions: ITagInputOption[];
  allOptions: IFormFieldOption[];
  groupedOptions: ILabelValueOptions[];
  // 自定义全局变量
  customerOptions: IFormFieldOption[];
  // 所有表单不为空的用户任务节点
  userTaskNodeOptions: ILabelValueOptions[];
}

export const getGlobalVarsOptions = (xml: string): IGlobalVarsOptions => {
  const xmlData = parseDataFromXml(xml);

  const sysGlobalVars = getWfGlobalVariables();
  const sysGlobalVarsOptions = _.map(sysGlobalVars, (it) => ({
    label: it.name,
    value: it.id,
    type: it.type,
  }));

  const userGlobalVars = xmlData.configData.globalVariables?.custVars;
  const userGlobalVarsOptions = _.map(
    _.filter(userGlobalVars, (v) => !!v.name),
    (it) => ({
      label: it.name,
      value: it.id,
      type: it.type,
    }),
  );

  const executableOptions = getExecutableNodeOptions(xml);
  const nodeExecuteTimeOptions = _.map(executableOptions, (it) => ({
    label: `【${it.label}】${i18n.chain.proMicroModules.bpmn.executeTime}`,
    value: `__${it.value}_execute_time`,
    type: DbColumnTypeEnum.STR,
  }));
  const nodeExecutorIdOptions = _.map(executableOptions, (it) => ({
    label: `【${it.label}】${i18n.chain.proMicroModules.bpmn.executeUserId}`,
    value: `__${it.value}_executor`,
    type: DbColumnTypeEnum.STR,
  }));

  const userTaskNodeOptions: ILabelValueOptions[] = [];
  _.forEach(getUserTaskNodeOptions(xml, false, false, false, true), (it) => {
    !_.isEmpty(it.fieldOptions) &&
      userTaskNodeOptions.push({
        label: it.label,
        value: it.value,
        options: it.fieldOptions,
      });
  });
  const userTaskFieldOptions = _.flatMap(userTaskNodeOptions, 'options');

  const allOptions: IFormFieldOption[] = [
    ...sysGlobalVarsOptions,
    ...userGlobalVarsOptions,
    ...nodeExecuteTimeOptions,
    ...nodeExecutorIdOptions,
    ...userTaskFieldOptions,
  ];

  const groupedOptions: ILabelValueOptions[] = [
    {
      label: i18n.chain.proMicroModules.bpmn.globalVars,
      value: GlobalVarsEnum.SYSTERM,
      options: sysGlobalVarsOptions,
    },
  ];
  if (!_.isEmpty(userGlobalVarsOptions)) {
    groupedOptions.push({
      label: i18n.chain.proMicroModules.bpmn.globalCustomVars,
      value: GlobalVarsEnum.CUSTOMER,
      options: userGlobalVarsOptions,
    });
  }
  groupedOptions.push({
    label: i18n.chain.proMicroModules.bpmn.nodeExecuteUserId,
    value: GlobalVarsEnum.NODE_EXECUTOR,
    options: nodeExecutorIdOptions,
  });
  if (!_.isEmpty(nodeExecuteTimeOptions)) {
    groupedOptions.push({
      label: i18n.chain.proMicroModules.bpmn.nodeExecuteTime,
      value: GlobalVarsEnum.NODE_EXECUTE_TIME,
      options: nodeExecuteTimeOptions,
    });
  }
  groupedOptions.push(...userTaskNodeOptions);

  // const tagInputVars = [
  //   GlobalVarsEnum.SYSTERM,
  //   GlobalVarsEnum.CUSTOMER,
  //   GlobalVarsEnum.NODE_EXECUTE_TIME,
  //   GlobalVarsEnum.NODE_EXECUTOR,
  // ];
  const tagInputOptions = _.map(
    // _.filter(groupedOptions, (it) => _.includes(tagInputVars, it.value)),
    groupedOptions,
    (it) => ({
      label: it.label,
      children: _.map(it.options, 'label'),
    }),
  );

  return {
    tagInputOptions,
    allOptions,
    groupedOptions,
    userTaskNodeOptions,
    customerOptions: userGlobalVarsOptions,
  };
};

export const varIsArrayType = (type?: string) => {
  return _.includes(_.toLower(type), 'array');
};
