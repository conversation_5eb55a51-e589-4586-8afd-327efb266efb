import _ from 'lodash';
import { toastApi } from '@metroDesign/toast';
import type { IOnetableDownstreamStat, IOnetableInvolvedForm, IOperatorFilter, IWorkflow } from '@mdtApis/interfaces';
import {
  DATE_FORMATTER_2,
  formateDate2,
  formateDateWithoutMillisecond,
  getCurrentDateStr,
} from '@mdtBsComm/utils/dayUtil';
import { ColumnOperatorFilterEnum, DbColumnTypeEnum, OneTableNewDataStateEnum } from '@mdtProComm/constants';
import { DatlasAppController } from '../datlas/app/DatlasAppController';
import { ONE_TABLE_INFO } from '../datlas/datlasConfig';
import type { IOneTableNewOperatorDataComm } from '../interfaces';
import i18n from '../languages';
import { ignoreMetroOptionColumns } from './oneTableUtil';

export {
  checkUploadExcelColumns,
  getColumnsFromFormSchame,
  filterFormEmptyValues,
  formValuesCommValidate,
  modifyResidentInfoDefaultSetting,
  checkUserExistsInMap,
  getOnetableRootDownstreamUsers,
  getColumnsMetaFromFormSetting,
  megerFormSpecProperAndSetting,
} from './oneTableUtil';

export const COLUMN_CREATE_TIME = '_create_time_';
export const COLUMN_UPDATE_TIME = '_update_time_';
export const COLUMN_CREATE_USER_ID = '_create_user_id_';
export const COLUMN_UPDATE_USER_ID = '_update_user_id_';
export const COLUMN_ACTION = '_action_';
export const COLUMN_STATUS = '_status_';
export const COLUMN_FLAG = '_flag_';
export const COLUMN_ASSIGN_USER = '_assign_user_';
export const COLUMN_APPROVE_STATUS = '_approve_status_';
export const COLUMN_APPROVE_COMMENT = '_approve_comment_';

export const enum DataStatusEnum {
  Insert = 1,
  Update = 2,
  // 下面定义的不是真实的状态，而是结合列计算的虚拟状态，比如Delete的3表示： column: ${COLUMN_FLAG}, status: 1
  Delete = 3,
  NoDelete = 4,
  Rejected = 5,
  Submitted = 6,
  UnSubmitted = 7,
  Issued = 8,
  UnIssued = 9,
  FLAG_NULL = 10,
  ACTION_NULL = 11,
}

export const enum BooleanEnum {
  False = 0,
  True = 1,
}

export const ONE_TABLE_NEW_KEY_COLUMNS = {
  update_time: COLUMN_UPDATE_TIME,
  create_time: COLUMN_CREATE_TIME,
  update_user_id: COLUMN_UPDATE_USER_ID,
  create_user_id: COLUMN_CREATE_USER_ID,
};

export const ONE_TABLE_NEW_COLUMN_EXPAND = {
  _version_id_: DbColumnTypeEnum.INT,
  [COLUMN_ASSIGN_USER]: DbColumnTypeEnum.USER_ID,
  [COLUMN_ACTION]: DbColumnTypeEnum.INT,
  [COLUMN_CREATE_TIME]: DbColumnTypeEnum.DATETIME,
  [COLUMN_UPDATE_TIME]: DbColumnTypeEnum.DATETIME,
  [COLUMN_CREATE_USER_ID]: DbColumnTypeEnum.USER_ID,
  [COLUMN_UPDATE_USER_ID]: DbColumnTypeEnum.USER_ID,
  [COLUMN_STATUS]: DbColumnTypeEnum.INT,
  [COLUMN_FLAG]: DbColumnTypeEnum.INT,
  [COLUMN_APPROVE_STATUS]: DbColumnTypeEnum.INT,
  [COLUMN_APPROVE_COMMENT]: DbColumnTypeEnum.STR,
};

export const enum DetailFromPageEnum {
  MANAGEMENT = 'management',
  TASK = 'task',
  GRANTED = 'granted', // 总览界面
}

export const enum FormTypeEnum {
  NORMAL = 'normal',
  PERIODIC = 'periodic',
  ENDLESS = 'endless',
}

export const enum TaskPersonalStatusEnum {
  UNHANDLED = 'unhandled',
  RUNNING = 'running',
  COMPLETE = 'complete',
}

export const enum DataTableTypeEnum {
  DETAIL = 'detail', // 详情表格
  FILL = 'fill', // 填报表格
  PRIVIEW = 'preview', // 预览表格
  ISSUE = 'issue', // 下发表格
  SUBMIT = 'submit', // 提交表格
}

export const enum FastViewTypeEnum {
  All = 'all',
  Add = 'add',
  Update = 'update',
  Delete = 'delete',
  Untouch = 'untouch',
  AllWithSubordinates = 'allWithSubordinates',
}

export const getHasDownload = (item: IOneTableNewOperatorDataComm, propsHasDownload = true) => {
  return (
    propsHasDownload && (item.featureFlags.enableDownloadData || (item.isFormManageLevelUser && !item.isCollaborate))
  );
};

export const transformPersonalStatusToFrontend = (item: IOnetableInvolvedForm, dataStatus: TaskPersonalStatusEnum) => {
  const assignWfData = item.assign_workflow?.data || {};
  const { data_state: ds } = assignWfData;
  const isCollaborate = isCollaborateTask(assignWfData);
  // 状态处理
  let aps: OneTableNewDataStateEnum = ds; // 默认
  if (dataStatus === TaskPersonalStatusEnum.UNHANDLED) {
    const urs = (item.unhandled_reason || OneTableNewDataStateEnum.FALLBACK) as OneTableNewDataStateEnum;
    aps = urs;
    if (urs === OneTableNewDataStateEnum.UNSUBMITTED || urs === OneTableNewDataStateEnum.FALLBACK) {
      aps = isCollaborate ? OneTableNewDataStateEnum.COLLABORATE : OneTableNewDataStateEnum.FALLBACK;
    }
  }
  return [aps, getReasonWithAssignWfData(assignWfData)];
};

export const getReasonWithAssignWfData = (data: Record<string, any>, otherReason?: string) => {
  const { data_state: ds, approval_comment: ac, command_comment: cc } = data;
  const refusedReason = ds === OneTableNewDataStateEnum.REFUSED ? cc : otherReason;
  const rejectReason =
    ds === OneTableNewDataStateEnum.REJECTED || ds === OneTableNewDataStateEnum.UNAPPROVED ? ac : otherReason;
  return refusedReason || rejectReason;
};

export const getFrontendPersonalStatus = (ps: string, ws?: string) => {
  return _.includes(RUNNING_STATUS, ws)
    ? ps === TaskPersonalStatusEnum.UNHANDLED
      ? TaskPersonalStatusEnum.UNHANDLED
      : TaskPersonalStatusEnum.RUNNING
    : TaskPersonalStatusEnum.COMPLETE;
};

export const getReasonTip = (reason?: string, approvalStatus?: OneTableNewDataStateEnum) => {
  const [t = '', s = ''] = getReasonAndTitle(reason, approvalStatus);
  return t + s;
};

export const getReasonAndTitle = (reason?: string, approvalStatus?: OneTableNewDataStateEnum) => {
  if (approvalStatus === OneTableNewDataStateEnum.REJECTED) {
    return [`${i18n.chain.proMicroModules.oneTable.rejectReasons}:`, reason];
  }
  if (approvalStatus === OneTableNewDataStateEnum.REFUSED) {
    return [`${i18n.chain.proMicroModules.oneTable.refusedReasons}:`, reason];
  }
  return [undefined, reason];
};

export const getFormTypeSpec = (formType: FormTypeEnum) => {
  const rc = ONE_TABLE_INFO.reportConfig;
  return isPeriodicForm(formType) ? rc.flowPeriodicSpec : isEndlessForm(formType) ? rc.flowEndlessSpec : rc.flowSpec;
};

export const getDraftFormMetaQuery = (): IOperatorFilter => {
  return {
    $and: [{ column: BIND_DATAPKG_ID_KEY, operator: ColumnOperatorFilterEnum.IS, param: null }],
  };
};

const RUNNING_STATUS = ['started', 'ready', 'ongoing'];
const COMPLETED_STATUS = ['cancelled', 'completed'];
export const getQueryWorkflowStatus = (completed?: boolean) => {
  return completed ? COMPLETED_STATUS : RUNNING_STATUS;
};

export const getWorkflowIsRuning = (status: string) => {
  return _.includes(RUNNING_STATUS, status);
};

// 获取流程的状态
export const getFlowStatusLable = (item: IWorkflow) => {
  const flowStatus = item.status;
  const data = item.data || {};
  let comment = data.cancel_comment;
  if (flowStatus === 'completed') {
    return [comment, i18n.chain.proMicroModules.oneTable.filled, 'error'];
  }
  // 被取消
  if (flowStatus === 'cancelled') {
    comment = data.workflow_cancel_comment;
    return [comment, i18n.chain.proMicroModules.oneTable.canceled, 'error'];
  }
  // 进行中
  return ['', i18n.chain.proMicroModules.oneTable.filling, 'primary'];
};

const BIND_DATAPKG_ID_KEY = 'bind_datapkg_id';
const ONETABLE_FORM_TYPE_KET = 'onetable_form_type';
const SOURCE_DATAPKG_ID_KET = 'source_datapkg_id';
const NEED_APPROVAL_KET = 'need_approval';
const CHECK_DATA_SUBMITTED_KET = 'check_data_submitted';
const PRIMARY_ASSIGNEE_KET = 'primary_assignee';
const ASSIGNEES_KET = 'assignees';
const END_DATE_KEY = 'end_date';
const SELECTED_CURRENT_LEVEL_USERS_KEY = 'selected_current_level_users';
const SELECTED_LOWER_LEVEL_ORG_USERS_KEY = 'selected_lower_level_org_users';
const FEATURE_FLAGS_KEY = 'feature_flags';
const CURRENT_VERSION_KEY = 'current_version';
const VERSION_GROUP_KEY = 'version_group';
const AFFILIATED_ORGS_KEY = 'affiliated_orgs';
// 后续新增需要保持顺序
export const FEATURE_FLAGS_KEYS = [
  'enableInsertData', // 允许新增数据
  'enableUpdateData', // 允许修改数据
  'enableDeleteData', // 允许删除数据
  'enableDownloadData', // 允许下载数据
  'enableSendDown', // 允许下发
  'enableTransfer', // 允许转交
  'enableReinforce', // 允许协同
  'enableRefuse', // 允许拒绝
  'enableModifyDataAfterSubmit', // 允许提交后修改数据
] as const;

export type IFeatureFlags = {
  [K in typeof FEATURE_FLAGS_KEYS[number]]: boolean;
};

const defaultFormTypeFeatureFlags: Record<string, number> = {};
export const getDefaultFeatureFlagsByFormType = (extraMeta: Record<string, any>) => {
  const formType = getFormType(extraMeta);
  return defaultFormTypeFeatureFlags[formType] ?? 0xffff;
};

export const transformFeatureFlagsToBackend = (extraMeta: Record<string, any>, modifyParam?: boolean) => {
  const flagMap = extraMeta[FEATURE_FLAGS_KEY] as Partial<IFeatureFlags>;
  const flags = _.reverse(_.map(FEATURE_FLAGS_KEYS, (key) => (flagMap?.[key] ? '1' : '0')));
  const result = parseInt(flags.join(''), 2);
  modifyParam && (extraMeta[FEATURE_FLAGS_KEY] = result);
  return result;
};

export const transformAffiliatedOrgsToBackend = (extraMeta: Record<string, any>) => {
  const affiliatedOrgs = extraMeta[AFFILIATED_ORGS_KEY];
  if (_.isEmpty(affiliatedOrgs)) {
    return extraMeta;
  }
  extraMeta[AFFILIATED_ORGS_KEY] = _.map(affiliatedOrgs, (it) => it.value || it);
  return extraMeta;
};

export const transformFeatureFlagsToFrontEnd = (extraMeta: Record<string, any>, modifyParam?: boolean) => {
  const flagStrs = (extraMeta[FEATURE_FLAGS_KEY] ?? getDefaultFeatureFlagsByFormType(extraMeta)).toString(2);
  const flags = _.reverse(_.split(_.padStart(flagStrs, FEATURE_FLAGS_KEYS.length, '1'), '').map((it) => it === '1'));
  const result: Partial<IFeatureFlags> = {};
  _.reduce(
    FEATURE_FLAGS_KEYS,
    (rslt, key: keyof IFeatureFlags, index) => {
      rslt[key] = flags[index];
      return rslt;
    },
    result,
  );
  modifyParam && (extraMeta[FEATURE_FLAGS_KEY] = result);
  return result as IFeatureFlags;
};

export const getSelectedLowerLevelOrgUsers = (data: Record<string, any>) => {
  return data[SELECTED_LOWER_LEVEL_ORG_USERS_KEY];
};

export const getSelectedCurrentLevelUsers = (data: Record<string, any>) => {
  return data[SELECTED_CURRENT_LEVEL_USERS_KEY];
};

export const getFormOwner = (form: Record<string, any>) => {
  const { app_id, user_id } = form;
  return { appId: app_id, userId: user_id };
};

// 获取表单类型
export const getFormType = (extraMeta?: Record<string, any>): FormTypeEnum => {
  return extraMeta?.[ONETABLE_FORM_TYPE_KET] ?? FormTypeEnum.NORMAL;
};

export const removeBindPkgId = (extraMeta?: Record<string, any>) => {
  delete extraMeta?.[BIND_DATAPKG_ID_KEY];
};

// 获取绑定数据包id
export const getBindPkgId = (extraMeta?: Record<string, any>) => {
  return extraMeta?.[BIND_DATAPKG_ID_KEY];
};

// 获取所属部门
export const getAffiliatedOrgs = (extraMeta?: Record<string, any>) => {
  return extraMeta?.[AFFILIATED_ORGS_KEY];
};

export const getSourcePkgId = (extraMeta?: Record<string, any>) => {
  return extraMeta?.[SOURCE_DATAPKG_ID_KET];
};

export const getEndDate = (extraMeta?: Record<string, any>) => {
  return extraMeta?.[END_DATE_KEY];
};

// 获取extra通用数据
export const getExtraMetaComm = (formType: FormTypeEnum, sourcePkgId?: string) => {
  return {
    [ONETABLE_FORM_TYPE_KET]: formType,
    with_data: !!sourcePkgId,
    source_datapkg_id: sourcePkgId,
    is_periodic: isPeriodicForm(formType), // 兼容下
  };
};

// 是否是周期
export const isPeriodicForm = (extraMeta: Record<string, any> | string) => {
  const val = _.isString(extraMeta) ? extraMeta : extraMeta[ONETABLE_FORM_TYPE_KET];
  return val === FormTypeEnum.PERIODIC;
};

// 是否是常态化
export const isEndlessForm = (extraMeta: Record<string, any> | string) => {
  const val = _.isString(extraMeta) ? extraMeta : extraMeta[ONETABLE_FORM_TYPE_KET];
  return val === FormTypeEnum.ENDLESS;
};

export const getVersionGroup = (data: Record<string, any>): number => {
  return data[VERSION_GROUP_KEY] || 0;
};

export const getPeriodicVersionName = (data: Record<string, any>) => {
  const version = data[CURRENT_VERSION_KEY] as number;
  return version ? `${i18n.chain.proMicroModules.oneTable.flowType.periodic}(${formateDate2(version, 2)})` : '';
};

export const getDisplayFormName = (itemData: IOneTableNewOperatorDataComm, custormName?: string) => {
  const { isPeriodic, versionName, formName } = itemData;
  const name = custormName || formName;
  return isPeriodic ? `${name}【${versionName}】` : name;
};

// 是否需要审批
export const isNeedApproval = (data: Record<string, any>) => {
  return data[NEED_APPROVAL_KET];
};

// 是否需要检查数据提交状态
export const isCheckDataSubmitted = (data: Record<string, any>) => {
  return data[CHECK_DATA_SUBMITTED_KET];
};

export const getPrimaryAssignee = (data: Record<string, any>) => {
  return data[PRIMARY_ASSIGNEE_KET];
};

export const getAssignees = (data: Record<string, any>) => {
  return data[ASSIGNEES_KET] || [];
};

// 是否协同任务(负责人是不是自己，可以认为就是协同任务)
export const isCollaborateTask = (data: Record<string, any>) => {
  const currentUser = DatlasAppController.getInstance().getUserId();
  return getPrimaryAssignee(data) !== currentUser;
};

// 是否是报表创建者
export const isFormManageLevelUser = (rootWfId: string, assignParentWfId: string) => {
  return _.isEqual(rootWfId, assignParentWfId);
};

const ONE_TABLE_NEW_COLUMNS = _.keys(ONE_TABLE_NEW_COLUMN_EXPAND);
export const ONE_TABLE_NEW_COLUMN_LIST = ONE_TABLE_INFO.newIgnoreDownloadColumns || ONE_TABLE_NEW_COLUMNS;

const ignoreOneTabelNewColumns = <T extends Record<string, any>>(columns: T[], key = 'name') => {
  const ignoreColumns = ONE_TABLE_INFO.newIgnoreDisplayColumns || ONE_TABLE_NEW_COLUMNS;
  return _.reject(columns, (c) => _.includes(ignoreColumns, c[key]));
};

export const ignoreFrontendColumns = <T extends Record<string, any>>(columns: T[], key = 'name') => {
  return _.flow(ignoreOneTabelNewColumns, ignoreMetroOptionColumns)(columns, key);
};

export const getNewRootWfspecId = (): string => {
  if (!ONE_TABLE_INFO.newRootWfspecId) {
    toastApi.error(i18n.chain.proMicroModules.oneTable.startSpecError);
    return '';
  }
  return ONE_TABLE_INFO.newRootWfspecId;
};

// 流程2的ID
export const getNewAssignWfspecId = (): string => {
  if (!ONE_TABLE_INFO.newAssignWfspecId) {
    toastApi.error(i18n.chain.proMicroModules.oneTable.flowSpecError);
    return '';
  }
  return ONE_TABLE_INFO.newAssignWfspecId;
};

export const enum IssueDataFilterTypeEnum {
  ALL = 'all',
  FILTER = 'filter',
  SELECTED = 'selected',
}

export const getIssuedStatusLabel = (row: Record<string, any>, primaryAssignee: number) => {
  const aus = row[COLUMN_ASSIGN_USER];
  const doing = _.isNil(aus) || _.isEqual(aus, primaryAssignee); // 未分配
  return doing
    ? [i18n.chain.proMicroModules.oneTable.issuedDoing, 'processing']
    : [i18n.chain.proMicroModules.oneTable.issuedOk, 'success'];
};

export const getDataStatusTagsComm = (row: Record<string, any>) => {
  if (row[COLUMN_FLAG] === BooleanEnum.True) {
    return [[i18n.chain.dataActionStatus.delete, 'error']];
  }
  const action = row[COLUMN_ACTION];
  if (action === DataStatusEnum.Insert) {
    return [[i18n.chain.dataActionStatus.insert, 'processing']];
  }
  if (action === DataStatusEnum.Update) {
    return [[i18n.chain.dataActionStatus.update, 'success']];
  }
  return [];
};
export const getNotNeedApproveDataStatusTags = (row: Record<string, any>) => {
  const tags: any[] = [];
  const status = row[COLUMN_STATUS];
  if (status === BooleanEnum.True) {
    tags.push([i18n.chain.dataActionStatus.synced, 'success']);
  } else if (status === BooleanEnum.False) {
    tags.push([i18n.chain.dataActionStatus.unsynce, 'warning']);
  }
  tags.push(...getDataStatusTagsComm(row));
  return tags.filter(Boolean);
};

// 不需要审核
export const getNeedApproveDataStatusTags = (row: Record<string, any>, isLeader?: boolean) => {
  const tags: any[] = [];
  const status = row[COLUMN_APPROVE_STATUS];
  if (status === BooleanEnum.False) {
    tags.push([
      isLeader ? i18n.chain.dataActionStatus.reject : i18n.chain.dataActionStatus.rejected,
      'error',
      getReasonTip(row[COLUMN_APPROVE_COMMENT]),
    ]);
  } else if (status === BooleanEnum.True) {
    tags.push([isLeader ? i18n.chain.dataActionStatus.approve : i18n.chain.dataActionStatus.approved, 'success']);
  }
  tags.push(...getDataStatusTagsComm(row));
  return tags.filter(Boolean);
};

export const getProcessRowsOptions = (
  isFormManageLevelUser: boolean,
  extra?: Omit<IProcessRowsOptions, 'addRowStatusVal'>,
): IProcessRowsOptions => {
  // 管理员级别直接提交, 其他级别的人员修改，忽略提交状态
  return { ...extra, addRowStatusVal: isFormManageLevelUser ? BooleanEnum.True : BooleanEnum.False };
};

// 数据处理
interface IProcessRowsOptions {
  addRowStatusVal?: number | null;
  ignoreUpdateRowActionStatus?: boolean;
}
export const processRows = (
  sources: Record<string, any>[],
  targets: Record<string, any>[],
  options: IProcessRowsOptions,
) => {
  const { addRowStatusVal, ignoreUpdateRowActionStatus } = options || {};
  // 判断新增的数据, 没有id为空, 即代表新增, 排除空对象
  const todoAddRows = _.filter(targets, (it) => !it.id && !_.isEmpty(it));
  const addColumns = _.uniq(_.flatMap(todoAddRows, (row) => _.keys(row)));
  const currentUser = DatlasAppController.getInstance().getUserId();
  const currentTime = getCurrentDateStr();
  const addValues = _.map(todoAddRows, (row) => {
    const rows = _.map(addColumns, (it) => row[it] ?? null);
    // 追加新增表示
    // 追加COLUMN_ASSIGN_USER是为了领导分配时方便知道已分配
    rows.push(DataStatusEnum.Insert, currentUser, currentUser, currentUser, currentTime, currentTime, addRowStatusVal);
    return rows;
  });
  // 新增_action_列
  addValues.length &&
    addColumns.push(
      COLUMN_ACTION,
      COLUMN_ASSIGN_USER,
      COLUMN_CREATE_USER_ID,
      COLUMN_UPDATE_USER_ID,
      COLUMN_CREATE_TIME,
      COLUMN_UPDATE_TIME,
      COLUMN_STATUS,
    );
  const addRows = { columns: addColumns, values: addValues };
  // 判断删除的数据(硬删除)
  const deleteRows = _.map(_.differenceBy(sources, targets, 'id'), 'id');
  // 找出更新对象
  const updateRows: any[] = [];
  _.forEach(sources, (originRow) => {
    if (!originRow) return;
    const matchedItem = targets.find((editorRow: any) => editorRow.id === originRow.id);
    // 不相同，找出不相同的列即改动的值
    if (matchedItem && !_.isEqual(originRow, matchedItem)) {
      const columns = _.uniq([..._.keys(originRow), ..._.keys(matchedItem)]);
      const updateColumns = _.filter(columns, (column) => {
        return !_.isEqual(originRow[column] ?? null, matchedItem[column] ?? null);
      });
      if (updateColumns.length !== 0) {
        const values = _.map(updateColumns, (column) => matchedItem[column] ?? null);
        const upColumns = ['id', COLUMN_UPDATE_USER_ID, COLUMN_UPDATE_TIME, ...updateColumns];
        const upRows = [matchedItem.id, currentUser, currentTime, ...values];
        // 如果编辑的是Insert或者delete数据
        if (
          !ignoreUpdateRowActionStatus &&
          originRow[COLUMN_ACTION] !== DataStatusEnum.Insert &&
          !_.includes(upColumns, COLUMN_ACTION)
        ) {
          upColumns.push(COLUMN_ACTION);
          upRows.push(DataStatusEnum.Update);
        }
        // 更新数据时，添加更新部门id
        updateRows.push({ columns: upColumns, values: [upRows] });
      }
    }
  });
  // 返回新增、删除和更新的行数据
  return { addRows, deleteRows, updateRows };
};

export interface IDataDeliveryInfo {
  totalAssignRows: number;
  totalUsers: number;
  maxSendDownDepth: number;

  latestUpdateTime: string;
  latestUpdateDaysAgo?: number;
  earliestUpdateTime?: string;
}

export const buildDataDeliveryTooltip = (
  info: (IOnetableDownstreamStat & { root: IOnetableDownstreamStat }) | undefined | null,
): string => {
  if (!info) {
    return '';
  }

  const { stat_include_downstream, root } = info;

  const totalAssignRows = stat_include_downstream.total_assign_rows;
  const totalUsers = stat_include_downstream.total_assign_users;
  const maxSendDownDepth = stat_include_downstream.max_send_down_depth;

  // Calculate the percentage based on root's total rows
  let dataPercentage = '0.0';
  if (root?.stat_include_downstream?.total_assign_rows > 0) {
    dataPercentage = ((totalAssignRows / root.stat_include_downstream.total_assign_rows) * 100).toFixed(1);
  }

  // 格式化日期
  let latestUpdateTime = '-';
  let earliestUpdateTime = '-';

  if (stat_include_downstream.latest_data_update_time) {
    latestUpdateTime = formateDateWithoutMillisecond(stat_include_downstream.latest_data_update_time, DATE_FORMATTER_2);
  }

  if (stat_include_downstream.earliest_data_update_time) {
    earliestUpdateTime = formateDateWithoutMillisecond(
      stat_include_downstream.earliest_data_update_time,
      DATE_FORMATTER_2,
    );
  }

  return [
    `${i18n.chain.proMicroModules.oneTable.dataDeliveryOverview}`,
    '─────────────────────────',
    `${i18n.chain.proMicroModules.oneTable.dataTotalCount(totalAssignRows, dataPercentage)}`,
    `${i18n.chain.proMicroModules.oneTable.userTotalCount(totalUsers)}`,
    `${i18n.chain.proMicroModules.oneTable.organizationLevel(maxSendDownDepth)}`,
    `${i18n.chain.proMicroModules.oneTable.latestUpdateTime}:
${latestUpdateTime}`,
    `${i18n.chain.proMicroModules.oneTable.earliestUpdateTime}:
${earliestUpdateTime}`,
  ].join('\n');
};
