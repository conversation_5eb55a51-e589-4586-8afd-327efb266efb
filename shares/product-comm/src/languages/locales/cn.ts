import dayjs from 'dayjs';
import { cn as bsCommCn } from '@mdtBsComm/languages';
import { cn as bsComponentsCn } from '@mdtBsComponents/languages';

export const cn = {
  ...bsCommCn,
  ...bsComponentsCn,
  permissions: {
    export_chart_data: '导出图表数据',
    share_datapkg_cross_app: '申请跨机构分享数据',
    public_datainfo_doc_add: '创建公共数据说明模板',
    other_general: '其他通用模块',
    update_mdt_user: '修改MDT用户',
    pkg_archive: '编辑数据说明模板',
    modify_mobile_page: '编辑移动端素材',
    app_pkg_datainfo_doc_button_show: '显示专享主题库数据治理说明申请查看按钮',
    am_menu_appsetting_manage: '机构偏好菜单',
    apply_for_impersonate: '申请临时模拟登录',
    personal_data: '我的数据（老版）',
    upload_pkg_by_data_or_dblink: '上传数据',
    update_pkg_album_approve: '分发机构数据更新权',
    mobile_work_space: '移动素材',
    share_project_cross_app: '跨APP分享项目',
    create_role_tag: '创建角色标签',
    get_user_roles: '获取用户角色',
    app_manage_center: '多机构管理菜单',
    delete_app: '删除App',
    pkg_album_tag: '编辑标签组',
    update_app: '编辑APP',
    appdata_share: '管理数据分享机构',
    build_personalized_login: '个性化登录管理',
    create_project_from_code: '使用拷贝码新建项目',
    enter_backend: '后台多机构管理（旧版）',
    user_history_menu: '用户操作日志菜单',
    manage_app_role: '管理机构角色',
    authorize_permission: '授予权限',
    dm_menu_meta_manage: '元数据模板管理菜单',
    public_archive_approve: '公共数据治理说明查看申请审批权限',
    download_pkg_album_approve: '审批数据下载申请',
    impersonate_any_app: '模拟任意app',
    process_engine: '流程引擎菜单',
    cross_app_manage: '管理跨机构分享',
    custom_login_page: '自定义登录页面',
    list_my_app_tasks: '查看本APP所有任务',
    preset_role_management: '通用角色管理菜单',
    am_menu_user_role_manage: '用户角色管理菜单',
    impersonate_management: '模拟登录管理',
    update_all_public_pkg: '可操作公共数据市场所有数据包',
    create_sensitive_fdw: '链接敏感数据库',
    bg_menu_app_role: '机构角色管理菜单',
    create_preference_spec: '新增偏好定义',
    update_preference_spec: '更改偏好定义',
    public_pkg_meta_edit: '编辑公共数据包元数据',
    delete_dblink: '删除数据库链接',
    dm_menu_tag_manage: '标签组管理',
    update_public_pkg_album_approve: '审批公共主题库编辑申请',
    add_login_page: '配置个性化登录页',
    get_impersonate_record: '获取模拟登录记录',
    group_menu: '工作组',
    create_user_tag: '创建用户标签',
    delete_published_pkg: '删除已发布数据包',
    ds_product: 'Datlas菜单',
    delete_pkg_album: '删除主题库',
    update_custom_package: '编辑个人数据包权限',
    view_app_all_task: '管理APP任务',
    ds_sensitive_operation: '地图轻应用敏感操作',
    add_pkg_album: '创建主题库',
    update_pkg_album: '编辑主题库',
    app_pkg_meta_edit: '管理机构数据包元数据',
    batch_update_user: '批量修改用户',
    map_v2: '地图V2.0',
    am_menu_authority_manage: '授权管理菜单',
    new_datamap: '体验新版',
    update_preference_identifier: '修改偏好名字（信息）',
    authorize_pkg_to_anyone: '授权公共包',
    task_center: '任务中心',
    dm_product: '数据市场菜单',
    download_pkg: '下载私有数据',
    qe_chart: 'QE图表',
    public_pkg_album_management: '公共主题库资源管理',
    archive_approve: '数据治理说明查看申请审批权限',
    audit: '审计日志',
    pkg_creator: '数据包创建者查看',
    pkg_album_approve: '审批数据使用申请',
    delete_mdt_user: '删除MDT用户',
    download_public_pkg_album_approve: '公共数据下载申请审批权限',
    public_tag_del: '删除公共标签',
    subscription: '订阅',
    get_mdt_user: '获取MDT用户',
    data_manage_center: '数据管理中心',
    am_user_tag_edit: '编辑用户标签',
    public_pkg_creator_view: '公共数据包创建者查看',
    delete_role: '删除角色',
    ailab_v2: 'AILAB2.0',
    delete_user_tag: '删除用户标签',
    manage_app_all_user: '编辑用户权限',
    df_product: '数据工厂菜单',
    maintenance_stats: '运维统计菜单',
    create_role: '创建角色',
    create_permission: '创建权限',
    share_map_poster: '地图海报分享',
    update_app_preference: '机构偏好操作',
    data_management_menu: '机构数据菜单',
    dm_menu_datasearch: '数据搜索菜单',
    delete_permission_tag: '删除权限标签',
    datainfo_doc_del: '删除数据说明模板',
    script: '脚本',
    app_pkg_use_button_show: '申请数据使用',
    default_page_batch_management: '设置机构首页大屏',
    resource_share: '资源共享',
    user_contact_info: '查看用户联系方式',
    datalake: 'DataLake',
    upload_and_update_pkg: '上传加工',
    manage_dblink: '自定义数据源',
    echarts: 'Echarts',
    public_pkg_info_management: '管理公共数据中心数据包信息',
    publish_to_pkg_album: '申请上架数据包到主题库',
    send_email: '发送验证邮件',
    public_pkg_download_approve: '公共数据包下载审批',
    work_space_distribute_data: '工作台分发日志',
    dm_menu_doctemplate_manage: '数据包文档',
    public_pkg_download_button_show: '显示公共主题库数据申请下载按钮',
    delete_sys_tag: '删除系统标签',
    ds_old: '地图轻应用旧版功能',
    global_role_management: '通用角色管理',
    create_fdw: '链接数据库',
    am_menu_user_manage: '用户管理菜单',
    sql_chart: 'SQL图表',
    get_login_page: '获取个性化登录页',
    public_pkg_read_approve: '公共数据包使用审批',
    public_pkg_datainfo_doc_button_show: '显示公共主题库数据治理说明申请查看按钮',
    public_datainfo_doc_del: '删除公共数据说明模板',
    user_management_menu: '用户管理',
    pre_pkg_create: '创建工厂数据包',
    public_pkg_archive: '编辑公共数据说明模板',
    dm_public_pkg_publish_approve: '数据包上架到公共主题库申请审批权限',
    app_pkg_download_button_show: '申请数据下载',
    dm_menu_album_manage: '主题库管理',
    asset_statistics_menu: '元数据管理菜单',
    local_market_pkg_approve: '数据审批菜单',
    update_user: '编辑用户',
    back_to_work_space: '返回系统按钮',
    query_app_admin: '查询APP管理员',
    public_market: '数据中心',
    permission_management: '功能权限管理菜单',
    reset_pwd: '修改机构用户密码',
    am_menu_user_authority_manage: '用户权限管理菜单',
    backend_user_management: '后台用户管理',
    pkg_retrieve_approve: '审批数据查看申请',
    ds_colpkg_create: '发起数据包协同',
    pd_menu_personaldata: '个人数据菜单',
    data_report: '数据报告大屏',
    public_pkg_album_approve: '公共数据使用申请审批权限',
    sys_setup_menu: '机构样式',
    delete_local_tag: '删除本地标签',
    create_tag: '临时创建标签权限，新版上了以后删除',
    publish_pkg: '数据发布',
    change_permission: '管理功能权限',
    add_customized_base_map: '添加自定义底图',
    pkg_editor_invite: '邀请数据包编辑者',
    pkg_update_approve: '分发机构数据更新权',
    public_pkg_use_button_show: '显示公共主题库数据申请使用按钮',
    pkg_album_management: '获得app所有主题库资源的权限',
    bg_product: '后台管理菜单',
    tag_del: '删除标签组',
    create_collector_user: '创建Datlas用户的同时创建Collector用户',
    create_user: '创建用户',
    nopwd_signin: '免密登录管理',
    return_home: '返回系统',
    data_market_app_management_menu: '数据市场APP管理_admin',
    manage_preset_role: '管理通用角色',
    low_code_etl: '低码ETL菜单',
    update_marker: '修改地图自定义图标',
    delete_custom_package: '删除个人数据包权限',
    delete_user: '删除用户',
    data_market_user_management_menu: '数据市场用户管理_admin',
    material: '素材',
    data_approval: '公共数据包审批',
    get_others_resource_permission_info: '查询他人资源权限信息',
    public_album_distribute: '分配公共主题库给app的权限',
    edit_work_space: '编辑大屏素材',
    ailab: 'Lab',
    app_administrator_permissions: '管理员权限配置',
    app_pkg_unload: '下架机构数据',
    manage_managed_app: '配置可管理APP',
    work_space_manage_center: '工作台管理中心',
    publish_to_private_data_market: '申请发布个人数据',
    dm_ds_switch: 'DS_相互跳转权限',
    pkg_album_management_menu: '主题库菜单权限',
    dm_datadashboard_menu: '数据看板菜单',
    role_management: '管理机构角色',
    create_dataset: '创建外连数据库',
    work_space: '页面菜单',
    delete_permission: 'delete_permission',
    customized_page: '自定义大屏',
    standard_page: '标准大屏',
    map: '地图',
    app_impersonate_link: '机构内模拟登录',
    am_product: '机构管理菜单',
    update_own_public_pkg: '只可操作自己上传的数据包',
    update_pkg_by_etl: '低码etl',
    query_all_app: '查询所有App',
    datainfo_doc_add: '创建数据说明模板',
    cm_product: 'CollectorManager模块跳转',
    vault_link_management: '大屏外链发布',
    bg_impersonate: '后台模拟登录',
    unauthorize_permission: '取消授权',
    manage_app_all_role: '编辑角色',
    manage_datlas: 'Datlas管理',
    dm_pkg_publish_approve: '审批数据发布申请',
    am_menu_user_tag_manage: '用户标签管理菜单',
    temp_pkg_to_public: '过渡_上传数据到公共数据中心权限',
    delete_public_pkg_album: '删除公共主题库',
    public_pkg_retrieve_approve: '公共数据检索申请审批权限',
    am_menu_sensitive_operation_manage: '敏感操作菜单',
    app_mangement: 'App管理',
    dm_app_pkg_publish_approve: '数据包上架到专享主题库申请审批权限',
    create_permission_tag: '创建权限标签',
    create_contact: '创建联系人',
    add_mdt_user: '创建MDT用户',
    manage_subscription: '管理APP订阅',
    app_pkg_retrieve_button_show: '申请数据查看',
    public_pkg_unload: '下架公共数据包',
    run_etl: 'run_etl',
    am_album_distribute: '主题库分配',
    cron_job_management: '管理定时任务',
    work_space_distribute: '工作台分发',
    public_pkg_view_detail_approve: '公共数据包查看明细审批',
    query_pkg_album_approve: '审批专享主题库检索申请',
    chart: '图表',
    add_public_pkg_album: '创建公共主题库',
    delete_pkg_album_tag_of_pkg: '删除数据包的主题库标签',
    add_pkg_album_tag_for_pkg: '数据包添加主题库标签',
    template_factory: '模板库',
    private_market: '专享数据中心',
    delete_preference_spec: '删除偏好定义',
    material_v2: '素材2.0',
    user_history_download: '导出用户操作日志',
    create_client: '创建客户端',
    create_preference_identifier: '增加偏好名字',
    query_public_pkg_album_approve: '审批公共主题库检索申请',
    md_product: '我的数据',
    public_pkg_album_tag: '编辑公共标签',
    data_update_audit: '数据更新日志',
    ot_product: '一表通',
    impersonate: '模拟登录',
    public_tag_add: '创建公共标签',
    app_pkg_create: '创建机构数据包',
    user_mangement: '用户管理(admin)',
    ot_table_management: '报表管理',
    pkg_owner_transfer: '转移数据包所有者',
    public_pkg_retrieve_button_show: '显示公共主题库数据申请检索按钮',
    delete_preference_identifier: '删除偏好名字',
    update_login_page: '修改个性化登录页',
    fuzzy_search_all_entity: '模糊查找所有实体',
    tag_add: '创建标签',
    data_market_management_menu: '数据市场管理菜单权限',
    manage_invite_code: '注册邀请码管理',
    create_app: '创建App',
    delete_role_tag: '删除角色标签',
    dm_temporary_upload_2_private_market: '直接创建机构数据',
    delete_login_page: '删除个性化登录页',
    update_public_pkg_album: '编辑公共主题库',
    publish_to_data_market: '发布公共市场包',
  },
  proComm: {
    wfAutoStore: {
      name: '填报人',
      app: '填报人所属机构',
      createTime: '填报时间',
    },
    wfTmpl: {
      initiatorName: '发起人',
      initiatorApp: '发起人所属机构',
      createTime: '填报时间',
      specName: '流程名称',
      initiatorId: '发起人ID',
      initiatorName2: '发起人姓名',
      initiatorAppId: '发起所属机构ID',
      initiatorAppName: '发起所属机构名称',
      specId: '流程ID',
      workflowId: '流程实例ID',
      initiateTime: '发起时间',
      currentUserID: '填报人ID',
      currentUserName: '填报人姓名',
      currentApp: '填报人所属机构ID',
      currentAppName: '填报人所属机构名称',
      taskId: '任务实例ID',
      taskSpecName: '任务名称',
      taskSpecId: '任务ID',
      workflowStatus: '流程实例状态',
      wfApprovalResult: '流程实例审批结果',
      wfCreateTimeStr: '流程实例创建日期时间',
      wfCreateDateStr: '流程实例创建日期',
      parentWorkflowId: '父流程实例ID',
      parentTaskId: '父任务ID',
      currentTimestampMs: '当前时间戳(精确毫秒)',
      currentTimestamp: '当前时间戳',
      currentDateTimeStr: '当前日期时间',
      currentDateStr: '当前日期',
      currentTimeStr: '当前时间',
      startof: '发起的',
      start: '发起',
      with: '于',
    },
    wfSpecType: {
      pkg: '数据包填报',
      oneTable: '一表通',
    },
  },
  // product
  product: {
    common: '脉策公共',
    dataFactory: '数据工厂',
    dataMarket: '数据市场',
    myData: '我的数据',
    orgAdmin: '机构管理',
    resourceShare: '资源分享',
    workflow: '流程引擎',
    oneTable: '一表通',
    datlas: '地图轻应用',
    form: '表单设计',
  },
  // api
  api: {
    0: '网络连接超时',
    400: '当前请求参数错误',
    500: '内部服务器错误',
    502: '错误的网关',
    503: '超载或系统维护',
    504: '网关超时',
  },
  apiNotify: {
    errorTitle: 'api请求错误列表',
    copyErrorDesc: '复制错误信息后请发送到问题反馈群，谢谢。',
    copyError: '复制错误信息',
    failedApi: (count: number) => `当前有【${count}】个请求失败`,
    failedApiDesc: '如果您无法解决该问题，您可以点击【反馈】按钮来复制错误请求，发送给研发人员',
  },
  rc: {
    1011: '用户不存在或密码错误',
    1012: '账号已过期',
    1013: '手机号未注册',
    1014: '验证码请求频繁',
    1015: '验证码错误或已过期',
    1016: '微信验证错',
    1017: '绑定微信的vcode错误或已失效',
    1018: '非法微信登录',
    1020: '微信账号重复绑定',
    1026: '验证码发送失败，手机号无效',
    1050: [
      '第{{times}}次登陆失败, 请等待{{timedelta}}再登录',
      {
        times: 0,
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1051: [
      '用户被锁定，需等待{{timedelta}}后再登录',
      {
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1077: '验证码错误',
    3002: '微信api错误',
    3003: '阿里云短信验证码接口已知错误',
    3004: '阿里云短信验证码接口未知错误',
  },
  comText: {
    total: '总数：',
    create: '创建',
    update: '修改',
    name: '名称',
    updateTime: '更新时间',
    copyLink: '复制链接',
    yes: '是',
    no: '否',
    filter: '筛选',
    lastOneMonth: '近一个月',
    lastThreeMonth: '近三个月',
    lastSixMonth: '近六个月',
    selectAll: '全选',
    open: '展开',
    close: '收起',
    tag: '标签',
    selectDatapkg: '选择数据包',
    select: '选择',
    project: '项目',
    contact: '联系人',
    orgUser: '部门用户',
    none: '无',
    none2: '-',
    more: '更多',
    operation: '操作',
    showFullInfo: '查看完整信息',
    end: '结束',
    upload: '上传',
    download: '下载',
    template: '模版',
    uploadTemp: '上传模版',
    sendDownMode: '下发模式',
    batchOpt: '批量操作',
    replace: '替换',
    append: '追加',
    cancel: '取消',
    copy: '复制',
    remove: '移除',
    equal: '等于',
    notEqual: '不等于',
    ge: '大于',
    gt: '大于等于',
    le: '小于等于',
    lt: '小于',
    default: '默认',
    custom: '自定义',
    user: '用户',
    role: '角色',
    org: '部门',
    orgLeader: '部门负责人',
    orgOnetableAdmin: '部门一表通负责人',
    group: '群组',
    add: '添加',
    durationDate: '间隔日期',
    durationTime: '间隔时间',
    year: '年',
    month: '月',
    day: '日',
    hour: '时',
    minute: '分',
    second: '秒',
    xmlParseError: 'xml文件不能正确解析',
    datapkg: '数据包',
    noPermission: '暂无权限',
    other: '其他',
    periodic: '周期性',
    approval: '待审核',
  },
  comNum: {
    one: '一',
    two: '二',
    three: '三',
    four: '四',
    five: '五',
    six: '六',
    seven: '七',
    eight: '八',
    nine: '九',
    ten: '十',
  },
  comWarning: {
    noEmpty: '不能为空',
    del: (name?: string) => `确认删除: ${name}?`,
    fileSize: (mVal: number) => `单个文件最大支持${mVal}M`,
    zipFileSize: (mVal: number) => `zip文件最大支持${mVal}M`,
    fileType: (type: string) => `暂不支持当前文件类型[${type}]`,
  },
  dataActionStatus: {
    insert: '新增',
    update: '已更新',
    delete: '已删除',
    noDelete: '未删除',
    null: '待处理',
    synced: '已同步',
    unsynce: '未同步', // 填报者视角看
    submit: '已提交',
    unsubmit: '未提交',
    rejected: '被驳回', // 填报者视角看
    reject: '检查通过', // 审核者视角看
    approved: '被通过', // 填报者视角看
    approve: '检查有误', // 审核者视角
    empty: '为空',
  },
  dataStateLabel: {
    unsubmitted: '数据未提交',
    submitted: '数据已提交',
    approved: '审核通过',
    rejected: '审核驳回',
    refused: '任务被退回',
    cancelled: '任务已撤销',
    submitted2: '数据待审核',
    submitted4: '已提交,待审核',
    unsubmitted2: '待数据收集',
    unsubmitted3: '撤销提交',
    fallback: '接到新任务',
    rootNotSentDown: '报表未下发',
    unapproved: '待审核数据',
    collaborate: '接到新协同任务',
  },
  taskStatusLabel: {
    unsubmitted: '待你提交数据',
    unapproved: '待你审核数据',
    unapproved2: '待你审核',
    rejected: '提交被驳回',
  },
  flowStatusLabel: {
    submit: '提交任务',
    unsubmit: '撤回提交',
    approve: '提交被审批通过', // 提交者视角
    reject: '提交被审核驳回',
    approved: '审核提交:通过', // 审核者视角
    rejectd: '审核提交:驳回',
    taskUser: '任务负责人',
    approveUser: '审核人',
    editCollaborate: '调整协同人员',
    afterEdit: '调整后人员',
    newAdd: '新增人员',
    submitData: '提交数据',
    submitData2: '同步数据',
    startUser: '发起人',
    endUser: '结束人',
    issuedUser: '下发人',
    mainUser: '负责人',
    refourceUser: '协同人员',
    submintUser: '提交人',
    submintUser2: '同步人',
    unsubmitUser: '撤回人',
    reason: '驳回意见',
  },
  comTip: {
    updateSuccess: '更新成功',
    createSuccess: '新建成功',
    addSuccess: '添加成功',
    deleteSuccess: '删除成功',
    copySuccess: '复制成功',
    publishSuccess: '发布成功',
    submitSuccess: '提交成功',
    optSuccess: '操作成功',
    sendSuccess: '发送成功',
    saveSuccess: '保存成功',
    unnamed: '未命名',
    nameEmptyError: '名称不能为空',
    formNoChanged: '表单未变更,无需保存',
    primaryEmpty: '负责人不能为空',
  },
  comConfirm: {
    finish: '确认完成？',
    submit: '确认提交？',
  },
  comDataType: {
    int: '整数',
    float: '小数',
    str: '字符串',
    bool: '布尔',
    date: '日期',
    datetime: '日期时间',
    intArray: '整值数组',
    strArray: '字符串数组',
    json: '对象',
    timeStamp: '时间戳(精确到秒)',
    timeStampMS: '时间戳(精确到毫秒)',
  },
  permission: {
    read: '可使用',
    read2: '可查看',
    viewDetail: '可检索',
    download: '可下载',
    update: '可编辑',
    auth: {
      read: '使用权',
      update: '编辑权',
      viewDetail: '检索权',
      download: '下载权',
      pkgUpdate: '编辑权',
      metaUpdate: '元数据编辑权',
      execute: '执行权',
      look: '查看权',
      publish: '使用权',
    },
  },
  geo: {
    point: '点数据',
    line: '线数据',
    polygon: '面数据',
    plain: '非地理数据',
    pointToPoint: '点->点数据',
    pointToLine: '点->线数据',
    pointToPolygon: '点->面数据',
    lineToPoint: '线->点数据',
    lineToLine: '线->线数据',
    lineToPolygon: '线->面数据',
    polygonToPoint: '面->点数据',
    polygonToLine: '面->线数据',
    polygonToPolygon: '面->面数据',
    点数据: '点数据',
    线数据: '线数据',
    面数据: '面数据',
    非地理数据: '非地理数据',
    '点->点数据': '点->点数据',
    '点->线数据': '点->线数据',
    '点->面数据': '点->面数据',
    '线->点数据': '线->点数据',
    '线->线数据': '线->线数据',
    '线->面数据': '线->面数据',
    '面->点数据': '面->点数据',
    '面->线数据': '面->线数据',
    '面->面数据': '面->面数据',
  },
  column: {
    type: {
      int: '数值(整数)',
      bitInt: '数值(长整数)',
      numberIc: '数值',
      float: '数值(小数)',
      str: '文本',
      bool: '布尔',
      date: '日期',
      time: '时间',
      dateTime: '日期时间',
      timeStemp: '时间戳',
      geometry: '地理',
      json: '对象',
      jpg: '图片',
      image: '图片',
      mediaJson: '文件',
      array: '数组',
      arrayStr: '文本数组',
      arrayInt: '整数数组',
      arrayFloat: '小数数组',
      bigint: '大数值(整数）',
      bigfloat: '大数值(浮点）',
      userId: '用户ID(整数）',
      orgId: '组织ID(整数）',
      groupId: '群组ID(整数）',
      roleId: '角色ID(整数）',
      generalRoleId: '通用角色ID(整数）',
    },
    operator: {
      empty: '为空',
      notEmpty: '非空',
      eq: '等于',
      ne: '不等于',
      equal: '等于',
      notEqual: '不等于',
      is: '为空',
      gt: '大于',
      ge: '大于等于',
      lt: '小于',
      le: '小于等于',
      contain: '包含',
      notContain: '不包含',
      startWith: '开头是',
      endWith: '结尾是',
      in: '在列表之内',
      notIn: '在列表之外',
      between: '在范围之内',
      notBetween: '在范围之外',
      like: '模糊匹配',
      ilike: '包含',
      match: '正则匹配',
      intersect: '包含任意一个',
      superset: '包含所有',
      subset: '全部属于',
    },
    constraint: {
      column: '字段监控',
      formula: '公式监控',
      sql: 'SQL监控',
    },
    enum: {
      id: '唯一索引',
      selectGeometry: 'select_geometry',
      displayGeometry: 'display_geometry',
      linkGeometry: 'link_geometry',
      name: '名称',
      address: '地址',
      updateTime: '更新时间',
      lng: '经度',
      lat: '纬度',
    },
    unknown: '未知类型',
    systemColumn: '系统列',
  },
  pkgStoreType: {
    table: 'Table',
    view: '数据库视图',
    sql: 'SQL',
    extable: '外链表格',
    collaboration: '协同',
    collaborationChild: '协同数据子包',
    customer: '私有数据',
    mview: '数据库物化视图',
  },
  dataType: {
    org: '机构数据',
    pirvate: '个人数据',
    externalDb: '自定义数据源',
  },
  datapkgOperate: {
    append: '追加',
    appendTip: '在已有数据的最后追加新数据，不可更改数据表结构（字段名称和类型需要与已有数据一模一样）',
    replace: '替换',
    replaceTip: '把已有数据替换为新数据，不可更改数据表结构（字段名称和类型需要与已有数据一模一样）',
    forceReplace: '强制替换',
    forceReplaceTip:
      '把已有数据替换为新数据，可以增加或减少数据包字段，不可更改字段类型。如果需要更改字段类型，请在"我的数据-字段属性"里修改好以后再上传新数据。',
  },
  srs: {
    wgs84: '墨卡托球面坐标系',
    bd09: '墨卡托球面坐标系（BD09）',
    gcj02: '墨卡托球面坐标系（GCJ02）',
  },
  dataset: {
    role: {
      exclusive: '内置数据源',
      public: '公共数据源',
      share: '外链数据源',
      collector: 'Collector',
    },
  },
  resource: {
    project: '项目',
    page: '页面',
    graph: '图表',
    map: '地图',
    datapkg: '数据包',
    market: '数据包',
    customer: '数据包',
    flow: 'Flow',
    script: '脚本',
    scriptJS: 'js脚本',
    scriptCSS: 'css',
    workflowSpec: '流程',
    dataSource: '自定义数据源',
    user: '用户',
  },
  operation: {
    login: '登录',
    loginRedirect: '直接登录',
    impersonate: '模拟登录',
    add: '新增',
    addUser: '新增用户',
    update: '修改',
    updateUsername: '修改用户名称',
    updateEmail: '修改邮箱',
    updateMobile: '修改手机号',
    updatePassword: '修改密码',
    updateExpireTime: '修改过期时间',
    updateStatus: '修改状态',
    delete: '删除',
    deleteUser: '删除用户',
    download: '下载',
    release: '发布',
    execute: '执行',
    updateMeta: '更新元数据',
  },
  operators: {
    empty: '为空',
    notEmpty: '不为空',
    equal: '等于',
    notEqual: '不等于',
    contain: '包含xxx',
    notContain: '不包含xxx',
    startWith: '以xxx开头',
    endWith: '以xxx结尾',
    gt: '大于xxx时间',
    lt: '小于xxx时间',
    get: '大于等于xxx时间',
    let: '小于等于xxx时间',
    eqt: '等于xxx时间',
    ne: '不等于xxx数',
    eq: '等于xxxx数',
    g: '大于xxx数',
    l: '小于xxx数',
    ge: '大于等于xxx数',
    le: '小于等于xxx数',
    in: '限制字符在xxx列表内',
    notIn: '限制字符不在xxx列表内',
    numberIn: '限制数值在xxx列表内',
    numberNotIn: '限制数值不在xxx列表内',
    between: '限制数值在xxx-yyy之间',
    notBetween: '限制数值不在xxx-yyy之间',
  },
  upload: {
    file: '文件',
    uploading: '上传中',
    uploadFailed: '上传失败',
    excelDescTitle: '填写须知：',
    excelDesc1: '请勿修改表格结构，否则会导致上传错误；',
    excelDesc2: '上传数据时请勿删除该"填写须知"单元格；',
    excelDesc3: '表头通过颜色来区分必填和非必填，红色表示必填，篮色表示非必填；',
    excelDesc4: '时间类型字段，表头颜色设置为绿色；',
    required: '*必填',
    idCard: '输入18位身份证号码，例如：110101199001011234',
    phone: '输入11位手机号码，例如：13812345678',
    email: '输入邮箱地址，例如：<EMAIL>',
    url: '输入URL地址，例如：https://www.example.com',
    integer: '输入整数，例如：100',
    decimal: '输入小数，例如：100.5',
    time: '输入时间，例如：14:30:00，或者excel支持的时间格式',
    datetime: '输入日期时间，例如：2023-01-01 14:30:00，或者excel支持的时间格式',
    week: '输入周，例如：2023-1周，或者excel支持的日期格式',
    month: '输入月份，例如：2023-01，或者excel支持的日期格式',
    year: '输入年份，例如：2023，或者excel支持的日期格式',
    quarter: '输入季度，例如：2023-Q1，或者excel支持的日期格式',
    date: '输入日期，例如：2023-01-01，或者excel支持的日期格式',
    radio: '输入选项文本，例如：选项1',
    checkboxGroup: '输入选项文本(多个用逗号分隔)，例如：选项1,选项2',
    boolean: '输入TRUE或FALSE',
    userIdArray: '输入联系人id，例如：[1]',
    userWithOrgArray: '输入携带联系人的组织和用户id，例如：[{orgId:1,userId:1}]',
    inputTextarea: '输入多行文本',
    defaultText: '输入文本，例如：张三',
    geometry_input: '输入地理坐标数据，例如：{"type":"Point","coordinates":[120.1,30.2]}',
    rate: '输入评分整数数字',
    arrayCards: '输入JSON格式的数组数据',
    arrayTable: '输入JSON格式的表格数据',
    cascader: '多级选项题使用【/】或者【,】进行分割，例如：选项1/选项1-1/选项1-1-1 或者 选项1,选项1-1,选项1-1-1',
    upload: '使用正确的文件格式上传，例如：[{"id": "xx", "type": "image", value: "xxxx", name: "example.png"}]',
  },
  markdown: {
    imgLoading: '图片加载中',
    imgLoadFailed: '图片加载失败',
  },
  allType: '全部类型',
};

export type Locale = typeof cn;
