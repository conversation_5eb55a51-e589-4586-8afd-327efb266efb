import dayjs from 'dayjs';
import { cn as bsCommCn } from '@mdtBsComm/languages';
import { en as bsComponentsEn } from '@mdtBsComponents/languages';
import { Locale } from './cn';

export const en: Locale = {
  ...bsCommCn,
  ...bsComponentsEn,
  permissions: {
    export_chart_data: 'Export chart data',
    share_datapkg_cross_app: 'Apply to share data across organizations',
    public_datainfo_doc_add: 'Create public data description template',
    other_general: 'Other general modules',
    update_mdt_user: 'Update MDT user',
    pkg_archive: 'Edit data description template',
    modify_mobile_page: 'Edit mobile material',
    app_pkg_datainfo_doc_button_show:
      'Show the data governance explanation application viewing button for the exclusive theme library',
    am_menu_appsetting_manage: 'Organization preference menu',
    apply_for_impersonate: 'Apply for temporary impersonate login',
    personal_data: 'My data (old version)',
    upload_pkg_by_data_or_dblink: 'Upload data',
    update_pkg_album_approve: 'Distribute organization data update rights',
    mobile_work_space: 'Mobile materials',
    share_project_cross_app: 'Share project across APP',
    create_role_tag: 'Create role tag',
    get_user_roles: 'Get user roles',
    app_manage_center: 'Multi-organization management menu',
    delete_app: 'Delete App',
    pkg_album_tag: 'Edit tag group',
    update_app: 'Edit APP',
    appdata_share: 'Manage data sharing organizations',
    build_personalized_login: 'Personalized login management',
    create_project_from_code: 'Create project using copy code',
    enter_backend: 'Backend multi-organization management (old version)',
    user_history_menu: 'User operation log menu',
    manage_app_role: 'Manage organization roles',
    authorize_permission: 'Grant permission',
    dm_menu_meta_manage: 'Metadata template management menu',
    public_archive_approve: 'Public data governance explanation application approval rights',
    download_pkg_album_approve: 'Approve data download application',
    impersonate_any_app: 'Impersonate any app',
    process_engine: 'Process engine menu',
    cross_app_manage: 'Manage cross-organization sharing',
    custom_login_page: 'Custom login page',
    list_my_app_tasks: 'View all tasks of this APP',
    preset_role_management: 'General role management menu',
    am_menu_user_role_manage: 'User role management menu',
    impersonate_management: 'Impersonate login management',
    update_all_public_pkg: 'Can operate all data packages in the public data market',
    create_sensitive_fdw: 'Link to sensitive database',
    bg_menu_app_role: 'Organization role management menu',
    create_preference_spec: 'Add preference definition',
    update_preference_spec: 'Update preference definition',
    public_pkg_meta_edit: 'Edit metadata of public data package',
    delete_dblink: 'Delete database link',
    dm_menu_tag_manage: 'Tag group management',
    update_public_pkg_album_approve: 'Approve public theme library edit application',
    add_login_page: 'Configure personalized login page',
    get_impersonate_record: 'Get impersonate login records',
    group_menu: 'Work group',
    create_user_tag: 'Create user tag',
    delete_published_pkg: 'Delete published data package',
    ds_product: 'Datlas menu',
    delete_pkg_album: 'Delete theme library',
    update_custom_package: 'Edit personal data package permissions',
    view_app_all_task: 'Manage APP tasks',
    ds_sensitive_operation: 'Sensitive operations of light map applications',
    add_pkg_album: 'Create theme library',
    update_pkg_album: 'Edit theme library',
    app_pkg_meta_edit: 'Manage organization data package metadata',
    batch_update_user: 'Batch update users',
    map_v2: 'Map V2.0',
    am_menu_authority_manage: 'Authorization management menu',
    new_datamap: 'Experience the new version',
    update_preference_identifier: 'Update preference name (information)',
    authorize_pkg_to_anyone: 'Authorize public package',
    task_center: 'Task center',
    dm_product: 'Data market menu',
    download_pkg: 'Download private data',
    qe_chart: 'QE chart',
    public_pkg_album_management: 'Public theme library resource management',
    archive_approve: 'Data governance explanation application approval rights',
    audit: 'Audit log',
    pkg_creator: 'Data package creator view',
    pkg_album_approve: 'Approve data usage application',
    delete_mdt_user: 'Delete MDT user',
    download_public_pkg_album_approve: 'Public data download application approval rights',
    public_tag_del: 'Delete public tag',
    subscription: 'Subscription',
    get_mdt_user: 'Get MDT user',
    data_manage_center: 'Data management center',
    am_user_tag_edit: 'Edit user tag',
    public_pkg_creator_view: 'Public data package creator view',
    delete_role: 'Delete role',
    ailab_v2: 'AILAB2.0',
    delete_user_tag: 'Delete user tag',
    manage_app_all_user: 'Edit user permissions',
    df_product: 'Data factory menu',
    maintenance_stats: 'Operations and maintenance statistics menu',
    create_role: 'Create role',
    create_permission: 'Create permission',
    share_map_poster: 'Share map poster',
    update_app_preference: 'Organization preference operation',
    data_management_menu: 'Organization data menu',
    dm_menu_datasearch: 'Data search menu',
    delete_permission_tag: 'Delete permission tag',
    datainfo_doc_del: 'Delete data description template',
    script: 'Script',
    app_pkg_use_button_show: 'Apply for data usage',
    default_page_batch_management: 'Set organization homepage big screen',
    resource_share: 'Resource sharing',
    user_contact_info: 'View user contact information',
    datalake: 'DataLake',
    upload_and_update_pkg: 'Upload processing',
    manage_dblink: 'Custom data source',
    echarts: 'Echarts',
    public_pkg_info_management: 'Manage public data center data packet information',
    publish_to_pkg_album: 'Apply to upload data package to theme library',
    send_email: 'Send verification email',
    public_pkg_download_approve: 'Public data package download approval',
    work_space_distribute_data: 'Workspace distribution log',
    dm_menu_doctemplate_manage: 'Datapkg document',
    public_pkg_download_button_show: 'Show public theme library data application download button',
    delete_sys_tag: 'Delete system tag',
    ds_old: 'Old version features of light map applications',
    global_role_management: 'General role management',
    create_fdw: 'Link to database',
    am_menu_user_manage: 'User management menu',
    sql_chart: 'SQL chart',
    get_login_page: 'Get personalized login page',
    public_pkg_read_approve: 'Public data package usage approval',
    public_pkg_datainfo_doc_button_show:
      'Show public theme library data governance explanation application viewing button',
    public_datainfo_doc_del: 'Delete public data description template',
    user_management_menu: 'User management',
    pre_pkg_create: 'Create factory data package',
    public_pkg_archive: 'Edit public data description template',
    dm_public_pkg_publish_approve: 'Data package listing application approval rights for public theme library',
    app_pkg_download_button_show: 'Apply for data download',
    dm_menu_album_manage: 'Theme library management',
    asset_statistics_menu: 'Matadata management menu',
    local_market_pkg_approve: 'Data approval menu',
    update_user: 'Edit user',
    back_to_work_space: 'Return to system button',
    query_app_admin: 'Query APP administrator',
    public_market: 'Data center',
    permission_management: 'Function permission management menu',
    reset_pwd: 'Reset organization user password',
    am_menu_user_authority_manage: 'User authority management menu',
    backend_user_management: 'Backend user management',
    pkg_retrieve_approve: 'Approve data view application',
    ds_colpkg_create: 'Initiate data package collaboration',
    pd_menu_personaldata: 'Personal data menu',
    data_report: 'Data report dashboard',
    public_pkg_album_approve: 'Public data usage application approval rights',
    sys_setup_menu: 'Organization style',
    delete_local_tag: 'Delete local tag',
    create_tag: 'Temporarily create tag permissions, delete after the new version is released',
    publish_pkg: 'Data publishing',
    change_permission: 'Manage function permissions',
    add_customized_base_map: 'Add custom base map',
    pkg_editor_invite: 'Invite data package editor',
    pkg_update_approve: 'Distribute organization data update rights',
    public_pkg_use_button_show: 'Show public theme library data application usage button',
    pkg_album_management: 'Get permission for all theme library resources of the app',
    bg_product: 'Backend management menu',
    tag_del: 'Delete tag group',
    create_collector_user: 'Create Collector user while creating Datlas user',
    create_user: 'Create user',
    nopwd_signin: 'Password-free login management',
    return_home: 'Return to system',
    data_market_app_management_menu: 'Data market APP management_admin',
    manage_preset_role: 'Manage general roles',
    low_code_etl: 'Low-code ETL menu',
    update_marker: 'Update custom map icons',
    delete_custom_package: 'Delete personal data package permissions',
    delete_user: 'Delete user',
    data_market_user_management_menu: 'Data market user management_admin',
    material: 'Material',
    data_approval: 'Public data package approval',
    get_others_resource_permission_info: "Query other people's resource permission information",
    public_album_distribute: 'Distribute public theme library to app',
    edit_work_space: 'Edit big screen material',
    ailab: 'Lab',
    app_administrator_permissions: 'Administrator permission configuration',
    app_pkg_unload: 'Unload organization data',
    manage_managed_app: 'Configure manageable APP',
    work_space_manage_center: 'Workspace management center',
    publish_to_private_data_market: 'Apply to publish personal data',
    dm_ds_switch: 'DS_Mutual jump permissions',
    pkg_album_management_menu: 'Theme library menu permissions',
    dm_datadashboard_menu: 'Data dashboard menu',
    role_management: 'Manage organization roles',
    create_dataset: 'Create external connected database',
    work_space: 'Page menu',
    delete_permission: 'Delete permission',
    customized_page: 'Custom big screen',
    standard_page: 'Standard big screen',
    map: 'Map',
    app_impersonate_link: 'Internal organization impersonate login',
    am_product: 'Organization management menu',
    update_own_public_pkg: 'Can only operate data packages uploaded by yourself',
    update_pkg_by_etl: 'Low-code ETL',
    query_all_app: 'Query all App',
    datainfo_doc_add: 'Create data description template',
    cm_product: 'CollectorManager module jump',
    vault_link_management: 'Big screen external link publishing',
    bg_impersonate: 'Backend impersonate login',
    unauthorize_permission: 'Revoke authorization',
    manage_app_all_role: 'Edit roles',
    manage_datlas: 'Datlas management',
    dm_pkg_publish_approve: 'Approve data publish application',
    am_menu_user_tag_manage: 'User tag management menu',
    temp_pkg_to_public: 'Transition_Upload data to the public data center permission',
    delete_public_pkg_album: 'Delete public theme library',
    public_pkg_retrieve_approve: 'Public data retrieval application approval rights',
    am_menu_sensitive_operation_manage: 'Sensitive operation menu',
    app_mangement: 'App management',
    dm_app_pkg_publish_approve: 'Data package listing application approval rights for the exclusive theme library',
    create_permission_tag: 'Create permission tag',
    create_contact: 'Create contact',
    add_mdt_user: 'Create MDT user',
    manage_subscription: 'Manage APP subscription',
    app_pkg_retrieve_button_show: 'Apply for data view',
    public_pkg_unload: 'Unload public data package',
    run_etl: 'Run ETL',
    am_album_distribute: 'Theme library distribution',
    cron_job_management: 'Manage cron jobs',
    work_space_distribute: 'Workspace distribution',
    public_pkg_view_detail_approve: 'Public data package view detail approval',
    query_pkg_album_approve: 'Approve exclusive theme library retrieval application',
    chart: 'Chart',
    add_public_pkg_album: 'Create public theme library',
    delete_pkg_album_tag_of_pkg: 'Delete theme library tags of the data package',
    add_pkg_album_tag_for_pkg: 'Add theme library tags to the data package',
    template_factory: 'Template library',
    private_market: 'Exclusive data center',
    delete_preference_spec: 'Delete preference definition',
    material_v2: 'Material 2.0',
    user_history_download: 'Export user operation log',
    create_client: 'Create client',
    create_preference_identifier: 'Add preference name',
    query_public_pkg_album_approve: 'Approve public theme library retrieval application',
    md_product: 'My data',
    public_pkg_album_tag: 'Edit public tags',
    data_update_audit: 'Data update log',
    ot_product: 'One Table Pass',
    impersonate: 'Impersonate login',
    public_tag_add: 'Create public tag',
    app_pkg_create: 'Create organization data package',
    user_mangement: 'User management (admin)',
    ot_table_management: 'Report management',
    pkg_owner_transfer: 'Transfer data package owner',
    public_pkg_retrieve_button_show: 'Show public theme library data application retrieval button',
    delete_preference_identifier: 'Delete preference name',
    update_login_page: 'Update personalized login page',
    fuzzy_search_all_entity: 'Fuzzy search all entities',
    tag_add: 'Create tag',
    data_market_management_menu: 'Data market management menu permissions',
    manage_invite_code: 'Registration invitation code management',
    create_app: 'Create App',
    delete_role_tag: 'Delete role tag',
    dm_temporary_upload_2_private_market: 'Directly create organization data',
    delete_login_page: 'Delete personalized login page',
    update_public_pkg_album: 'Edit public theme library',
    publish_to_data_market: 'Release Public Market Package',
  },
  proComm: {
    wfAutoStore: {
      name: 'Name',
      app: 'App',
      createTime: 'Create Time',
    },
    wfTmpl: {
      initiatorName: 'Initiator name',
      initiatorApp: 'Initiator app',
      createTime: 'Create time',
      specName: 'Workflow name',
      initiatorId: 'Initiator id',
      initiatorName2: 'Initiator name',
      initiatorAppId: 'Initiator organization id',
      initiatorAppName: 'Initiator organization name',
      specId: 'Spec id',
      workflowId: 'Workflow id',
      initiateTime: 'Initiate time',
      currentUserID: 'Current user id',
      currentUserName: 'Current user name',
      currentApp: 'Current app',
      currentAppName: 'Current app name',
      taskId: 'Task id',
      taskSpecName: 'Task spec name',
      taskSpecId: 'Task spec id',
      workflowStatus: 'Workflow status',
      wfApprovalResult: 'Workflow approval result',
      wfCreateTimeStr: 'Workflow create time',
      wfCreateDateStr: 'Workflow create date',
      parentWorkflowId: 'Workflow parent id',
      parentTaskId: 'Task parent id',
      currentTimestampMs: 'Current timestamp(ms)',
      currentTimestamp: 'Current timestamp',
      currentDateTimeStr: 'Current datetime',
      currentDateStr: 'Current date',
      currentTimeStr: 'Current time',
      startof: 'Start of',
      start: 'Start',
      with: 'With',
    },
    wfSpecType: {
      pkg: 'Datapkg Form',
      oneTable: 'One Table',
    },
  },
  // product
  product: {
    common: 'Metro Common',
    dataFactory: 'Data Factory',
    dataMarket: 'Data Market',
    oneTable: 'One Table',
    myData: 'My Data',
    orgAdmin: 'Organization',
    resourceShare: 'Resource',
    workflow: 'Workflow',
    datlas: 'Data App',
    form: 'Form Design',
  },
  // api
  api: {
    0: 'Network timeout',
    400: 'Bad Request',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout',
  },
  apiNotify: {
    errorTitle: 'API request error list',
    copyErrorDesc: 'After copying the error information, please send it to the feedback group. Thank you.',
    copyError: 'Copy error information',
    failedApi: (count: number) => `There are currently ${count} failed requests`,
    failedApiDesc:
      'If you cannot solve the problem, you can click the "Feedback" button to copy the error request and send it to the development personnel.',
  },
  rc: {
    1011: 'Account does not exist or password error',
    1012: 'Account expired',
    1013: 'Phone is unregistered',
    1014: 'Verification code request too frequent',
    1015: 'Verification code error or expired',
    1016: 'WeChat verification error',
    1017: 'WeChat verification code error or failed',
    1018: 'Illegal WeChat login',
    1020: 'WeChat account registered',
    1026: 'Verification code sent failed, invalid phone number',
    1050: [
      'The {{times}} time login failed, please login in again wait for {{timedelta}}',
      {
        times: 0,
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1051: [
      'User is locked, login in after {{timedelta}}',
      {
        timedelta: (value = 0) => {
          return dayjs.duration(value, 's').humanize();
        },
      },
    ],
    1077: 'Verification Code Error',
    3002: 'WeChat API error',
    3003: 'Known Ali cloud message authentication code interface error',
    3004: 'Unknown Ali cloud message authentication code interface error',
  },
  comText: {
    total: 'Total:',
    create: 'Create ',
    update: 'Update ',
    name: ' Name',
    updateTime: 'Update time',
    copyLink: 'Copy url',
    yes: 'Yes',
    no: 'No',
    filter: 'Filter',
    lastOneMonth: 'Last month',
    lastThreeMonth: 'Last three month',
    lastSixMonth: 'Last six month',
    selectAll: 'All',
    open: 'Unfold',
    close: 'Fold',
    tag: 'Tag',
    selectDatapkg: 'Select datapkg',
    select: 'Select',
    project: 'Project',
    contact: 'Contact',
    orgUser: 'Orginazition user',
    none: 'None',
    none2: '-',
    more: 'More',
    operation: 'Operation',
    showFullInfo: 'Show full info',
    end: 'End',
    upload: 'Upload',
    download: 'Download',
    template: 'Template',
    uploadTemp: 'Upload template',
    sendDownMode: 'Send down mode',
    batchOpt: 'Batch operate',
    replace: 'Replace',
    append: 'Append',
    cancel: 'Cancel',
    copy: 'Copy',
    remove: 'Remove',
    equal: 'Equal',
    notEqual: 'Not equal',
    ge: 'Greater than',
    gt: 'Greater than or equal to',
    le: 'Less than or equal to',
    lt: 'Less than',
    default: 'Default',
    custom: 'Custom',
    user: 'User',
    role: 'Role',
    org: 'Orginazition',
    orgLeader: 'Org leader',
    orgOnetableAdmin: 'Org onetable admin',
    group: 'Group',
    add: 'Add',
    durationDate: 'Duration Date',
    durationTime: 'Duration Time',
    year: 'Year',
    month: 'Month',
    day: 'Day',
    hour: 'Hour',
    minute: 'Minute',
    second: 'Second',
    xmlParseError: 'xml parse error',
    datapkg: 'Datapkg',
    noPermission: 'No permission',
    other: 'Other',
    periodic: 'Periodic',
    approval: 'Approval',
  },
  comNum: {
    one: 'One',
    two: 'Two',
    three: 'Three',
    four: 'Four',
    five: 'Five',
    six: 'Six',
    seven: 'Seven',
    eight: 'Eight',
    nine: 'Nine',
    ten: 'Ten',
  },
  comWarning: {
    noEmpty: 'can not empty',
    del: (name?: string) => `Are you sure to delete ${name}?`,
    fileSize: (mVal: number) => `Single file support up to ${mVal}M`,
    zipFileSize: (mVal: number) => `Zip file support up to ${mVal}M`,
    fileType: (type: string) => `Not supported type[${type}]`,
  },
  comTip: {
    updateSuccess: 'Update Success',
    createSuccess: 'Create Success',
    addSuccess: 'Add Success',
    deleteSuccess: 'Delete Success',
    copySuccess: 'Copy Success',
    publishSuccess: 'Publish Success',
    submitSuccess: 'Submit Success',
    optSuccess: 'Operation Success',
    sendSuccess: 'Send Success',
    saveSuccess: 'Save Success',
    unnamed: 'Unnamed',
    nameEmptyError: 'Name connot empty',
    formNoChanged: 'Form has no changed and never to save',
    primaryEmpty: 'Primary user cannot be empty',
  },
  dataActionStatus: {
    insert: 'Insert',
    update: 'Updated',
    delete: 'Deleted',
    noDelete: 'Not Deleted',
    null: 'Todo',
    synced: 'synced',
    unsynce: 'unsynce',
    submit: 'Submited',
    unsubmit: 'Unsubmit',
    rejected: 'Rejected',
    reject: 'Reject',
    approved: 'Approved',
    approve: 'Approve',
    empty: 'Empty',
  },
  dataStateLabel: {
    unsubmitted: 'Unsubmitted',
    submitted: 'Submitted',
    approved: 'Approved',
    rejected: 'Rejected',
    refused: 'Refused',
    cancelled: 'Cancelled',
    submitted2: 'Approve',
    submitted4: 'Submit & Waiting',
    unsubmitted2: 'Waiting data collect',
    unsubmitted3: 'Submitted',
    fallback: 'Defaul',
    rootNotSentDown: 'Not sent down',
    unapproved: 'Approve',
    collaborate: 'Collaborate',
  },
  taskStatusLabel: {
    unsubmitted: 'Need you submit',
    unapproved: 'Need you approve',
    unapproved2: 'Need you approve',
    rejected: 'Submit had rejected',
  },
  flowStatusLabel: {
    submit: 'Submit task',
    unsubmit: 'Cancel submit',
    approve: 'The submission is approved',
    reject: 'The submission was rejected by review',
    approved: 'Submitted after review',
    rejectd: 'Review the rejection of the submission',
    taskUser: 'Task user',
    approveUser: 'Approve user',
    editCollaborate: 'Modify Collaborate',
    afterEdit: 'After',
    newAdd: 'New add',
    submitData: 'Submit data',
    submitData2: 'Sync data',
    startUser: 'Start user',
    endUser: 'End user',
    issuedUser: 'Issue user',
    mainUser: 'Main user',
    mainUser2: 'Independent leader',
    mainUser3: 'Co-lead',
    refourceUser: 'Refource user',
    submintUser: 'Submit user',
    submintUser2: 'Sync user',
    unsubmitUser: 'Reback user',
    reason: 'Reason',
  },
  comConfirm: {
    finish: 'Are you sure to finish?',
    submit: 'Are you sure to submit?',
  },
  comDataType: {
    int: 'Int',
    float: 'Float',
    str: 'String',
    bool: 'Bool',
    date: 'Date',
    datetime: 'Datetime',
    intArray: 'Int Array',
    strArray: 'String Array',
    json: 'JSON',
    timeStamp: 'Timestamp(Precise to second)',
    timeStampMS: 'Timestamp(Precise to millisecond)',
  },
  permission: {
    read: 'Read',
    read2: 'Read',
    viewDetail: 'View',
    download: 'Download',
    update: 'Update',
    auth: {
      read: 'Read',
      update: 'Edit',
      viewDetail: 'Search',
      download: 'Download',
      pkgUpdate: 'Package update',
      metaUpdate: 'Metadata edit',
      execute: 'Execute',
      look: 'Read',
      publish: 'Use',
    },
  },
  geo: {
    point: 'Point',
    line: 'Line',
    polygon: 'Polygon',
    plain: 'Plain',
    pointToPoint: 'Point To Point',
    pointToLine: 'Point To Line',
    pointToPolygon: 'Point To Polygon',
    lineToPoint: 'Line To Point',
    lineToLine: 'Line To Line',
    lineToPolygon: 'Line To Polygon',
    polygonToPoint: 'Polygon To Point',
    polygonToLine: 'Polygon To Line',
    polygonToPolygon: 'Polygon To Polygon',
    点数据: 'Point',
    线数据: 'Line',
    面数据: 'Polygon',
    非地理数据: 'Plain',
    '点->点数据': 'Point To Point',
    '点->线数据': 'Point To Line',
    '点->面数据': 'Point To Polygon',
    '线->点数据': 'Line To Point',
    '线->线数据': 'Line To Line',
    '线->面数据': 'Line To Polygon',
    '面->点数据': 'Polygon To Point',
    '面->线数据': 'Polygon To Line',
    '面->面数据': 'Polygon To Polygon',
  },
  column: {
    type: {
      int: 'Numeric (Integer)',
      bitInt: 'Numeric (Long integer)',
      numberIc: 'Numeric',
      float: 'Numeric (Decimal)',
      str: 'Text',
      bool: 'Boolean',
      date: 'Date',
      time: 'Time',
      dateTime: 'Date and time',
      timeStemp: 'Timestamp',
      geometry: 'Geographic',
      json: 'Object',
      jpg: 'Image',
      image: 'Image',
      mediaJson: 'File',
      array: 'Array',
      arrayStr: 'Text Array',
      arrayInt: 'Int Array',
      arrayFloat: 'Float Array',
      bigint: 'Big Int',
      bigfloat: 'Big Float',
      userId: 'UserID(Integer）',
      orgId: 'OrgID(Integer）',
      groupId: 'GroupID(Integer）',
      roleId: 'RoleID(Integer）',
      generalRoleId: 'GeneralRoleID(Integer）',
    },
    operator: {
      empty: 'Empty',
      notEmpty: 'NotEmpty',
      eq: 'Equal',
      ne: 'NotEqual',
      equal: 'Equal',
      notEqual: 'NotEqual',
      is: 'Empty',
      in: 'In',
      gt: 'GreaterThan',
      ge: 'GreaterThanOrEqual',
      lt: 'LessThan',
      le: 'LessThanOrEqual',
      contain: 'Contain',
      notContain: 'NotContain',
      startWith: 'StartWith',
      endWith: 'EndWith',
      notIn: 'NotIn',
      between: 'Between',
      notBetween: 'NotBetween',
      like: 'Like',
      ilike: 'Ilike',
      match: 'Match',
      intersect: 'Intersect',
      superset: 'Superset',
      subset: 'Subset',
    },
    constraint: {
      column: 'Column monitoring',
      formula: 'Formula monitoring',
      sql: 'SQL monitoring',
    },
    enum: {
      id: 'Unique index',
      selectGeometry: 'select_geometry',
      displayGeometry: 'display_geometry',
      linkGeometry: 'link_geometry',
      name: 'Name',
      address: 'Address',
      updateTime: 'Update time',
      lng: 'Longitude',
      lat: 'Latitude',
    },
    unknown: 'Unknown type',
    systemColumn: 'System',
  },
  pkgStoreType: {
    table: 'Table',
    view: 'View',
    sql: 'SQL',
    extable: 'External table',
    collaboration: 'Collaboration',
    collaborationChild: 'Collaboration data sub-package',
    customer: 'Customer',
    mview: 'Database materialized view',
  },
  dataType: {
    org: 'App Data',
    pirvate: 'Private Data',
    externalDb: 'External DB',
  },
  datapkgOperate: {
    append: 'Append',
    appendTip:
      'Append new data to the end of existing data, cannot change the data table structure (field names and types must be exactly the same as existing data)',
    replace: 'Replace',
    replaceTip:
      'Replace existing data with new data, cannot change the data table structure (field names and types must be exactly the same as existing data)',
    forceReplace: 'Force replace',
    forceReplaceTip:
      'Replace existing data with new data, can add or remove data package fields, cannot change field types. If you need to change field types, please modify them in "My Data-Field Properties" before uploading new data.',
  },
  srs: {
    wgs84: 'Mercator Spherical Coordinate System',
    bd09: 'Mercator Spherical Coordinate System (BD09)',
    gcj02: 'Mercator Spherical Coordinate System (GCJ02)',
  },
  dataset: {
    role: {
      exclusive: 'Built-in data source',
      public: 'Public data source',
      share: 'Share data source',
      collector: 'Collector',
    },
  },
  resource: {
    project: 'Project',
    page: 'Page',
    graph: 'Chart',
    map: 'Map',
    datapkg: 'Data package',
    market: 'Market',
    customer: 'Customer',
    flow: 'Flow',
    script: 'Script',
    scriptJS: 'JS script',
    scriptCSS: 'CSS',
    workflowSpec: 'Workflow',
    dataSource: 'External DB',
    user: 'User',
  },
  operation: {
    login: 'Login',
    loginRedirect: 'Login Redirect',
    impersonate: 'Impersonate',
    add: 'Add',
    addUser: 'Add User',
    update: 'Update',
    updateUsername: 'Update username',
    updateEmail: 'Update email',
    updateMobile: 'Update mobile',
    updatePassword: 'Update password',
    updateExpireTime: 'Update expire time',
    updateStatus: 'Update status',
    delete: 'Delete',
    deleteUser: 'Delete User',
    download: 'Download',
    release: 'Release',
    execute: 'Execute',
    updateMeta: 'Update meta',
  },
  operators: {
    empty: 'Empty',
    notEmpty: 'Not empty',
    equal: 'Equal',
    notEqual: 'Not equal',
    contain: 'Contains xxx',
    notContain: 'Does not contain xxx',
    startWith: 'Starting with xxx',
    endWith: 'Ending with xxx',
    gt: 'Greater than xxx time',
    lt: 'Less than xxx time',
    get: 'Greater than or equal to xxx time',
    let: 'Less than or equal to xxx time',
    eqt: 'Equal to xxx time',
    ne: 'Not equal to xxx number',
    eq: 'Equal to xxxx number',
    g: 'Greater than xxx number',
    l: 'Less than xxx number',
    ge: 'Greater than or equal to xxx number',
    le: 'Less than or equal to xxx number',
    in: 'Limit characters to be within the xxx list',
    notIn: 'Limit characters that are not in the xxx list',
    numberIn: 'Limit numbers to be within the xxx list',
    numberNotIn: 'Limit numbers that are not in the xxx list',
    between: 'Limit values within the range of xxx yyy',
    notBetween: 'Limit values without the range of xxx yyy',
  },
  upload: {
    file: 'File',
    uploading: 'Uploading',
    uploadFailed: 'Upload Failed',
    excelDescTitle: 'Fill description:',
    excelDesc1: 'Please do not modify the table structure, otherwise it will cause an upload error;',
    excelDesc2: 'Please do not delete the "Fill in the description" cell when uploading data;',
    excelDesc3:
      'The header is distinguished by color between required and non-required, red for required, blue for non-required;',
    excelDesc4: 'The header color of the time type field is set to green;',
    required: '*Required',
    idCard: 'Enter 18-digit ID number, e.g.: 110101199001011234',
    phone: 'Enter 11-digit phone number, e.g.: 13812345678',
    email: 'Enter email address, e.g.: <EMAIL>',
    url: 'Enter URL address, e.g.: https://www.example.com',
    integer: 'Enter integer, e.g.: 100',
    decimal: 'Enter decimal, e.g.: 100.5',
    time: 'Enter time, e.g.: 14:30:00, or Excel supported time format',
    datetime: 'Enter datetime, e.g.: 2023-01-01 14:30:00, or Excel supported time format',
    week: 'Enter week, e.g.: 2023-W1, or Excel supported date format',
    month: 'Enter month, e.g.: 2023-01, or Excel supported date format',
    year: 'Enter year, e.g.: 2023, or Excel supported date format',
    quarter: 'Enter quarter, e.g.: 2023-Q1, or Excel supported date format',
    date: 'Enter date, e.g.: 2023-01-01, or Excel supported date format',
    radio: 'Enter option text, e.g.: Option1',
    checkboxGroup: 'Enter option text (separate multiple with commas), e.g.: Option1,Option2',
    boolean: 'Enter TRUE or FALSE',
    userIdArray: 'Enter contact ID, e.g.: [1]',
    userWithOrgArray: 'Enter organization and user ID with contact, e.g.: [{orgId:1,userId:1}]',
    inputTextarea: 'Enter multiline text',
    defaultText: 'Enter text, e.g.: John',
    geometry_input: 'Enter geographic coordinate data, e.g.: {"type":"Point","coordinates":[120.1,30.2]}',
    rate: 'Enter rating integer number',
    arrayCards: 'Enter array data in JSON format',
    arrayTable: 'Enter table data in JSON format',
    cascader:
      'Use 【/】 or 【,】 to separate multi-level options, e.g.: First category/Second subcategory/Third sub-category. or First category,Second subcategory,Third sub-category',
    upload:
      'Use the correct file format to upload, e.g.: [{"id": "xx", "type": "image", value: "xxxx", name: "example.png"}]',
  },
  markdown: {
    imgLoading: 'Image Loading',
    imgLoadFailed: 'Image Load Failed',
  },
  allType: 'All Types',
};
