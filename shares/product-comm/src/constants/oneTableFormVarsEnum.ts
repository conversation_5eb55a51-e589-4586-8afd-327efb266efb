// 一表通所定义的所有表单变量枚举（唯一入口）
export enum ReportInfoAttrEnum {
  NAME = 'name',
  TAGS = 'tags',
  TASK_TYPE = 'taskType',
  REGION = 'region',
  LEVEL = 'level',
  DESC = 'desc',
  FORM_ORG = 'formOrg',
  FREQUENCY = 'frequency',
  RANGE = 'range',
  LEVEL_MUTI = 'levelMuti',
  OWNER = 'owner',
  OWNER_PHONE = 'ownerPhone',
  OWNERSHIP = 'ownership',

  // 流转到step3的变量
  CHECK_DATA_SUBMITTED = 'checkDataSubmitted',
  IS_AUTO_COMPLETE_WORKFLOW_WHEN_DATA_SUBMITTED = 'isAutoCompleteWorkflowWhenDataSubmitted',
  ENABLE_SUBORDINATE_COMPLETE_WORKFLOW = 'enableSubordinateCompleteWorkflow',
}
