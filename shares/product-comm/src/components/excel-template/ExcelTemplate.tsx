import _ from 'lodash';
import { FC } from 'react';
import { Button } from '@metroDesign/button';
import { DropmenuButton, IMenuItemProps } from '@mdtBsComm/components/dropmenu-button';
import { DATE_FORMATTER_2, formateDate, transformDateToUnix } from '@mdtBsComm/utils/dayUtil';
import { MenuInfo } from '@mdtDesign/menu';
import i18n from '../../languages';
import { ExcelResolver } from './ExcelResolver';
import { exportExcel, IExcelColumn, IExportExcelOptions } from './exportExcel';
import './index.less';

export type IExcelTemplateProps = Omit<IExportExcelOptions, 'columns'> & {
  className?: string;
  onChange?: (values: any) => void;
  columns: IExcelColumn[];
};

export const ExcelTemplate: FC<IExcelTemplateProps> = (props) => {
  const { onChange, className, ...exportProps } = props;
  const resolver = new ExcelResolver({});

  const handleUpload = (files: File[]) => {
    resolver.handleExcel(files).subscribe((vals) => {
      onChange?.(transformUploadData(vals, exportProps.columns));
    });
  };

  const handleMenuClick = (menu: MenuInfo) => {
    if (menu.key === 'export') {
      if (_.isEmpty(exportProps.columns)) return;
      exportExcel(exportProps);
    }
  };

  const menus: IMenuItemProps[] = [
    {
      title: i18n.chain.comText.download,
      key: 'export',
      divider: true,
    },
    {
      title: i18n.chain.comText.upload,
      type: 'upload',
      key: 'upload',
      uploadProps: {
        multiple: false,
        accept: '.xls,.xlsx',
        onDropAccepted: handleUpload,
      },
    },
  ];

  const cls = `metro-excel-template-btn ${className || ''}`;
  return (
    <DropmenuButton
      menus={menus}
      noSelected={true}
      buttonLabel=""
      destroyPopupOnHide={false}
      onClickMenu={handleMenuClick}
    >
      <Button className={cls}>{i18n.chain.comText.template}</Button>
    </DropmenuButton>
  );
};

export const transformUploadData = (data: any[], columns: IExcelColumn[]) => {
  const labelToTypeMap: Record<string, string> = {};
  const labelToValueMap = _.reduce(
    columns,
    (prev: Record<string, any>, curr) => {
      prev[curr.label] = curr.value;
      labelToTypeMap[curr.label] = curr.type;
      return prev;
    },
    {},
  );

  const validData: Record<string, any>[] = [];
  _.forEach(data, (it) => {
    const val: Record<string, any> = {};
    _.forEach(it, (v, k) => {
      const nk = labelToValueMap[k];
      nk && (val[nk] = transformValueByType(labelToTypeMap[k], v));
    });
    // 有效数据填充
    !_.isEmpty(val) && validData.push(val);
  });
  return validData;
};

export const transformValueByType = (type: string, val: any) => {
  if (type === 'array' && _.isString(val)) {
    const fval = _.replace(val, /[\[\'\"\]]/gi, '');
    return _.split(fval, /\s*[,，]\s*/);
  }
  // 兼容不合规的数据, 不做数据填充处理
  if (type === 'array' && !_.isArray(val)) {
    return [];
  }
  if (type === 'number' && _.isDate(val)) {
    return transformDateToUnix(val);
  }
  if (type === 'string' && _.isDate(val)) {
    const noTime = _.includes(val.toISOString(), 'T00:00:00.000');
    return formateDate(val, noTime ? undefined : DATE_FORMATTER_2);
  }
  if (type === 'string' && _.isNumber(val)) {
    return _.toString(val);
  }
  return val;
};
