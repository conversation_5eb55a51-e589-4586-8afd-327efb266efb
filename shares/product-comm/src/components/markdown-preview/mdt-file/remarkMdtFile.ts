import { fromMarkdown } from './fromMarkdown';
import { syntax } from './syntax';
let warningIssued = false;

function remarkV13Warning(context: any): boolean {
  if (!warningIssued && (context.Parser?.prototype?.blockTokenizers || context.Compiler?.prototype?.visitors)) {
    warningIssued = true;
    console.warn('[remark-mdt-file] Warning: please upgrade to remark 13 to use this plugin');
  }

  return warningIssued;
}

function remarkMdtFile(this: any) {
  const data = this.data();
  remarkV13Warning(this);

  add('micromarkExtensions', syntax());
  add('fromMarkdownExtensions', fromMarkdown());

  function add(field: string, value: any) {
    if (data[field]) data[field].push(value);
    else data[field] = [value];
  }
}

export { remarkMdtFile };
