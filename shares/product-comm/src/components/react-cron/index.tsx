import { useContext, useEffect, useState } from 'react';
import { But<PERSON> } from '@metroDesign/button';
import { Tabs } from '@metroDesign/tabs';
import { dayRegex, hourRegex, minuteRegex, monthRegex, secondRegex, weekRegex, yearRegex } from './cron-regex';
import DayPane from './DayPane';
import ReactCronContext from './GlobalContext';
import HourPane from './HourPane';
import MinutePane from './MinutePane';
import MonthPane from './MonthPane';
import SecondPane from './SecondPane';
import WeekPane from './WeekPane';
import YearPane from './YearPanel';
import './index.less';

ReactCronContext.displayName = 'react-cron config provider';
const ReactCronContextProvider = ReactCronContext.Provider;

const validValue = (value: string): [boolean, string[]] => {
  let vals = value.split(' ');
  let isAllRight = true;
  vals = [secondRegex, minuteRegex, hourRegex, dayRegex, monthRegex, weekRegex, yearRegex].map((regex, index) => {
    let val = vals[index];
    if (!regex.test(val)) {
      isAllRight = false;
      val = '*';
    }
    return val;
  });
  vals[3] !== '?' && (vals[5] = '?');
  return [isAllRight, vals];
};

// eslint-disable-next-line complexity,sonarjs/cognitive-complexity
function ReactCron(props: any) {
  const { language = {} } = useContext(ReactCronContext);
  const { paneTitle = {}, okBtnText } = language;

  const { onChange, style, value, onOk, footer, getCronFns, panesShow = {}, errorTip } = props;

  // 手动制定后不设置
  let defaultTab = props.defaultTab;
  if (!defaultTab) {
    try {
      let [, [secondVal, minuteValue, hourVal, dayVal, monthVal, weekVal, yearVal]] = validValue(value);
      secondVal = secondRegex.test(secondVal) ? secondVal : '*';
      minuteValue = minuteRegex.test(minuteValue) ? minuteValue : '*';
      hourVal = hourRegex.test(hourVal) ? hourVal : '*';
      dayVal = dayRegex.test(dayVal) ? dayVal : '*';
      monthVal = monthRegex.test(monthVal) ? monthVal : '*';
      weekVal = weekRegex.test(weekVal) ? weekVal : '?';
      weekVal = dayVal !== '?' ? '?' : weekVal;
      yearVal = yearRegex.test(yearVal) ? yearVal : '*';
      if (secondVal !== '?' && panesShow.second !== false) {
        defaultTab = 'second';
      } else if (minuteValue !== '?' && panesShow.minute !== false) {
        defaultTab = 'minute';
      } else if (hourVal !== '?' && panesShow.hour !== false) {
        defaultTab = 'hour';
      } else if (dayVal !== '?' && panesShow.day !== false) {
        defaultTab = 'day';
      } else if (monthVal !== '?' && panesShow.month !== false) {
        defaultTab = 'month';
      } else if (weekVal !== '?' && panesShow.week !== false) {
        defaultTab = 'week';
      } else if (yearVal !== '?' && panesShow.year !== false) {
        defaultTab = 'year';
      }
    } catch (error) {}
  }
  if (!defaultTab) {
    if (panesShow.second !== false) {
      defaultTab = 'second';
    } else if (panesShow.minute !== false) {
      defaultTab = 'minute';
    } else if (panesShow.hour !== false) {
      defaultTab = 'hour';
    } else if (panesShow.day !== false) {
      defaultTab = 'day';
    } else if (panesShow.month !== false) {
      defaultTab = 'month';
    } else if (panesShow.week !== false) {
      defaultTab = 'week';
    } else if (panesShow.year !== false) {
      defaultTab = 'year';
    }
  }

  const [currentTab, setCurrentTab] = useState(defaultTab);
  const [second, setSecond] = useState('*');
  const [minute, setMinute] = useState('*');
  const [hour, setHour] = useState('*');
  const [day, setDay] = useState('*');
  const [month, setMonth] = useState('*');
  const [week, setWeek] = useState('?');
  const [year, setYear] = useState('*');
  const [error, setError] = useState(false);

  // eslint-disable-next-line sonarjs/cognitive-complexity
  const onParse = () => {
    return new Promise((resolve) => {
      if (value) {
        try {
          let [isAllRight, [secondVal, minuteValue, hourVal, dayVal, monthVal, weekVal, yearVal]] = validValue(value);
          setError(!isAllRight);
          secondVal = secondRegex.test(secondVal) ? secondVal : '*';
          minuteValue = minuteRegex.test(minuteValue) ? minuteValue : '*';
          hourVal = hourRegex.test(hourVal) ? hourVal : '*';
          dayVal = dayRegex.test(dayVal) ? dayVal : '*';
          monthVal = monthRegex.test(monthVal) ? monthVal : '*';
          weekVal = weekRegex.test(weekVal) ? weekVal : '?';
          weekVal = dayVal !== '?' ? '?' : weekVal;
          // console.log('yearVal', value.split(" "), yearVal, yearRegex.test(yearVal))
          // return;
          yearVal = yearRegex.test(yearVal) ? yearVal : '*';
          setSecond(secondVal);
          setMinute(minuteValue);
          setHour(hourVal);
          setDay(dayVal);
          setMonth(monthVal);
          setWeek(weekVal);
          setYear(yearVal);
          resolve({ value, secondVal, minuteValue, hourVal, dayVal, monthVal, weekVal, yearVal });
        } catch (error) {
          setSecond('*');
          setMinute('*');
          setHour('*');
          setDay('*');
          setMonth('*');
          setWeek('?');
          setYear('*');
          resolve({});
        }
      } else {
        setSecond('*');
        setMinute('*');
        setHour('*');
        setDay('*');
        setMonth('*');
        setWeek('?');
        setYear('*');
        resolve({});
      }
    });
  };

  const onGenerate = () => {
    if (onOk) {
      onOk([second, minute, hour, day, month, week, year].join(' '));
    }
  };

  const onChangeDay = (v: string) => {
    setDay(v);
    if (v !== '?') {
      setWeek('?');
    }
  };

  const onChangeWeek = (v: string) => {
    setWeek(v);
    if (v !== '?') {
      setDay('?');
    }
  };

  const changeInc = (value: string, changeFn: any, type?: string) => {
    changeFn(value);
    onChange?.({ type, value });
  };

  useEffect(() => {
    getCronFns?.({
      // 设置值
      onParse,
      getValue: () => [second, minute, hour, day, month, week, year].join(' '),
    });
  });

  useEffect(() => {
    onParse();
  }, [value]);

  const items: any[] = [
    panesShow.second !== false && {
      key: 'second',
      label: paneTitle.second || '秒',
      children: <SecondPane value={second} onChange={(val: string) => changeInc(val, setSecond, 'second')} />,
    },
    panesShow.minute !== false && {
      key: 'minute',
      label: paneTitle.minute || '分',
      children: <MinutePane value={minute} onChange={(val: string) => changeInc(val, setMinute, 'minute')} />,
    },
    panesShow.hour !== false && {
      key: 'hour',
      label: paneTitle.hour || '时',
      children: <HourPane value={hour} onChange={(val: string) => changeInc(val, setHour, 'hour')} />,
    },
    panesShow.day !== false && {
      key: 'day',
      label: paneTitle.day || '日',
      children: <DayPane value={day} onChange={(val: string) => changeInc(val, onChangeDay, 'day')} />,
    },
    panesShow.month !== false && {
      key: 'month',
      label: paneTitle.month || '月',
      children: <MonthPane value={month} onChange={(val: string) => changeInc(val, setMonth, 'month')} />,
    },
    panesShow.week !== false && {
      key: 'week',
      label: paneTitle.week || '周',
      children: <WeekPane value={week} onChange={(val: string) => changeInc(val, onChangeWeek, 'week')} />,
    },
    panesShow.year !== false && {
      key: 'year',
      label: paneTitle.year || '年',
      children: <YearPane value={year} onChange={(val: string) => changeInc(val, setYear, 'year')} />,
    },
  ].filter(Boolean);

  return (
    <div className="react-cron" style={style}>
      {errorTip && error ? <span className="error-msg">{errorTip}</span> : null}
      <Tabs activeKey={currentTab} onChange={setCurrentTab} className="tabs" items={items} />
      <div className="footer">
        {footer === false || footer === null || footer ? (
          footer
        ) : (
          <Button type="primary" onClick={onGenerate}>
            {okBtnText || '生成'}
          </Button>
        )}
      </div>
    </div>
  );
}
ReactCron.Provider = ReactCronContextProvider;
export default ReactCron;
