import {
  batchCreateAlbum,
  deleteAlbum,
  IBatchCreateAlbumOptions,
  IDeleteAlbumOptions,
  IQueryAlbumPkgsOptions,
  IQueryAlbumsOptions,
  IQueryAlbumTagsOptions,
  IUpdateAlbumOptions,
  queryAlbumPkgsFirstPage,
  queryAlbumPkgsNextPage,
  queryAlbums,
  queryAlbumTags,
  updateAlbum,
} from '@mdtBsBffServices/services';
import { ProductPrefixEnum } from '../constants';

export class AlbumBffService {
  private static productName: ProductPrefixEnum;

  public static init(product: ProductPrefixEnum) {
    this.productName = product;
  }

  public static queryAlbums(options: IQueryAlbumsOptions) {
    return queryAlbums(this.productName, options);
  }

  public static batchCreateAlbum(options: IBatchCreateAlbumOptions) {
    return batchCreateAlbum(this.productName, options);
  }

  public static updateAlbum(options: IUpdateAlbumOptions) {
    return updateAlbum(this.productName, options);
  }

  public static deleteAlbum(options: IDeleteAlbumOptions) {
    return deleteAlbum(this.productName, options);
  }

  public static queryAlbumPkgsFirstPage(options: IQueryAlbumPkgsOptions) {
    return queryAlbumPkgsFirstPage(this.productName, options);
  }

  public static queryAlbumPkgsNextPage(options: IQueryAlbumPkgsOptions) {
    return queryAlbumPkgsNextPage(this.productName, options);
  }

  public static queryAlbumTags(options: IQueryAlbumTagsOptions) {
    return queryAlbumTags(this.productName, options);
  }
}
