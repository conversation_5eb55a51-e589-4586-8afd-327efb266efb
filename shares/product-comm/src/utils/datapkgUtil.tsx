import _ from 'lodash';
import Icon from '@mdtDesign/icon';
import Tooltip from '@mdtDesign/tooltip';
import {
  ApplyTicketTypeEnum,
  DatapkgGeometryTypeEnum,
  DatapkgPermissionEnum,
  DatapkgStoreTypeEnum,
  DatapkgUpdateType,
  DatasetRoleEnum,
  GeometryFormatEnum,
  KeyColumnEnum,
  OwnershipEnum,
  SrsEnum,
} from '../constants';
import { IDatapkgFeatureFlags, IDatapkgPermission, IOwnership } from '../interfaces';
import i18n from '../languages';

// 获取数据包权限名称
let datapkgPermissionLabelMap: Record<string, string>;
export const getDatapkgPermissionLabel = (permission: string, defaultLabel = '') => {
  return datapkgPermissionLabelMap[permission] || defaultLabel;
};
export const getDatapkgPermissionLabels = (permissions?: string[]) => {
  const labels: string[] = [];
  _.forEach(permissions, (it) => {
    let label = datapkgPermissionLabelMap[it] || '';
    label && labels.push(label);
  });
  return labels;
};

// 获取数据包权限key
export const getDatapkgPermissionNormalKeys = () => {
  return [DatapkgPermissionEnum.READ, DatapkgPermissionEnum.VIEW_DETAIL, DatapkgPermissionEnum.DOWNLOAD];
};

// 数据包数据操作权限
export const checkDatapkgDataPermission = (permissions: IDatapkgPermission[] = []) => {
  return {
    viewDetail: _.includes(permissions, DatapkgPermissionEnum.VIEW_DETAIL),
    update: _.includes(permissions, DatapkgPermissionEnum.UPDATE),
    insert: _.includes(permissions, DatapkgPermissionEnum.INSERT),
    delete: _.includes(permissions, DatapkgPermissionEnum.DELETE),
    download: _.includes(permissions, DatapkgPermissionEnum.DOWNLOAD),
    read: _.includes(permissions, DatapkgPermissionEnum.READ),
  };
};

// 获取可用的双地理类型
export const getMultipleGeometryType = () => {
  return [
    DatapkgGeometryTypeEnum.POINT_TO_POINT,
    DatapkgGeometryTypeEnum.POINT_TO_POLYGON,
    DatapkgGeometryTypeEnum.POINT_TO_LINE,
  ];
};

// 获取可用的单地理类型
export const getSingleGeometryType = () => {
  return [
    DatapkgGeometryTypeEnum.POINT,
    DatapkgGeometryTypeEnum.LINE,
    DatapkgGeometryTypeEnum.POLYGON,
    DatapkgGeometryTypeEnum.PLAIN,
  ];
};

// 是否单地理类型
export const isSingleGeometryType = (type: DatapkgGeometryTypeEnum) => {
  const types = _.reject(getSingleGeometryType(), [DatapkgGeometryTypeEnum.PLAIN]);
  return _.includes(types, type);
};

// 获取地理类型名称
let datapkgGeometryTypeLabelMap: Record<string, string>;
export const getDatapkgGeometryTypeLabel = (type: string) => {
  return datapkgGeometryTypeLabelMap[type] || datapkgGeometryTypeLabelMap[DatapkgGeometryTypeEnum.PLAIN];
};

export const getDatapkgGeometryOptions = () => {
  return _.map(datapkgGeometryTypeLabelMap, (label, value) => ({ label, value }));
};

const transformGeometrys = [
  DatapkgGeometryTypeEnum.POINT,
  DatapkgGeometryTypeEnum.LINE,
  DatapkgGeometryTypeEnum.POLYGON,
  DatapkgGeometryTypeEnum.POINT_TO_POINT,
];
export const getDatapkgTransformGeometryOptions = () => {
  return _.map(transformGeometrys, (key) => ({
    label: datapkgGeometryTypeLabelMap[key],
    value: key,
  }));
};

// 获取数据包地理类型对应的图标
const dup_icon_polygon = 'location-to-polygon';
const datapkgGeometryTypeIconMap: Record<string, string> = {
  [DatapkgGeometryTypeEnum.POINT]: 'location',
  [DatapkgGeometryTypeEnum.LINE]: 'line',
  [DatapkgGeometryTypeEnum.POLYGON]: 'layer',
  [DatapkgGeometryTypeEnum.PLAIN]: 'file',
  [DatapkgGeometryTypeEnum.POINT_TO_POINT]: 'location-to-location',
  [DatapkgGeometryTypeEnum.POINT_TO_LINE]: 'location-to-polygon',
  [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.LINE_TO_POINT]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.LINE_TO_LINE]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.LINE_TO_POLYGON]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POINT]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.POLYGON_TO_LINE]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POLYGON]: dup_icon_polygon,
};
export const getDatapkgGeometryTypeIcon = (type: string) => {
  return datapkgGeometryTypeIconMap[type] || datapkgGeometryTypeIconMap[DatapkgGeometryTypeEnum.PLAIN];
};

const datapkgGeometryTypeBigIconMap: Record<string, string> = {
  [DatapkgGeometryTypeEnum.POINT]: 'file-location',
  [DatapkgGeometryTypeEnum.LINE]: 'file-line',
  [DatapkgGeometryTypeEnum.POLYGON]: 'file-polygon',
  [DatapkgGeometryTypeEnum.PLAIN]: 'file-description',
  [DatapkgGeometryTypeEnum.POINT_TO_POINT]: 'file-location-to-location',
  [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: 'file-location-to-polygon',
};
export const getDatapkgGeometryBigTypeIcon = (type: string) => {
  return datapkgGeometryTypeBigIconMap[type] || datapkgGeometryTypeBigIconMap[DatapkgGeometryTypeEnum.PLAIN];
};

// 获取数据包地理类型对应的颜色
const dup_color_magenta = 'var(--dmc-magenta-500-color)';
const datapkgGeometryTypeColorMap: Record<string, string> = {
  [DatapkgGeometryTypeEnum.POINT]: 'var(--dmc-blue-500-color)',
  [DatapkgGeometryTypeEnum.LINE]: 'var(--dmc-red-500-color)',
  [DatapkgGeometryTypeEnum.POLYGON]: 'var(--dmc-yellow-500-color)',
  [DatapkgGeometryTypeEnum.PLAIN]: 'var(--dmc-green-500-color)',
  [DatapkgGeometryTypeEnum.POINT_TO_POINT]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POINT_TO_LINE]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.LINE_TO_POINT]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.LINE_TO_LINE]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.LINE_TO_POLYGON]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POINT]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POLYGON_TO_LINE]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POLYGON]: dup_color_magenta,
};
export const getDatapkgGeometryTypeColor = (type: string) => {
  return datapkgGeometryTypeColorMap[type] || datapkgGeometryTypeColorMap[DatapkgGeometryTypeEnum.PLAIN];
};

// 获取数据包存储名称
export let datapkgStoreTypeLabelMap: Record<string, string>;
export const getDatapkgStoreTypeLabel = (packageType?: string, defaultLabel = '') => {
  return (packageType && datapkgStoreTypeLabelMap[packageType]) || defaultLabel;
};
export const getDatapkgStoreTypeOptions = () => {
  const list = [DatapkgStoreTypeEnum.TABLE, DatapkgStoreTypeEnum.SQL];
  return _.map(list, (key) => ({
    value: key,
    label: getDatapkgStoreTypeLabel(key),
  }));
};

// 获取数据包存储类型对应的颜色
export const datapkgStoreTypeColorMap: Record<string, string> = {
  [DatapkgStoreTypeEnum.TABLE]: 'blue-700',
  [DatapkgStoreTypeEnum.SQL]: 'purple-700',
  [DatapkgStoreTypeEnum.EXTABLE]: 'blue-900',
  [DatapkgStoreTypeEnum.COLLABORATION]: 'orange-700',
  [DatapkgStoreTypeEnum.COLLABORATION_V2]: 'orange-700',
};
export const getDatapkgStoreTypeColor = (packageType?: string, defaultColor = 'magenta-700') => {
  return (packageType && datapkgStoreTypeColorMap[packageType]) || defaultColor;
};

// 是否是外链数据包
export const isOutLink = (connRole: number) => {
  return connRole === DatasetRoleEnum.OUT_LINK;
};

// 是否是collector数据包
export const isCollectorDatapkg = (connRole: number) => {
  return connRole === DatasetRoleEnum.COLLECTOR;
};

// 是否内置数据源下数据包
export const isInnerDatapkg = (connRole: number) => {
  return !isOutLink(connRole) && !isCollectorDatapkg(connRole);
};

// 数据包是否是外链表(外链数据源，通过表生成的数据包)
export const isOutTableDatapkg = (connRole: number, pkgType: string) => {
  return connRole === DatasetRoleEnum.OUT_LINK && pkgType === DatapkgStoreTypeEnum.TABLE;
};

// 数据包是否是外链SQL(外链数据源，通过sql生成的数据包)
export const isOutSqlDatapkg = (connRole: number, pkgType: string) => {
  return connRole === DatasetRoleEnum.OUT_LINK && pkgType === DatapkgStoreTypeEnum.SQL;
};

const CollaborateKeys = [DatapkgStoreTypeEnum.COLLABORATION, DatapkgStoreTypeEnum.COLLABORATION_V2];

// 是否是协同数据包
export const isCollaborateDatapkg = (pkgType: string, feature_flags: IDatapkgFeatureFlags) => {
  return _.includes(CollaborateKeys, pkgType) || feature_flags.row_level_permission;
};

// 是否私有数据
export const isCustomerPkg = (ownership?: string, connRole?: number) =>
  ownership === OwnershipEnum.USER && connRole === 1;

// 判断
export const getDatapkgApplyTicketType = (role: number) => {
  return DatasetRoleEnum.PUBLIC === role
    ? ApplyTicketTypeEnum.PUBLIC_DATAPKG_ON_ALBUM
    : ApplyTicketTypeEnum.APP_DATAPKG_ON_ALBUM;
};

export let ownershipMap: Record<OwnershipEnum, string>;
export const getOwnershipLabel = (ownershipType?: IOwnership, defaultLabel = '-') => {
  return (ownershipType && ownershipMap[ownershipType]) || defaultLabel;
};
export const getOwnershipOptions = () => {
  return _.map(ownershipMap, (label, value) => ({ label, value }));
};

// 是否个人数据包
export const isPersonalPkg = (ownershipType?: IOwnership) => ownershipType === OwnershipEnum.USER;

export let datapkgUpdateTypeMap: Record<DatapkgUpdateType, { title: string; tip: string }>;

export const getDatapkgUpdateTypeOptions = () =>
  _.map(datapkgUpdateTypeMap, (value, key) => ({
    label: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {value.title}
        <Tooltip placement="right" title={value.tip}>
          <Icon size={14} icon="info-2-outlined" style={{ marginLeft: '5px' }} />
        </Tooltip>
      </div>
    ),
    value: key,
    title: value.title,
  }));

// 地理列格式名称
const geometryFormatLabelMap: Record<string, string> = {
  [GeometryFormatEnum.WKB]: 'WKB',
  [GeometryFormatEnum.WKT]: 'WKT',
  [GeometryFormatEnum.GEOJSON]: 'GeoJSON',
  // [GeometryFormatEnum.AUTO]: '自动推断格式',
};
export const getGeometryFormatLabel = (type: string) => {
  return geometryFormatLabelMap[type] || geometryFormatLabelMap[GeometryFormatEnum.AUTO];
};
export const getGeometryFormatOptions = () => {
  return _.map(geometryFormatLabelMap, (label, value) => ({ label, value }));
};

// 坐标系名称
let srsLabelMap: Record<string, string>;
export const getSrsLabelLabel = (type: string) => {
  return srsLabelMap[type] || '';
};
export const getSrsOptions = () => {
  return _.map(srsLabelMap, (label, value) => ({ label, value }));
};

export const isPoint2PointGeometry = (type: DatapkgGeometryTypeEnum) => DatapkgGeometryTypeEnum.POINT_TO_POINT === type;

// 获取列名与关键列的映射
export const getColumnKeyTypeMap = (
  keyColumn: Partial<Record<KeyColumnEnum, string>>,
): Record<string, KeyColumnEnum[]> => {
  const result: Record<string, KeyColumnEnum[]> = {};
  _.forEach(_.keys(keyColumn), (keyType) => {
    const key: KeyColumnEnum = keyType as KeyColumnEnum;
    const column = keyColumn[key]!;
    if (result[column]) {
      result[column].push(key);
    } else {
      result[column] = [key];
    }
  });
  return result;
};

export const initDatapkgUtil = () => {
  datapkgGeometryTypeLabelMap = {
    [DatapkgGeometryTypeEnum.POINT]: i18n.chain.geo.point,
    [DatapkgGeometryTypeEnum.LINE]: i18n.chain.geo.line,
    [DatapkgGeometryTypeEnum.POLYGON]: i18n.chain.geo.polygon,
    [DatapkgGeometryTypeEnum.PLAIN]: i18n.chain.geo.plain,
    [DatapkgGeometryTypeEnum.POINT_TO_POINT]: i18n.chain.geo.pointToPoint,
    [DatapkgGeometryTypeEnum.POINT_TO_LINE]: i18n.chain.geo.pointToLine,
    [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: i18n.chain.geo.pointToPolygon,
    [DatapkgGeometryTypeEnum.LINE_TO_POINT]: i18n.chain.geo.lineToPoint,
    [DatapkgGeometryTypeEnum.LINE_TO_LINE]: i18n.chain.geo.lineToLine,
    [DatapkgGeometryTypeEnum.LINE_TO_POLYGON]: i18n.chain.geo.lineToPolygon,
    [DatapkgGeometryTypeEnum.POLYGON_TO_POINT]: i18n.chain.geo.polygonToPoint,
    [DatapkgGeometryTypeEnum.POLYGON_TO_LINE]: i18n.chain.geo.polygonToLine,
    [DatapkgGeometryTypeEnum.POLYGON_TO_POLYGON]: i18n.chain.geo.polygonToPolygon,
  };

  datapkgUpdateTypeMap = {
    [DatapkgUpdateType.APPEND]: {
      title: i18n.chain.datapkgOperate.append,
      tip: i18n.chain.datapkgOperate.appendTip,
    },
    [DatapkgUpdateType.REPLACE]: {
      title: i18n.chain.datapkgOperate.replace,
      tip: i18n.chain.datapkgOperate.replaceTip,
    },
    [DatapkgUpdateType.FORCE_REPLACE]: {
      title: i18n.chain.datapkgOperate.forceReplace,
      tip: i18n.chain.datapkgOperate.forceReplaceTip,
    },
    // [DatapkgUpdateType.INDEX]: {
    //   title: '按条件替换',
    //   tip: '将符合条件的部分已有数据替换为新数据，不可更改数据表结构（字段名称和类型需要与已有数据一模一样）',
    // },
  };

  srsLabelMap = {
    [SrsEnum.WGS84]: i18n.chain.srs.wgs84,
    [SrsEnum.BD09]: i18n.chain.srs.bd09,
    [SrsEnum.GCJ02]: i18n.chain.srs.gcj02,
  };

  datapkgPermissionLabelMap = {
    [DatapkgPermissionEnum.READ]: i18n.chain.permission.read,
    [DatapkgPermissionEnum.VIEW_DETAIL]: i18n.chain.permission.viewDetail,
    [DatapkgPermissionEnum.DOWNLOAD]: i18n.chain.permission.download,
    [DatapkgPermissionEnum.UPDATE]: i18n.chain.permission.update,
  };

  datapkgStoreTypeLabelMap = {
    [DatapkgStoreTypeEnum.TABLE]: i18n.chain.pkgStoreType.table,
    [DatapkgStoreTypeEnum.VIEW]: i18n.chain.pkgStoreType.view,
    [DatapkgStoreTypeEnum.SQL]: i18n.chain.pkgStoreType.sql,
    [DatapkgStoreTypeEnum.EXTABLE]: i18n.chain.pkgStoreType.extable,
    [DatapkgStoreTypeEnum.COLLABORATION]: i18n.chain.pkgStoreType.collaboration,
    [DatapkgStoreTypeEnum.COLLABORATION_V2]: i18n.chain.pkgStoreType.collaboration,
    [DatapkgStoreTypeEnum.COLLABORATION_CHILD]: i18n.chain.pkgStoreType.collaborationChild,
    [DatapkgStoreTypeEnum.CUSTOMER]: i18n.chain.pkgStoreType.customer,
    [DatapkgStoreTypeEnum.MVIEW]: i18n.chain.pkgStoreType.mview,
  };

  ownershipMap = {
    [OwnershipEnum.APP]: i18n.chain.dataType.org,
    [OwnershipEnum.USER]: i18n.chain.dataType.pirvate,
  };
};
