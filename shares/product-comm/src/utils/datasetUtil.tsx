import _ from 'lodash';
import { IDataset } from '../interfaces';

export const isDatasetEnableRead = (dataset: IDataset) => {
  return !!_.intersection(dataset.permissions, ['read', 'publish', 'update']).length;
};

export const isDatasetEnableUse = (dataset: IDataset) => {
  return !!_.intersection(dataset.permissions, ['publish', 'update']).length;
};

export const isDatasetEnableUpdate = (dataset: IDataset) => {
  return !!_.intersection(dataset.permissions, ['update']).length;
};

export const isDatasetEnableDelete = (dataset: IDataset) => {
  return !!_.intersection(dataset.permissions, ['delete']).length;
};
