/* stylelint-disable color-function-notation */
/* stylelint-disable alpha-value-notation */
/* stylelint-disable prettier/prettier */
/* stylelint-disable declaration-empty-line-before */
/* stylelint-disable length-zero-no-unit */
/* stylelint-disable rule-empty-line-before */
/* stylelint-disable scale-unlimited/declaration-strict-value */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable order/properties-order */
/* stylelint-disable-next-line import-notation */
@import './grid.less';
/* stylelint-disable-next-line import-notation */
@import './animation.less';

@theme: default;

@metro-prefix: ~'metro';
@form-item-cls: ~'@{metro-prefix}-formily-item';

@black: var(--metro-text-0);
@text-color-secondary: var(--metro-secondary-0);
@heading-color: var(--metro-text-0);
@error-color: #f5222d;
@warning-color: #faad14;
@success-color: #52c41a;
@border-color-base: hsv(0, 0, 85%);
@form-error-input-bg: var(--metro-secondary-default);
@form-warning-input-bg: var(--metro-secondary-default);
@background-color-light: hsv(0, 0, 98%);

@height-base: 32px;
@form-item-margin-bottom: 24px;
@font-size-base: 14px;
@font-size-sm: 12px;
@height-sm: 24px;
@font-size-lg: @font-size-base + 2px;
@height-lg: 40px;

// 方法
@input-hover-border-color: #1890ff;
.hover(@color: @input-hover-border-color) {
  border-color: @color;
  border-right-width: 1px;
}

@primary-color: #1890ff;
@primary-color-hover: #1890ff;
@outline-fade: 20%;
@primary-color-outline: fade(@primary-color, @outline-fade);
@input-outline-offset: 0 0;
@outline-blur-size: 0;
@outline-width: 2px;
@border-width-base: 1px;
.active(@borderColor: @primary-color; @hoverBorderColor: @primary-color-hover; @outlineColor: @primary-color-outline) {
  & when (@theme = dark) {
    border-color: @borderColor;
  }
  & when (not (@theme = dark) and not (@theme = variable)) {
    border-color: @hoverBorderColor;
  }
  & when not (@theme = variable) {
    /* stylelint-disable-next-line function-no-unknown */
    box-shadow: @input-outline-offset @outline-blur-size @outline-width fade(@borderColor, @outline-fade);
  }
  & when (@theme = variable) {
    border-color: @hoverBorderColor;
    box-shadow: @input-outline-offset @outline-blur-size @outline-width @outlineColor;
  }
  border-right-width: @border-width-base;
  outline: 0;
}

.@{form-item-cls} {
  position: relative;
  display: flex;
  margin-bottom: @form-item-margin-bottom - 2;
  font-size: @font-size-base;

  &-label {
    min-height: @height-base - 2;
    line-height: @height-base;
    label {
      cursor: text;
    }
  }

  textarea.@{metro-prefix}-input {
    height: auto;
  }

  // input[type=file]
  .@{metro-prefix}-upload {
    background: transparent;
  }

  .@{metro-prefix}-upload.@{metro-prefix}-upload-drag {
    background: @background-color-light;
  }

  input[type='radio'],
  input[type='checkbox'] {
    width: @font-size-base;
    height: @font-size-base;
  }

  // Radios and checkboxes on same line
  .@{metro-prefix}-radio-inline,
  .@{metro-prefix}-checkbox-inline {
    display: inline-block;
    margin-left: 8px;
    font-weight: normal;
    vertical-align: middle;
    cursor: pointer;

    &:first-child {
      margin-left: 0;
    }
  }

  .@{metro-prefix}-checkbox-vertical,
  .@{metro-prefix}-radio-vertical {
    display: block;
  }

  .@{metro-prefix}-checkbox-vertical + .@{metro-prefix}-checkbox-vertical,
  .@{metro-prefix}-radio-vertical + .@{metro-prefix}-radio-vertical {
    margin-left: 0;
  }

  .@{metro-prefix}-input-number {
    width: 100%;

    + .@{metro-prefix}-form-text {
      margin-left: 8px;
    }

    &-handler-wrap {
      z-index: 2; // https://github.com/ant-design/ant-design/issues/6289
    }
  }

  .@{metro-prefix}-select,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker {
    width: 100%;
  }

  // Don't impact select inside input group
  .@{metro-prefix}-input-group .@{metro-prefix}-select,
  .@{metro-prefix}-input-group .@{metro-prefix}-cascader-picker {
    width: auto;
  }
}

.@{form-item-cls}-label {
  position: relative;
  display: flex;

  &-content {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-tooltip {
    cursor: help;

    * {
      cursor: help;
    }

    label {
      border-bottom: 1px dashed currentcolor;
    }
  }
}

.@{form-item-cls}-label {
  color: @heading-color;
}

.@{form-item-cls}-label-align-left {
  > .@{form-item-cls}-label {
    justify-content: flex-start;
  }
}

.@{form-item-cls}-label-align-right {
  > .@{form-item-cls}-label {
    justify-content: flex-end;
  }
}

.@{form-item-cls}-label-wrap {
  .@{form-item-cls}-label {
    label {
      white-space: pre-line;
      word-break: break-all;
    }
  }
}

.@{form-item-cls}-feedback-layout-terse {
  margin-bottom: 8px;

  &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
    margin-bottom: 0;
  }
}

.@{form-item-cls}-feedback-layout-loose {
  margin-bottom: 22px;

  &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
    margin-bottom: 0;
  }
}

.@{form-item-cls}-feedback-layout-none {
  margin-bottom: 0px;

  &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
    margin-bottom: 0;
  }
}

.@{form-item-cls}-control {
  flex: 1;
  max-width: 100%;

  .@{form-item-cls}-control-content {
    display: flex;

    .@{form-item-cls}-control-content-component {
      width: 100%;
      min-height: @height-base - 2;
      line-height: @height-base + 2;

      &-has-feedback-icon {
        position: relative;
        display: flex;
        flex: 1;
        align-items: center;
      }
    }

    .@{form-item-cls}-addon-before {
      display: inline-flex;
      flex-shrink: 0;
      align-items: center;
      min-height: @height-base;
      margin-right: 8px;
    }

    .@{form-item-cls}-addon-after {
      display: inline-flex;
      flex-shrink: 0;
      align-items: center;
      min-height: @height-base;
      margin-left: 8px;
    }
  }

  .@{form-item-cls}-help,
  .@{form-item-cls}-extra {
    min-height: 22px;
    color: @text-color-secondary;
    line-height: 22px;
  }
}

.@{form-item-cls}-size-small {
  font-size: @font-size-sm;

  line-height: @height-sm;

  .@{form-item-cls}-label {
    min-height: @height-sm - 2;
    line-height: @height-sm;
  }

  .@{form-item-cls}-control-content {
    .@{form-item-cls}-control-content-component {
      min-height: @height-sm - 2;
      line-height: @height-sm + 2;
    }
  }

  .@{form-item-cls}-help,
  .@{form-item-cls}-extra {
    min-height: @height-sm - 4;
    line-height: @height-sm - 4;
  }

  .@{form-item-cls}-control-content {
    min-height: @height-sm - 2;
  }

  .@{form-item-cls}-label > label {
    height: @height-sm - 2;
  }

  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-picker {
    padding: 0px 11px;

    input {
      height: @height-sm - 2;
      font-size: @font-size-sm;
    }
  }

  .@{metro-prefix}-cascader-picker {
    height: @height-sm - 2;

    input {
      height: @height-sm - 2;
      padding: 0 7px;
      font-size: @font-size-sm;
    }
  }

  .@{metro-prefix}-select-single:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector {
    height: @height-sm - 2;
    padding: 0px 11px;
    font-size: @font-size-sm;
    line-height: @height-sm;

    .@{metro-prefix}-select-selection-search {
      height: @height-sm;
      line-height: @height-sm - 2;

      &-input {
        height: @height-sm;
        line-height: @height-sm - 2;
      }
    }

    .@{metro-prefix}-select-selection-placeholder {
      height: @height-sm;
      line-height: @height-sm - 2;
    }

    .@{metro-prefix}-select-selection-item {
      height: @height-sm;
      line-height: @height-sm - 2;
    }
  }

  .@{metro-prefix}-select-multiple:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector {
    height: @height-sm - 2;
    padding: 0px 2px;
    font-size: @font-size-sm;
    line-height: @height-sm;

    &::after {
      height: @height-sm - 8;
      line-height: @height-sm - 8;
    }

    .@{metro-prefix}-select-selection-search {
      height: @height-sm - 8;
      line-height: @height-sm - 8;
      margin-inline-start: 0;

      &-input {
        height: @height-sm - 12;
        line-height: @height-sm - 12;
      }
    }

    .@{metro-prefix}-select-selection-placeholder {
      left: 4px;
      height: @height-sm - 8;
      line-height: @height-sm - 8;
    }

    .@{metro-prefix}-select-selection-overflow-item {
      align-self: flex-start;
    }

    .@{metro-prefix}-select-selection-item {
      height: @height-sm - 8;
      line-height: @height-sm - 10;
    }
  }

  &.@{form-item-cls}-feedback-layout-terse {
    margin-bottom: 8px;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }

  &.@{form-item-cls}-feedback-layout-loose {
    margin-bottom: @height-sm - 4;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }
}

.@{form-item-cls}-size-large {
  font-size: @font-size-lg;
  line-height: @height-lg;

  .@{form-item-cls}-label {
    min-height: @height-lg - 2;
    line-height: @height-lg;
  }

  .@{form-item-cls}-control-content {
    .@{form-item-cls}-control-content-component {
      min-height: @height-lg - 2;
      line-height: @height-lg;
    }
  }

  .@{form-item-cls}-help,
  .@{form-item-cls}-extra {
    min-height: @form-item-margin-bottom;
    line-height: @form-item-margin-bottom;
  }

  .@{form-item-cls}-control-content {
    min-height: @height-lg - 2;
  }

  .@{metro-prefix}-input {
    font-size: @font-size-lg;
  }

  .@{metro-prefix}-input-number {
    font-size: @font-size-lg;

    input {
      height: @height-lg - 2;
    }
  }

  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-picker {
    padding: 0px 11px;
    line-height: @height-lg - 2;

    input {
      height: @height-lg - 2;
      font-size: @font-size-lg;
    }
  }

  .@{metro-prefix}-btn {
    height: @height-lg;
    padding: 0px 8px;
  }

  .@{metro-prefix}-radio-button-wrapper {
    height: @height-lg;
    line-height: @height-lg;
  }

  .@{metro-prefix}-cascader-picker {
    height: @height-lg - 2;

    input {
      height: @height-lg - 2;
      padding: 0px 11px;
      font-size: @font-size-lg;
    }
  }

  .@{metro-prefix}-select-single:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector {
    height: @height-lg;
    padding: 0px 11px;
    font-size: @font-size-lg;
    line-height: @height-lg;

    .@{metro-prefix}-select-selection-search {
      height: @height-lg;
      line-height: @height-lg - 2;

      &-input {
        height: @height-lg;
        line-height: @height-lg - 2;
      }
    }

    .@{metro-prefix}-select-selection-placeholder {
      height: @height-lg;
      line-height: @height-lg - 2;
    }

    .@{metro-prefix}-select-selection-item {
      height: @height-lg;
      line-height: @height-lg - 2;
    }
  }

  .@{metro-prefix}-select-multiple:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector {
    height: @height-lg - 2;
    padding: 0px 2px;
    font-size: @font-size-lg;
    line-height: @height-lg;

    &::after {
      height: @height-lg - 8;
      line-height: @height-lg - 8;
    }

    .@{metro-prefix}-select-selection-search {
      height: @height-lg - 8;
      line-height: @height-lg - 8;

      &-input {
        height: @height-lg - 12;
        line-height: @height-lg - 12;
      }
    }

    .@{metro-prefix}-select-selection-placeholder {
      height: @height-lg - 8;
      line-height: @height-lg - 8;
    }

    .@{metro-prefix}-select-selection-overflow-item {
      align-self: flex-start;
    }

    .@{metro-prefix}-select-selection-item {
      height: @height-lg - 8;
      line-height: @height-lg - 10;
    }
  }

  &.@{form-item-cls}-feedback-layout-terse {
    margin-bottom: 8px;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }

  &.@{form-item-cls}-feedback-layout-loose {
    margin-bottom: @form-item-margin-bottom;

    &.@{form-item-cls}-feedback-has-text:not(.@{form-item-cls}-inset) {
      margin-bottom: 0;
    }
  }
}

.@{form-item-cls} {
  &-layout-vertical {
    display: block;

    .@{form-item-cls}-label {
      min-height: @height-base - 10;
      line-height: 1.5715;
    }
  }
}

.@{form-item-cls}-feedback-layout-popover {
  margin-bottom: 8px;
}

.@{form-item-cls}-label-tooltip-icon {
  display: flex;
  align-items: center;
  max-height: @height-base;
  margin-left: 4px;
  color: var(--metro-text-2);

  span {
    display: inline-flex;
  }
}

.@{form-item-cls}-control-align-left {
  .@{form-item-cls}-control-content {
    justify-content: flex-start;
  }
}

.@{form-item-cls}-control-align-right {
  .@{form-item-cls}-control-content {
    justify-content: flex-end;
  }
}

.@{form-item-cls}-control-wrap {
  .@{form-item-cls}-control {
    white-space: pre-line;
    word-break: break-all;
  }
}

.@{form-item-cls}-asterisk {
  display: inline-block;
  margin-right: 4px;
  color: @error-color;
  font-family: SimSun, sans-serif;
}

.@{form-item-cls}-optional {
  color: rgba(0, 0, 0, 0.45);
}

.@{form-item-cls}-colon {
  margin-right: 8px;
  margin-left: 2px;
}

.@{form-item-cls}-help,
.@{form-item-cls}-extra {
  clear: both;
  min-height: @form-item-margin-bottom - 2;
  padding-top: 0px;
  color: rgba(0, 0, 0, 0.45);
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.@{form-item-cls}-fullness {
  > .@{form-item-cls}-control {
    > .@{form-item-cls}-control-content {
      > .@{form-item-cls}-control-content-component {
        > *:first-child {
          width: 100%;
        }
      }
    }
  }
}

.@{form-item-cls}-control-content-component-has-feedback-icon {
  padding-right: 8px;
  border: 1px solid @border-color-base;
  border-radius: 2px;
  outline: none;
  transition: all 0.3s;
  touch-action: manipulation;

  .@{metro-prefix}-input-number,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-cascader-picker:focus .@{metro-prefix}-cascader-input,
  .@{metro-prefix}-select:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input {
    border: none !important;
    box-shadow: none !important;
  }
}

.@{form-item-cls}-bordered-none {
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-cascader-picker:focus .@{metro-prefix}-cascader-input,
  .@{metro-prefix}-select:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector,
  .@{metro-prefix}-input {
    border: none !important;
    box-shadow: none !important;
  }

  .@{metro-prefix}-input-number-handler-wrap {
    border: none !important;

    .@{metro-prefix}-input-number-handler {
      border: none !important;
    }
  }
}

.@{form-item-cls}-inset {
  padding-left: 12px;
  border: 1px solid @border-color-base;
  border-radius: 2px;
  transition: 0.3s all;

  .@{metro-prefix}-input-number,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-cascader-picker:focus .@{metro-prefix}-cascader-input,
  .@{metro-prefix}-select:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input {
    border: none !important;
    box-shadow: none !important;
  }

  .@{metro-prefix}-input-number-handler-wrap {
    border: none !important;

    .@{metro-prefix}-input-number-handler {
      border: none !important;
    }
  }

  &:hover {
    .hover();
  }
}

.@{form-item-cls}-inset-active {
  .active();
}

.@{form-item-cls}-active {
  .@{form-item-cls}-control-content-component-has-feedback-icon {
    .active();
  }

  .@{metro-prefix}-input-number,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-cascader-picker:focus .@{metro-prefix}-cascader-input,
  .@{metro-prefix}-select:not(.@{metro-prefix}-select-customize-input) .@{metro-prefix}-select-selector,
  .@{metro-prefix}-input {
    .active();
  }
}

.@{form-item-cls} {
  &:hover {
    .@{form-item-cls}-control-content-component-has-feedback-icon {
      .hover();
    }
  }
}

.@{form-item-cls}-error {
  .@{metro-prefix}-select-selector,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-input,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input {
    border-color: @error-color !important;
  }

  .@{metro-prefix}-select-selector,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-input,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-affix-wrapper:hover,
  .@{metro-prefix}-input:hover {
    border-color: @error-color !important;
  }

  .@{metro-prefix}-select:not(.@{metro-prefix}-select-disabled):not(.@{metro-prefix}-select-customize-input) {
    .@{metro-prefix}-select-selector {
      background-color: @form-error-input-bg;
      border-color: @error-color !important;
    }

    &.@{metro-prefix}-select-open .@{metro-prefix}-select-selector,
    &.@{metro-prefix}-select-focused .@{metro-prefix}-select-selector {
      .active(@error-color);
    }
  }

  .@{metro-prefix}-input-number,
  .@{metro-prefix}-picker {
    background-color: @form-error-input-bg;
    border-color: @error-color;

    &-focused,
    &:focus {
      .active(@error-color);
    }

    &:not([disabled]):hover {
      background-color: @form-error-input-bg;
      border-color: @error-color;
    }
  }

  .@{metro-prefix}-cascader-picker:focus .@{metro-prefix}-cascader-input {
    background-color: @form-error-input-bg;
    .active(@error-color);
  }

  .@{metro-prefix}-input-affix-wrapper-focused,
  .@{metro-prefix}-input-affix-wrapper:focus,
  .@{metro-prefix}-input-focused,
  .@{metro-prefix}-input:focus {
    .active(@error-color);
  }
}

.@{form-item-cls}-error-help {
  color: @error-color !important;
}

.@{form-item-cls}-warning-help {
  color: @warning-color !important;
}

.@{form-item-cls}-success-help {
  color: @success-color !important;
}

.@{form-item-cls}-warning {
  .@{metro-prefix}-select-selector,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-input,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input {
    border-color: @warning-color !important;
  }

  .@{metro-prefix}-select-selector,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-input,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-affix-wrapper:hover,
  .@{metro-prefix}-input:hover {
    border-color: @warning-color !important;
  }

  .@{metro-prefix}-select:not(.@{metro-prefix}-select-disabled):not(.@{metro-prefix}-select-customize-input) {
    .@{metro-prefix}-select-selector {
      background-color: @form-warning-input-bg;
      border-color: @warning-color !important;
    }

    &.@{metro-prefix}-select-open .@{metro-prefix}-select-selector,
    &.@{metro-prefix}-select-focused .@{metro-prefix}-select-selector {
      .active(@warning-color);
    }
  }

  .@{metro-prefix}-input-number,
  .@{metro-prefix}-picker {
    background-color: @form-warning-input-bg;
    border-color: @warning-color;

    &-focused,
    &:focus {
      .active(@warning-color);
    }

    &:not([disabled]):hover {
      background-color: @form-warning-input-bg;
      border-color: @warning-color;
    }
  }

  .@{metro-prefix}-cascader-picker:focus .@{metro-prefix}-cascader-input {
    background-color: @form-warning-input-bg;
    .active(@warning-color);
  }

  .@{metro-prefix}-input-affix-wrapper-focused,
  .@{metro-prefix}-input-affix-wrapper:focus,
  .@{metro-prefix}-input-focused,
  .@{metro-prefix}-input:focus {
    .active(@warning-color);
  }
}

.@{form-item-cls}-success {
  .@{metro-prefix}-select-selector,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-input,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input {
    border-color: @success-color !important;
  }

  .@{metro-prefix}-select-selector,
  .@{metro-prefix}-cascader-picker,
  .@{metro-prefix}-picker,
  .@{metro-prefix}-input,
  .@{metro-prefix}-input-number,
  .@{metro-prefix}-input-affix-wrapper,
  .@{metro-prefix}-input-affix-wrapper:hover,
  .@{metro-prefix}-input:hover {
    border-color: @success-color !important;
  }

  .@{metro-prefix}-input-affix-wrapper-focused,
  .@{metro-prefix}-input-affix-wrapper:focus,
  .@{metro-prefix}-input-focused,
  .@{metro-prefix}-input:focus {
    border-color: @success-color !important;
    border-right-width: 1px !important;
    outline: 0;
  }
}
