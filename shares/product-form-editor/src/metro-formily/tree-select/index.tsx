/* eslint-disable */
import React from 'react';
import { connect, mapProps, mapReadPretty } from '@formily/react';
import { Loading } from '@metro/icons';
import { TreeSelect as MetroTreeSelect } from '@metroDesign/tree-select';
import { PreviewText } from '../preview-text';

export const TreeSelect = connect(
  MetroTreeSelect as any,
  mapProps(
    {
      dataSource: 'treeData',
      loading: true,
    },
    (props, field) => {
      return {
        ...props,
        // @ts-ignore
        suffixIcon: field?.['validating'] ? <Loading /> : props.suffixIcon,
      };
    },
  ),
  mapReadPretty(PreviewText.TreeSelect),
);

export default TreeSelect;
