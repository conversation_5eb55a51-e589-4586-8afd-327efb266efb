import _ from 'lodash';
import React from 'react';
import { ArrayField } from '@formily/core';
import { ISchema, Schema } from '@formily/json-schema';
import { observer, ReactFC, RecursionField, useField, useFieldSchema } from '@formily/react';
import { Card, CardProps } from '@metroDesign/card';
import { Empty } from '@metroDesign/empty';
import cls from 'classnames';
import { ExcelTemplate, IExcelColumn } from '@mdtProComm/components/excel-template';
import i18n from '../../languages';
import { usePrefixCls } from '../__builtins__';
import { ArrayBase } from '../array-base';
import './style.less';

const isAdditionComponent = (schema: ISchema) => {
  // eslint-disable-next-line sonarjs/no-duplicate-string
  return schema['x-component']?.indexOf('Addition') > -1;
};

const isIndexComponent = (schema: ISchema) => {
  return schema['x-component']?.indexOf?.('Index') > -1;
};

const isRemoveComponent = (schema: ISchema) => {
  return schema['x-component']?.indexOf?.('Remove') > -1;
};

const isCopyComponent = (schema: ISchema) => {
  return schema['x-component']?.indexOf?.('Copy') > -1;
};

const isMoveUpComponent = (schema: ISchema) => {
  return schema['x-component']?.indexOf?.('MoveUp') > -1;
};

const isMoveDownComponent = (schema: ISchema) => {
  return schema['x-component']?.indexOf?.('MoveDown') > -1;
};

const isOperationComponent = (schema: ISchema) => {
  return (
    isAdditionComponent(schema) ||
    isRemoveComponent(schema) ||
    isCopyComponent(schema) ||
    isMoveDownComponent(schema) ||
    isMoveUpComponent(schema)
  );
};

const useExcelTemplateInfo = (schema?: Schema): IExcelColumn[] => {
  if (!schema) return [];
  const properties = _.orderBy(_.values(_.get(schema.toJSON(), 'items.properties')), 'x-index');
  return _.reduce(
    properties,
    (buf, { type, title, name }) => {
      if (type === 'void') return buf;
      return buf.concat({
        label: title,
        value: name,
        type,
      });
    },
    [] as any,
  );
};

// eslint-disable-next-line sonarjs/cognitive-complexity
export const InternalArrayCards: ReactFC<CardProps> = observer((props) => {
  const field = useField<ArrayField>();
  const schema = useFieldSchema();
  const dataSource = Array.isArray(field.value) ? field.value : [];
  const prefixCls = usePrefixCls('formily-array-cards', props);
  const tmplColumns = useExcelTemplateInfo(schema);

  if (!schema) throw new Error('can not found schema object');

  const renderItems = () => {
    return dataSource?.map((item, index) => {
      const items = Array.isArray(schema.items) ? schema.items[index] || schema.items[0] : schema.items;
      const title = (
        <span>
          {items ? (
            <RecursionField
              schema={items}
              name={index}
              filterProperties={(schema) => {
                return !!isIndexComponent(schema);
              }}
              onlyRenderProperties
            />
          ) : null}
          {props.title || field.title}
        </span>
      );
      const extra = (
        <span>
          {items ? (
            <RecursionField
              schema={items}
              name={index}
              filterProperties={(schema) => {
                return !!isOperationComponent(schema);
              }}
              onlyRenderProperties
            />
          ) : null}
          {props.extra}
        </span>
      );
      const content = items ? (
        <RecursionField
          schema={items}
          name={index}
          filterProperties={(schema) => {
            if (isIndexComponent(schema)) return false;
            return !isOperationComponent(schema);
          }}
        />
      ) : null;
      return (
        <ArrayBase.Item key={index} index={index} record={() => field.value?.[index]}>
          <Card
            {...props}
            onChange={() => {}}
            className={cls(`${prefixCls}-item`, props.className)}
            title={title}
            extra={extra}
          >
            {content}
          </Card>
        </ArrayBase.Item>
      );
    });
  };

  const renderAddition = () => {
    return schema.reduceProperties((addition, schema, key) => {
      if (isAdditionComponent(schema)) {
        return <RecursionField schema={schema} name={key} />;
      }
      return addition;
    }, null);
  };

  const renderEmpty = () => {
    if (dataSource?.length) return;
    return (
      <Card
        {...props}
        onChange={() => {}}
        className={cls(`${prefixCls}-item`, props.className)}
        title={props.title || field.title}
      >
        <Empty />
      </Card>
    );
  };

  const templateComp =
    _.get(props, 'enableTemplate') && !_.get(props, 'disabled') ? (
      <ExcelTemplate
        fileName={`${i18n.chain.metroFormDesign.arrayCards}${new Date().toLocaleString()}.xlsx`}
        columns={tmplColumns}
        onChange={(uploadValues: any) => {
          (props.onChange as any)?.(uploadValues);
        }}
        className="metro-array-cards-template"
      />
    ) : null;

  return (
    <ArrayBase>
      {templateComp}
      {renderEmpty()}
      {renderItems()}
      {renderAddition()}
    </ArrayBase>
  );
});

export const ArrayCards = ArrayBase.mixin(InternalArrayCards);

ArrayCards.displayName = 'ArrayCards';

export default ArrayCards;
