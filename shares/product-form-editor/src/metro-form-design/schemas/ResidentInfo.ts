import { ISchema } from '@formily/react';
import { MetroSettingProp } from '../shared';

export const ResidentInfo: ISchema = {
  type: 'void',
  properties: {
    [MetroSettingProp.residentFieldList]: {
      type: 'array',
      'x-decorator': 'FormItem',
      'x-component': 'ArrayTable',
      'x-validator': [],
      'x-component-props': {
        bordered: false,
      },
      'x-decorator-props': {
        className: 'resident-info-table',
      },
      items: {
        type: 'object',
        properties: {
          iddpp2g4uhz: {
            type: 'void',
            // eslint-disable-next-line sonarjs/no-duplicate-string
            'x-component': 'ArrayTable.Column',
            'x-index': 0,
            properties: {
              y3czm5p9xff: {
                type: 'void',
                // eslint-disable-next-line sonarjs/no-duplicate-string
                'x-component': 'ArrayTable.SortHandle',
                'x-index': 0,
              },
            },
          },
          poh6gt6jes4: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            properties: {
              isSelect: {
                type: 'Array',
                'x-decorator': 'FormItem',
                'x-component': 'Checkbox.Group',
                enum: [
                  {
                    value: 'Y',
                  },
                ],
              },
            },
            'x-index': 1,
          },
          jgl10q1vhrn: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            properties: {
              fieldLabel: {
                type: 'string',
                'x-component': 'Input',
                'x-component-props': {
                  bordered: false,
                  disabled: true,
                },
              },
            },
            'x-index': 2,
          },
        },
      },
    },
  },
};
