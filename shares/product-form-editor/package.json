{"name": "@mdt/product-form-editor", "version": "0.20.12", "private": false, "description": "脉策产品级别问卷编辑器", "keywords": ["mdt", "product", "comm", "form-editor"], "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "directories": {"dist": "dist"}, "files": ["dist", "package.json"], "publishConfig": {"registry": "https://nexus.idatatlas.com/repository/npm-dev/"}, "scripts": {"build": "rimraf dist && gulp --gulpfile ../../_template/gulp/gulpfile.js --cwd ./", "test": "jest --coverage", "push": "npm publish"}, "dependencies": {"@designable/core": "1.0.0-beta.45", "@designable/react": "1.0.0-beta.45", "@designable/shared": "1.0.0-beta.45", "@dnd-kit/core": "6.0.8", "@dnd-kit/sortable": "7.0.2", "@formily/core": "2.3.0", "@formily/grid": "2.3.0", "@formily/json-schema": "2.3.0", "@formily/react": "2.3.0", "@formily/reactive": "2.3.0", "@formily/reactive-react": "2.3.0", "@formily/shared": "2.3.0", "@i18n-chain/react": "2.0.1", "@mdt/product-comm": "^1.28.12", "@mdt/product-tasks": "^1.25.12", "@metro/components": "0.4.3", "@metro/mobile-components": "^0.2.1-alpah.0", "@metro/rule-form": "^0.2.5", "@monaco-editor/react": "^4.2.1", "@syncpoint/wkx": "^0.5.2", "maptalks": "^v1.0.0-rc.23", "monaco-editor": "0.25.2", "react-color": "2.19.3", "react-sticky-box": "^2.0.4", "react-tiny-popover": "6.0.5"}, "devDependencies": {"@types/maptalks": "^0.49.3"}}