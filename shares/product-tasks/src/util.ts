import type { IOnUploadFile } from '@mdtProComm/components/markdown-editor';
import type { ILoadFileFunc } from '@mdtProComm/components/markdown-preview';
import type { IDatapkgDownloadPost, IFlowPost } from '@mdtProComm/interfaces';
import type { ITaskControllerOptions } from './create-datapkg-from-upload-local-file-task/TaskController';
import CreateDatapkgFromUploadLocalFileTask from './create-datapkg-from-upload-local-file-task';
import CreateTemplateFromUploadLocalFileTask from './create-template-from-upload-local-file-task';
import { CreateDownloadDatapkgTask } from './CreateDownloadDatapkgTask';
import { CreateRunDatapkgConstraintTask } from './CreateRunDatapkgConstraintTask';
import { CreateRunTemporaryFlowTask } from './CreateRunTemporaryFlowTask';
import { addTaskToList } from './single-globel';
import { WaitRunFlowTask } from './WaitRunFlowTask';
import { WaitRunTemporaryFlowTask } from './WaitRunTemporaryFlowTask';

// 下载数据包
export const addCreateDownloadDatapkgTask = (pkgId: string, title: string, postData: IDatapkgDownloadPost) => {
  const task = new CreateDownloadDatapkgTask(pkgId, title, postData);
  addTaskToList(task);
  return task;
};

// 本地上传数据包
export const addCreateDatapkgFromUploadLocalFileTask = (options: ITaskControllerOptions) => {
  const task = new CreateDatapkgFromUploadLocalFileTask(options);
  addTaskToList(task);
  return task;
};

// 运行质量监控
export const addCreateRunDatapkgConstraintTask = (pkgId: string, pkgName: string, constraintIds?: string[]) => {
  const task = new CreateRunDatapkgConstraintTask(pkgId, pkgName, constraintIds);
  addTaskToList(task);
  return task;
};

// 本地上传文档模板文件
export const addCreateTemplateFromUploadLocalFileTask = (
  file: File,
  uploadFunc: IOnUploadFile,
  loadFunc: ILoadFileFunc,
) => {
  const task = new CreateTemplateFromUploadLocalFileTask({ file, uploadFunc, loadFunc });
  addTaskToList(task);
  return task;
};

export const addWaitRunFlowTask = (taskId: string, flowId: string, flowName: string) => {
  const task = new WaitRunFlowTask(taskId, flowId, flowName);
  addTaskToList(task);
  return task;
};

export const addWaitRunTemporaryFlowTask = (taskId: string, flowRunId: string, flowName: string) => {
  const task = new WaitRunTemporaryFlowTask(taskId, flowRunId, flowName);
  addTaskToList(task);
  return task;
};

export const addCreateRunTemporaryFlowTask = (flowNames: string[], flowDatas: (IFlowPost | undefined)[]) => {
  const task = new CreateRunTemporaryFlowTask(flowNames, flowDatas);
  addTaskToList(task);
  return task;
};
