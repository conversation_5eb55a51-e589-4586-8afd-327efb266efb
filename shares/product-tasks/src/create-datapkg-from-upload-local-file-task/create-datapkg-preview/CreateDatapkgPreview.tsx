import { FC } from 'react';
import { useObservableState } from '@mdtBsComm/hooks/use-observable-state';
import { randomUuid } from '@mdtBsComm/utils/stringUtil';
import { DataListCompTableWithCurd } from '@mdtBsComponents/data-list-comp-table-curd';
import DataTable from '@mdtDesign/data-table';
import { RadioGroup } from '@mdtDesign/radio';
import i18n from '../../languages';
import { CreateDatapkgPreviewController } from './CreateDatapkgPreviewController';
import './index.less';

export enum MenuKeys {
  DATA_PREVIEW = 'data_preview',
  FIELD_PREVIEW = 'field_preview',
}

interface IProps {
  controller: CreateDatapkgPreviewController;
}

const FieldPreview: FC<IProps> = ({ controller }) => {
  useObservableState(controller.getEditPermission$());
  return <DataListCompTableWithCurd key={randomUuid()} controller={controller.getFieldController()!} />;
};

export const CreateDatapkgPreview: FC<IProps> = ({ controller }) => {
  const activeKey = useObservableState(controller.getActiveMenuKey$());
  const tableProps = controller.getDataTableProps();

  const handleMenuChange = (val: string) => {
    controller.changeActiveMenuKey$(val as MenuKeys);
  };

  const content =
    activeKey === MenuKeys.DATA_PREVIEW ? <DataTable {...tableProps} /> : <FieldPreview controller={controller} />;

  return (
    <div className="module_create-datapkg-preview">
      <RadioGroup
        radioType="nav"
        value={activeKey}
        options={[
          {
            label: i18n.chain.proTasks.dataPreview,
            value: MenuKeys.DATA_PREVIEW,
          },
          {
            label: i18n.chain.proTasks.keyType,
            value: MenuKeys.FIELD_PREVIEW,
          },
        ]}
        onChange={handleMenuChange}
      />
      {content}
    </div>
  );
};
