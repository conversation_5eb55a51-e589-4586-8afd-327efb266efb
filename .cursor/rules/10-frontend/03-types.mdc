---
description: 
globs: client/src/**/*.ts*
alwaysApply: false
---
# TypeScript类型规范

## 类型定义位置

### 全局通用类型
集中管理在`@/types`目录,按功能模块分组:
```
types/
├── api.ts          # API相关类型统一定义在此
└── common.ts      # 通用类型
```

### 组件专用类型
直接定义在组件文件内部:
```tsx
// UserCard.tsx
interface UserCardProps {
  user: User;
  onEdit: (id: string) => void;
}

export function UserCard({ user, onEdit }: UserCardProps) {
  // ...
}
```

### 模块级类型
当类型仅在单一模块内使用,定义在模块的types.ts文件:
```
module/
├── components/
├── ModuleController.ts
└── types.ts       # 模块内共享类型
```

## 类型命名规范

### 后缀命名约定
- **API数据传输对象**: `xxxDto` (如 `UserDto`)
- **视图模型对象**: `xxxVM` (如 `UserProfileVM`)
- **请求参数**: `xxxParams`/`xxxBody` (如 `LoginParams`)
- **响应数据**: `xxxResponse` (如 `LoginResponse`) 
- **组件属性**: `xxxProps` (如 `ButtonProps`)
- **状态接口**: `xxxState` (如 `FormState`)

### 类型前缀约定
- **枚举类型**: `E枚举名` (如 `EUserRole`)
- **类型别名**: 使用`type`关键字定义,采用PascalCase
- **接口**: 使用`interface`关键字定义,采用PascalCase

## 类型定义最佳实践

### 类型声明示例
```typescript
// 接口定义
interface User {
  id: string;
  name: string;
  email: string;
  role: EUserRole;
  createdAt: Date;
}

// 枚举定义
enum EUserRole {
  Admin = 'admin',
  User = 'user',
  Guest = 'guest'
}

// 部分类型
type UserBasicInfo = Pick<User, 'id' | 'name'>;

// 响应类型
interface UserResponse {
  data: User;
  message: string;
  success: boolean;
}

// 请求参数
interface UpdateUserParams {
  name?: string;
  email?: string;
}
```

### 类型导入与使用
```typescript
// ✅ 推荐方式:从类型定义文件导入
import { User, UpdateUserParams } from '@/types/models/user';
import { EUserRole } from '@/types/enums';

// ❌ 避免的方式:从其他模块导入类型
// import { User } from '@/modules/user/UserComponent';

function updateUser(userId: string, params: UpdateUserParams): Promise<User> {
  // ...
}
```

## 使用规范

1. **始终使用类型**: 所有变量、参数、返回值都应有明确类型
2. **类型精确性**: 避免使用`any`,优先使用具体类型或泛型
3. **可选属性**: 使用`?`表示可选属性而非联合类型
   ```typescript
   // ✅ 好的方式
   interface Options {
     name?: string;
   }
   
   // ❌ 避免的方式
   interface Options {
     name: string | undefined;
   }
   ```
4. **类型断言**: 必要时使用`as`进行类型断言,但应尽量避免
5. **严格检查**: 启用`strict`模式,包括`strictNullChecks`等
