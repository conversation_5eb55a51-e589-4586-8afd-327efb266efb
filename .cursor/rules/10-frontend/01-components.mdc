---
description: 
globs: client/src/**/*.ts*
alwaysApply: false
---
# 前端组件开发规范

## 组件类型划分

### 普通组件 (Components)
- **职责**: UI渲染和基础交互
- **特点**: 
  - 无业务逻辑,只负责展示
  - 通过props接收数据和回调
  - 可包含UI状态(展开/折叠等)
  - 可复用,与业务上下文无关
- **示例**: Button, Card, Modal, Input

### 容器组件 (Containers)
- **职责**: 业务逻辑处理和状态管理
- **特点**:
  - 包含业务逻辑和数据处理
  - 配合Controller管理业务状态
  - 通常组合多个UI组件
  - 与特定业务场景紧密相关
- **示例**: LoginForm, UserProfile, DataTable

## 组件文件结构

### 单文件组件
```
components/
└── Button.tsx              # 单一简单组件直接放根目录
```

### 组合组件
```
components/
└── data-table/             # 复杂组件使用目录组织(kebab-case)
    ├── DataTable.tsx       # 主组件文件(PascalCase)
    ├── DataTableHeader.tsx # 子组件
    ├── DataTableRow.tsx    # 子组件
    ├── types.ts            # 组件类型定义
    ├── utils.ts            # 组件工具函数
    └── index.ts            # 导出接口
```

### 业务场景组件
```
scenes/
└── anti-fraud/             # 业务场景目录
    ├── components/         # 该场景专有组件
    │   ├── FraudAlert.tsx
    │   └── FraudStats.tsx
    └── AntiFraudPage.tsx   # 场景页面组件
```

## 组件命名规范

1. **文件命名**:
   - 组件文件: PascalCase (如 `UserCard.tsx`)
   - 目录名: kebab-case (如 `user-card/`)
   - 工具文件: camelCase (如 `tableUtils.ts`)

2. **组件命名**:
   - 清晰表达功能: `UserList` 而非 `List`
   - 子组件命名: 父组件名 + 子功能名 (如 `TableHeader`, `TableRow`)
   - 场景组件: 场景名 + 功能 (如 `AntiFraudStats`)

## 组件编写最佳实践

### 组件接口设计
```tsx
// ✅ 好的实践
interface UserCardProps {
  user: User;               // 明确的数据类型
  onEdit: (id: string) => void;  // 明确的回调函数签名
  isActive?: boolean;       // 可选属性
}

// ❌ 避免的方式
interface BadProps {
  data: any;                // 类型不明确
  callback: Function;       // 函数签名不明确
  // ...大量无关属性
}
```

### 状态管理方式
```tsx
// ✅ 推荐使用Controller + useObservableState
function UserCard({ controller }: { controller: UserController }) {
  const user = useObservableState(controller.user$);
  const isLoading = useObservableState(controller.isLoading$);
  
  return (
    <Card>
      {isLoading ? <Spinner /> : <UserInfo user={user} />}
    </Card>
  );
}

// ❌ 避免直接在组件中处理复杂状态
function BadComponent() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 大量业务逻辑...
}
```

### 组件拆分原则
- **单一职责**: 每个组件只负责一个功能
- **复用性**: 频繁重复的UI模式应提取为独立组件
- **复杂度管理**: 超过100行的组件考虑拆分
- **状态共享**: 共享同一状态的UI元素应组合在一起

### 组件拆分与交互规范
- **功能/布局拆分**: UI组件和容器组件应按照功能或布局拆分为子组件
- **Controller传递**: 子组件优先直接传递controller而非多个独立props
  ```tsx
  // ✅ 推荐方式
  <UserPanel controller={controller} />
  
  // ❌ 避免方式
  <UserPanel 
    user={user} 
    isLoading={isLoading} 
    onDelete={handleDelete}
    onEdit={handleEdit} 
  />
  ```
- **列表渲染**: 列表循环的子内容必须抽离为独立子组件,提高性能
  ```tsx
  // ✅ 推荐方式
  function UserList({ controller }) {
    const users = useObservableState(controller.users$);
    return (
      <ul>
        {users.map(user => 
          <UserItem key={user.id} user={user} controller={controller} />
        )}
      </ul>
    );
  }
  
  // ❌ 避免方式
  function BadUserList({ controller }) {
    const users = useObservableState(controller.users$);
    return (
      <ul>
        {users.map(user => (
          <li key={user.id}>
            {/* 复杂内联渲染 */}
            <div>{user.name}</div>
            <button onClick={() => controller.deleteUser(user.id)}>删除</button>
          </li>
        ))}
      </ul>
    );
  }
  ```

### 性能优化技巧
- 使用`React.memo()`包裹纯展示组件
- 明智地使用`useCallback`和`useMemo`:
  ```tsx
  // ✅ 正确使用useCallback - 依赖项稳定
  const handleClick = useCallback(() => {
    console.log('按钮被点击');
  }, []); // 空依赖数组,函数只创建一次
  
  const handleEdit = useCallback((id) => {
    controller.editItem(id);
  }, [controller]); // 只依赖于稳定的controller引用
  
  // ❌ 避免无效的useCallback - 依赖频繁变化的值
  const handleSubmit = useCallback(() => {
    submitData(formData);
  }, [formData]); // 如果formData频繁变化,useCallback失去缓存意义
  
  // ✅ 更好的写法 - 直接定义函数
  const handleSubmit = () => {
    submitData(formData);
  };
  ```
- 大列表使用虚拟滚动(如virtua,react-virtualized等)
- 使用React.useTransition优化大型状态更新
- 考虑使用状态派生优化渲染性能
  ```tsx
  // ✅ 状态派生 - 避免在每次渲染时重新计算
  const filteredUsers = useMemo(() => {
    return users.filter(user => user.name.includes(searchTerm));
  }, [users, searchTerm]);
  ```

### 组件渲染优化规范

#### 组件拆分与粒度原则
- **适度拆分原则**: 组件拆分应平衡性能优化和代码复杂度
  ```tsx
  // ✅ 推荐 - 适度拆分
  function UserProfile({ controller }) {
    return (
      <div>
        <UserProfileHeader user={user} />
        <UserContent user={user} posts={posts} />
        <UserFooter />
      </div>
    );
  }
  
  // ❌ 避免过度拆分 - 导致组件过多,增加心智负担
  function UserProfile({ controller }) {
    return (
      <div>
        <UserAvatar user={user} />
        <UserName user={user} />
        <UserBio user={user} />
        <UserStats user={user} />
        <UserPostList posts={posts} />
        <UserPostCount count={posts.length} />
        {/* 过多的小组件会导致代码难以追踪 */}
      </div>
    );
  }
  ```

- **状态共享范围**: 基于状态共享范围来决定组件边界
  ```tsx
  // ✅ 推荐 - 按状态共享范围拆分
  function CheckoutForm() {
    // 这些状态被下方多个区域共享
    const [address, setAddress] = useState({});
    const [payment, setPayment] = useState({});
    
    return (
      <form>
        <AddressSection address={address} onChange={setAddress} />
        <PaymentSection payment={payment} onChange={setPayment} />
        <OrderSummary address={address} payment={payment} />
      </form>
    );
  }
  ```

#### 状态依赖优化方式
- **按状态依赖拆分**: 将组件按状态依赖拆分,避免不必要的重渲染
  ```tsx
  function ProductPage({ product, reviews, isLoading }) {
    // ✅ 只依赖product的组件
    const productDetails = useMemo(() => (
      <ProductDetails product={product} />
    ), [product]);
    
    // ✅ 只依赖reviews的组件
    const reviewsList = useMemo(() => (
      <ReviewsList reviews={reviews} />
    ), [reviews]);
    
    // ✅ 只依赖isLoading的组件
    const loadingIndicator = useMemo(() => (
      isLoading ? <LoadingSpinner /> : null
    ), [isLoading]);
    
    return (
      <div>
        {loadingIndicator}
        {productDetails}
        {reviewsList}
      </div>
    );
  }
  ```

#### Context与Props传递选择
- **Context适用场景**: 
  - 组件树嵌套较深(3层以上)且多个层级需要同一数据
  - 全局状态管理(主题、用户认证等)
  - 需要避免多层组件的props透传

- **直接Props传递适用场景**:
  - 组件嵌套层级较浅(1-2层)
  - 父子组件直接通信
  - 数据流向明确且简单

  ```tsx
  // ✅ 嵌套浅,直接使用props
  function SimpleForm({ onSubmit }) {
    return (
      <form>
        <FormFields />
        <SubmitButton onClick={onSubmit} />
      </form>
    );
  }
  
  // ✅ 嵌套深,使用Context
  function ComplexNestedUI() {
    return (
      <ThemeProvider>
        <Layout>
          <Sidebar>
            <Navigation>
              <NavItem />  {/* 需要访问主题 */}
            </Navigation>
          </Sidebar>
        </Layout>
      </ThemeProvider>
    );
  }
  ```

#### Controller传递最佳实践
- **优先使用Controller传递模式**: 在Controller实例相对稳定的情况下,优先直接传递controller给子组件
  ```tsx
  // ✅ 优先推荐 - 直接传递controller
  function UserForm({ controller }) {
    return (
      <div>
        <UserNameField controller={controller} />
        <UserEmailField controller={controller} />
        <SubmitButton controller={controller} />
      </div>
    );
  }
  
  // 子组件自行订阅所需状态
  const UserNameField = memo(({ controller }) => {
    const name = useObservableState(controller.name$);
    const isLoading = useObservableState(controller.isLoading$);
    
    const handleChange = (e) => {
      controller.updateName(e.target.value);
    };
    
    return <input value={name} onChange={handleChange} disabled={isLoading} />;
  });
  ```

- **Controller传递的优势**:
  1. 功能扩展无需修改组件接口
  2. 子组件自主管理,降低容器复杂度
  3. 适合长期维护的功能模块
  4. 组件间无需通过props协调,减少耦合

- **Controller传递适用场景**:
  - Controller实例变化不频繁
  - 功能模块可能持续扩展
  - 子组件与特定业务逻辑紧密关联
  - 组件的prop接口可能频繁变化

- **方法传递的适用场景**: 当只需要有限功能时,考虑传递具体方法而非整个controller
  ```tsx
  // 适用于功能简单且固定的场景
  <UserForm 
    onSave={userController.saveUser}
    onValidate={userController.validateForm}
  />
  ```

#### Props vs Controller传递决策
根据以下因素选择合适的传递方式:

1. **稳定性**: Controller实例是否稳定
   - 稳定 → 优先考虑Controller传递
   - 不稳定 → 考虑Props或Context传递

2. **功能复杂度**: 组件需要访问的功能数量
   - 功能单一 → Props传递足够
   - 功能复杂或可能扩展 → Controller传递更合适

3. **独立性要求**: 组件是否需要高度独立
   - 需要独立 → Props传递更明确
   - 允许业务耦合 → Controller传递更便捷

4. **维护周期**: 功能模块的预期维护时间
   - 长期维护 → Controller传递更易于扩展
   - 短期/一次性 → Props传递更简单直接

```tsx
// 示例: 基于不同场景选择传递方式
// 场景1: 简单UI组件,功能稳定
<SimpleForm onSubmit={handleSubmit} isLoading={isLoading} />

// 场景2: 复杂业务组件,功能可能扩展
<ComplexBusinessModule controller={moduleController} />

// 场景3: 深层嵌套组件
<ThemeProvider value={theme}>
  <DeepNestedComponent />
</ThemeProvider>
```

#### 混合策略
在复杂应用中,可以结合不同传递方式:
- 顶层业务模块使用Controller传递
- 中间层组件可混合使用Props和Controller
- 底层UI组件优先使用Props传递以保持通用性

#### 更多详细案例和优化经验请参考 `.cursor/rules/10-frontend/component-optimization-cases.mdc` 文件

## 组件文档编写
为复杂组件提供简洁文档,包含:
- 组件用途
- Props说明
- 使用示例
- 注意事项

## 优化案例与经验

### LoginForm页面优化经验总结

#### 优化过程回顾

1. **初始状态评估**
   - 起初的LoginForm是单一大组件,所有状态和逻辑混杂
   - Controller与Form之间存在大量重复Props传递
   - 组件重渲染频繁,性能次优

2. **重构步骤**
   - **Controller改进**: 
     - 将统一的formState拆分为独立的状态变量(identifier$, password$)
     - 添加派生状态(isValid$)以减少计算开销
     - 优化login方法,支持自动获取最新状态值

   - **组件拆分**:
     - 识别不同的功能区域,如表单头部、输入字段、提交按钮等
     - 按功能和状态依赖将组件拆分成合适粒度的子组件
     - 使用memo包装所有子组件

   - **状态传递模式演进**:
     - 最初:整体formState传递 → 状态和更新函数分离传递
     - 中期:Props分散传递 → Context统一传递(后发现过度设计)
     - 最终:直接Controller传递(最佳平衡点)

   - **交互处理优化**:
     - 从子组件层面处理交互,直接调用controller方法
     - 移除不必要的useCallback包装
     - 移除组件不需要的状态订阅

3. **关键决策点**
   - 选择Controller传递而非Props传递或Context
   - 确定组件拆分的适当粒度(避免过度拆分)
   - 移除SubmitButton对identifier和password的依赖

#### 经验教训

1. **组件拆分平衡点**
   - **经验**: 组件拆分需考虑代码可读性和性能的平衡。过度拆分会导致代码跟踪困难,拆分不足则影响性能。
   - **建议**: 以功能和状态依赖为基础拆分组件,不必为每个UI元素创建组件。

2. **Controller传递优势**
   - **经验**: 在功能可能扩展的业务组件中,直接传递controller比拆分props更有优势。
   - **建议**: 当子组件与特定业务逻辑紧密相关,且功能可能扩展时,优先考虑Controller传递模式。

3. **状态依赖最小化**
   - **经验**: SubmitButton只需要isValid和isLoading状态,不需要订阅identifier和password。
   - **建议**: 每个组件只订阅其真正需要的状态,Controller方法应支持内部获取状态。

4. **浅层嵌套的Context使用**
   - **经验**: 对于LoginForm这种嵌套层级较浅的组件,使用Context是过度设计。
   - **建议**: 对于1-2层嵌套的组件结构,直接Props或Controller传递更简洁高效。

#### 具体实现要点

1. **Controller优化**
   ```typescript
   // 优化前
   public login(identifier: string, password: string): Promise<void> {
     // 使用参数传入的值
   }
   
   // 优化后
   public async login(identifier?: string, password?: string): Promise<void> {
     // 如果没有传入参数,从BehaviorSubject获取最新值
     const actualIdentifier = identifier ?? this.identifier$.getValue()
     const actualPassword = password ?? this.password$.getValue()
     // 使用实际值
   }
   ```

2. **组件依赖最小化**
   ```tsx
   // 优化前 - 不必要的依赖
   const SubmitButton = memo(({ controller }) => {
     const identifier = useObservableState(controller.identifier$)
     const password = useObservableState(controller.password$)
     const isValid = useObservableState(controller.isValid$)
     const isLoading = useObservableState(controller.isLoading$)
     
     return (/* ... */)
   })
   
   // 优化后 - 最小化依赖
   const SubmitButton = memo(({ controller }) => {
     const isValid = useObservableState(controller.isValid$)
     const isLoading = useObservableState(controller.isLoading$)
     
     return (/* ... */)
   })
   ```

3. **子组件状态自主管理**
   ```tsx
   // 优化后的模式 - 自主管理状态
   const IdentifierInput = memo<{ controller: LoginController }>(({ controller }) => {
     // 自行订阅所需状态
     const identifier = useObservableState(controller.identifier$)
     const isLoading = useObservableState(controller.isLoading$)

     // 自行处理交互
     const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
       controller.updateIdentifier(e.target.value)
     }
     
     return (/* ... */)
   })
   ```

#### 适用场景与注意事项

- **适用场景**:
  - 表单类组件
  - 具有多个相关子组件的业务功能区
  - 需要长期维护和迭代的功能模块

- **注意事项**:
  - Controller实例应相对稳定,避免频繁重建
  - 子组件只订阅真正需要的状态
  - 所有Controller方法应具有高内聚性,相关功能集中