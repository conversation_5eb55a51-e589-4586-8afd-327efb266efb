---
description: 
globs: client/src/**/*.ts*
alwaysApply: false
---

# 响应式Controller创建模式规范

## 核心原则

**【重要原则】** 所有需要动态创建的子Controller,必须采用响应式方式创建,严禁在UI组件内手动创建控制器实例。

## useController Hook使用规范

### 使用范围

**【重要说明】** useController Hook仅限于在页面级别组件中使用,用于创建页面的主控制器。子容器组件必须通过父容器的控制器创建并传递,不应自行使用useController创建控制器。

```tsx
// ✅ 正确:在页面级组件使用useController
function SomePage() {
  const [controller] = useController(() => {
    const controller = new SomePageController();
    return [controller, {}];
  }, []);
  
  return <SomeContainer controller={controller} />;
}

// ❌ 错误:在子容器组件中使用useController
function SomeContainer() {
  // 错误:子容器不应直接使用useController
  const [controller] = useController(() => {
    const controller = new SomeContainerController();
    return [controller, {}];
  }, []);
  
  return <div>...</div>;
}

// ✅ 正确:子容器通过props接收控制器
function SomeContainer({ controller }) {
  // 通过props接收父容器传递的控制器
  return <div>...</div>;
}
```

### 控制器创建与传递层级

1. **页面级控制器**:由页面组件使用useController创建
2. **容器级控制器**:由父控制器响应式创建和管理
3. **容器组件**:通过props接收控制器,而非自行创建

## 响应式创建模式

### 场景状态驱动的Controller创建

在涉及场景切换、选项变更等动态场景下,应使用RxJS的状态流驱动Controller的创建和销毁:

```typescript
// ✅ 推荐模式:响应式创建Controller
class ParentController {
  // 定义状态
  public selectedItem$ = new BehaviorSubject<Item | null>(null);
  public childController$ = new BehaviorSubject<ChildController | null>(null);
  
  constructor() {
    // 订阅状态变化,响应式创建/销毁子Controller
    this.selectedItem$.pipe(
      // 避免初始null值触发
      skip(1),
      // 处理之前的Controller实例
      tap(() => this.childController$.getValue()?.destroy())
    ).subscribe(item => {
      if (item) {
        // 创建新的Controller实例
        const controller = new ChildController(item.id);
        this.childController$.next(controller);
      } else {
        this.childController$.next(null);
      }
    });
  }
  
  // 资源释放
  public destroy(): void {
    this.childController$.getValue()?.destroy();
    this.childController$.complete();
    this.selectedItem$.complete();
  }
}
```

### 响应式创建的优势

1. **状态一致性**: Controller的创建与状态变化严格同步,避免不一致
2. **生命周期管理**: 自动化的Controller创建和销毁,减少内存泄漏风险
3. **依赖注入清晰**: 父状态可直接传递给子Controller,依赖关系明确
4. **可测试性**: 便于单元测试,可以模拟状态流来测试Controller创建逻辑

## 适用场景

### 场景切换

当用户在不同功能模块、视图间切换时,应使用状态流驱动相关Controller的创建:

```typescript
// 场景类型定义
export enum SceneType {
  DASHBOARD = 'dashboard',
  ANALYSIS = 'analysis',
  SETTINGS = 'settings'
}

// 场景控制器管理
class AppController {
  public currentScene$ = new BehaviorSubject<SceneType>(SceneType.DASHBOARD);
  private sceneController$ = new BehaviorSubject<BaseSceneController | null>(null);
  
  constructor() {
    // 响应式创建场景Controller
    this.currentScene$.pipe(
      distinctUntilChanged()
    ).subscribe(scene => {
      // 销毁旧的Controller
      this.sceneController$.getValue()?.destroy();
      
      // 创建新的Controller
      let controller: BaseSceneController;
      switch (scene) {
        case SceneType.DASHBOARD:
          controller = new DashboardController();
          break;
        case SceneType.ANALYSIS:
          controller = new AnalysisController();
          break;
        case SceneType.SETTINGS:
          controller = new SettingsController();
          break;
      }
      
      this.sceneController$.next(controller);
    });
  }
}
```

### 表单编辑与创建

创建和编辑表单时,应根据选中项状态响应式创建表单Controller:

```typescript
class ItemManagerController {
  public selectedItem$ = new BehaviorSubject<Item | null>(null);
  public formController$ = new BehaviorSubject<FormController | null>(null);
  
  // 初始化监听
  private initListeners() {
    // 监听选中项变化,创建或更新表单控制器
    this.selectedItem$.pipe(skip(1)).subscribe(item => {
      // 销毁旧的表单控制器
      this.formController$.getValue()?.destroy();
      
      // 创建新的表单控制器
      const formController = new FormController({
        initialData: item || undefined,
        onSubmit: this.handleSubmit,
        onCancel: this.handleCancel
      });
      
      this.formController$.next(formController);
    });
  }
  
  // 处理创建新项目
  public handleCreate() {
    this.selectedItem$.next(null); // 触发创建新的FormController
  }
  
  // 处理编辑项目
  public handleEdit(item: Item) {
    this.selectedItem$.next(item); // 触发创建编辑模式的FormController
  }
}
```

## 反模式示例

**【严禁】** 在UI组件中直接创建Controller:

```typescript
// ❌ 错误模式:在组件中创建Controller
function ItemEditForm({ itemId }: { itemId: string }) {
  // 错误:在组件中创建Controller
  const [controller] = useState(() => new ItemFormController(itemId));
  
  // ...
}
```

**【严禁】** 在父Controller中命令式创建子Controller:

```typescript
// ❌ 错误模式:命令式创建Controller
class BadParentController {
  private childController: ChildController | null = null;
  
  // 错误:命令式方法创建和管理Controller
  public createChildController(itemId: string) {
    if (this.childController) {
      this.childController.destroy();
    }
    this.childController = new ChildController(itemId);
  }
}
```

## 最佳实践示例

以PromptTemplateManagerController为例,展示响应式创建Controller的最佳实践:

```typescript
export class PromptTemplateManagerController {
  // 状态定义
  public templates$ = new BehaviorSubject<TemplateDto[]>([]);
  public selectedTemplate$ = new BehaviorSubject<TemplateDto | null>(null);
  public formController$ = new BehaviorSubject<FormController | null>(null);
  
  constructor() {
    this.initListeners();
    this.fetchTemplates();
  }
  
  // 响应式创建Controller
  private initListeners() {
    // 监听选中模板变化,响应式创建表单控制器
    this.selectedTemplate$.pipe(skip(1)).subscribe(template => {
      // 销毁旧的表单控制器
      this.formController$.getValue()?.destroy();
      
      // 创建新的表单控制器
      const formController = new TemplateFormController({
        initialTemplate: template,
        onSubmit: this.handleSubmit,
        onCancel: this.handleCancel
      });
      
      this.formController$.next(formController);
    });
  }
  
  // 处理创建或编辑
  public handleCreateOrEdit(template?: TemplateDto) {
    this.selectedTemplate$.next(template || null);
  }
  
  // 资源释放
  public destroy() {
    this.formController$.getValue()?.destroy();
    this.formController$.complete();
    this.selectedTemplate$.complete();
    this.templates$.complete();
  }
}
```

## 注意事项

1. 确保正确处理初始状态,考虑使用`skip(1)`避免初始值触发不必要的操作
2. 所有Controller必须实现`destroy`方法并确保资源正确释放
3. 使用可选类型(如`Controller | null`)配合空值处理,提高代码健壮性
4. 确保状态订阅中正确处理异常,避免因错误导致整个流中断
5. 在页面级组件使用`useController` Hook创建控制器,在容器组件通过props接收控制器 