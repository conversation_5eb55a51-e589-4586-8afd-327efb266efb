export interface ContractTemplateSection {
  title: string;
  introduction?: string;
  content: string[];
}

export interface ContractTemplate {
  contract_metadata: {
    file_name: string;
    contract_code: string;
    contract_type: string;
    contract_version: string;
    description: string;
    summary: string;
  };
  metadata_block: {
    llm_group_id: string;
    contract_title: string;
    parties: { [key: string]: string }[];
    signing_date: string;
  };
  main_content_block: {
    llm_group_id: string;
    sections: ContractTemplateSection[];
  };
  standard_clauses_block?: {
    llm_group_id: string;
    sections: ContractTemplateSection[];
  };
  attachments_block?: {
    llm_group_id: string;
    sections: ContractTemplateSection[];
  };
}
