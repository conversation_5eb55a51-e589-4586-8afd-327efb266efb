import { Injectable, Logger, OnModuleInit } from '@nestjs/common';

import * as fs from 'fs';
import * as path from 'path';

import { ContractTemplate } from './interfaces/contract-template.interface';

@Injectable()
export class ContractTemplatesService implements OnModuleInit {
  private readonly logger = new Logger(ContractTemplatesService.name);
  private readonly refersDir = path.resolve(__dirname, '../../data/refers');
  private templates: ContractTemplate[] = [];

  onModuleInit() {
    this.loadTemplates();
  }

  private loadTemplates() {
    if (!fs.existsSync(this.refersDir)) {
      this.logger.error(`模板目录不存在: ${this.refersDir}`);
      this.templates = [];
      return;
    }
    const files = fs.readdirSync(this.refersDir).filter((f) => f.endsWith('.json'));
    this.templates = files
      .map((filename) => {
        try {
          const filePath = path.join(this.refersDir, filename);
          const content = fs.readFileSync(filePath, 'utf-8');
          const data = JSON.parse(content);
          data.template_filename = filename;
          return data;
        } catch (e) {
          this.logger.error(`加载模板失败: ${filename}`, e);
          return null;
        }
      })
      .filter(Boolean) as ContractTemplate[];
    this.logger.log(`已加载合同模板 ${this.templates.length} 个`);
  }

  getAllTemplates(): ContractTemplate[] {
    return this.templates;
  }

  getTemplateByContractCode(contractCode: string): ContractTemplate | undefined {
    return this.templates.find((t) => t.contract_metadata.contract_code === contractCode);
  }
}
