import { DatapkgService } from '@graphql/datapkg';
import { ComDatapkg } from '@graphqlCom/datapkg';
import { Injectable } from '@nestjs/common';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';

@Injectable()
export class DatapkgLoader extends NestDataLoaderFactory<string, ComDatapkg> {
  constructor(private readonly datapkgService: DatapkgService) {
    super();
  }

  protected loaderQuery(ids: string[]) {
    return this.datapkgService.findAll({ id: ids });
  }
}
