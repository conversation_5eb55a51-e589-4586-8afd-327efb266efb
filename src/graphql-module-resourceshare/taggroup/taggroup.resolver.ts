import { TaggroupService } from '@graphql/taggroup';
import { ComTaggroup, ComTaggroupResolver } from '@graphqlCom/taggroup';
import { Resolver } from '@nestjs/graphql';
import { ProductPrefixEnum } from '@comm/constants';

@Resolver(() => ComTaggroup)
export class TaggroupResolver extends ComTaggroupResolver(
  ComTaggroup,
  ProductPrefixEnum.RESOURCE_SHARE,
) {
  constructor(protected readonly taggroupService: TaggroupService) {
    super(taggroupService);
  }
}
