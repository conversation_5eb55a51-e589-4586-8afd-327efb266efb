import { GraphService } from '@graphql/graph';
import { ComGraph, ComGraphResolver } from '@graphqlCom/graph';
import { Resolver } from '@nestjs/graphql';
import { ProductPrefixEnum } from '@comm/constants';

@Resolver(() => ComGraph)
export class GraphResolver extends ComGraphResolver(ComGraph, ProductPrefixEnum.ONETABLE) {
  constructor(protected readonly graphService: GraphService) {
    super(graphService);
  }
}
