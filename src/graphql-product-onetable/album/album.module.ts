import { Module } from '@nestjs/common';
import {
  AlbumDatapkgsResolver,
  AlbumPkgStatsResolver,
  AlbumResolver,
  TaggroupTagsModelResolver,
} from './album.resolver';

@Module({
  providers: [
    AlbumResolver,
    AlbumPkgStatsResolver,
    AlbumDatapkgsResolver,
    TaggroupTagsModelResolver,
  ],
  exports: [AlbumResolver, AlbumPkgStatsResolver, AlbumDatapkgsResolver, TaggroupTagsModelResolver],
})
export class AlbumModule {}
