import { TicketService } from '@graphql/ticket';
import { ComTicket, ComTicketResolver } from '@graphqlCom/ticket';
import { Resolver } from '@nestjs/graphql';
import { ProductPrefixEnum } from '@comm/constants';

@Resolver(() => ComTicket)
export class TicketResolver extends ComTicketResolver(ComTicket, ProductPrefixEnum.ONETABLE) {
  constructor(protected readonly ticketService: TicketService) {
    super(ticketService);
  }
}
