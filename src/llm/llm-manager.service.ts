import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { OpenAI } from 'openai';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

/**
 * LLM调用参数接口
 */
export interface LLMOptions {
  model: string;
  temperature?: number;
  maxTokens?: number;
  jsonMode?: boolean;
  systemPrompt?: string;
  parseJson?: boolean;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  seed?: number;
  stream?: boolean;
  tools?: any[];
  toolChoice?: string | any;
  user?: string;
  removeThink?: boolean;
  [key: string]: any; // 允许其他自定义参数
}

@Injectable()
export class LlmManagerService {
  private readonly logger = new Logger(LlmManagerService.name);
  private client: OpenAI;

  public constructor(private readonly configService: ConfigService) {
    this.client = new OpenAI({
      baseURL: this.configService.get<string>('LLM_API_BASE_URL', ''),
      apiKey: this.configService.get<string>('LLM_API_KEY', ''),
    });
  }

  /**
   * 通用的LLM调用方法，支持更灵活的参数配置
   * @param prompt 提示词内容
   * @param options 可选参数配置
   * @param requestOptions 请求头选项
   * @returns LLM响应内容
   */
  public async callLLMUseOpenAISDK<T>(
    prompt: string,
    options: LLMOptions,
    requestOptions?: OpenAI.RequestOptions,
  ): Promise<ResultResponse<T>> {
    if (!prompt) {
      return ResultUtil.fail('LLM调用缺少提示词内容', 'LLM_MISSING_PROMPT');
    }

    return ResultUtil.execute(async () => {
      // 处理请求参数
      const requestParams = this.buildLLMRequestParams(prompt, options);

      // 发送请求
      this.logger.log(`发送LLM调用请求: ${JSON.stringify(requestParams, null, 2)}`);

      const response = await this.client.chat.completions.create(
        requestParams as any,
        requestOptions,
      );
      this.logger.debug(`LLM调用响应: ${JSON.stringify(response, null, 2)}`);

      return this.processLLMResponse(response, options) as ResultResponse<T>;
    }, 'LLM调用失败');
  }

  /**
   * 构建LLM请求参数
   * @private
   */
  private buildLLMRequestParams(prompt: any, options: LLMOptions): Record<string, any> {
    const messages: Record<string, any>[] = [];
    // 构建请求参数
    const params: Record<string, any> = { model: options.model, messages };

    // 添加系统提示
    if (options.systemPrompt) {
      messages.push({ role: 'system', content: options.systemPrompt });
    }

    // 添加用户提示
    messages.push({ role: 'user', content: prompt });

    // 添加可选参数
    if (options.temperature !== undefined) {
      params.temperature = options.temperature;
    }

    if (options.maxTokens !== undefined) {
      params.max_tokens = options.maxTokens;
    }

    if (options.jsonMode !== undefined) {
      params.response_format = { type: options.jsonMode ? 'json_object' : 'text' };
    }

    if (options.topP !== undefined) {
      params.top_p = options.topP;
    }

    if (options.frequencyPenalty !== undefined) {
      params.frequency_penalty = options.frequencyPenalty;
    }

    if (options.presencePenalty !== undefined) {
      params.presence_penalty = options.presencePenalty;
    }

    if (options.stopSequences !== undefined) {
      params.stop = options.stopSequences;
    }

    if (options.seed !== undefined) {
      params.seed = options.seed;
    }

    // 流式响应
    if (options.stream !== undefined) {
      params.stream = options.stream;
    }

    // 工具调用
    if (options.tools && options.tools.length > 0) {
      params.tools = options.tools;
    }

    // 工具选择
    if (options.toolChoice !== undefined) {
      params.tool_choice = options.toolChoice;
    }

    // 用户标识
    if (options.user !== undefined) {
      params.user = options.user;
    }

    return params;
  }

  /**
   * 处理LLM响应
   * @private
   */
  private processLLMResponse(response: any, options: LLMOptions): ResultResponse<any> {
    let content = response.choices[0].message.content;

    // 移除<think>...</think>标签内容
    options.removeThink && (content = (content || '').replace(/<think>[\s\S]*?<\/think>/gi, ''));

    // 如果需要解析JSON
    if (options.parseJson && content) {
      return this.parseLLMResponseToJson<any>(content);
    }

    // 返回完整的响应数据（如果有工具调用等特殊情况）
    if (options.tools || options.stream) {
      content = response;
    }

    return ResultUtil.success(content);
  }

  private parseLLMResponseToJson<T>(content: string): ResultResponse<T> {
    try {
      // 直接尝试解析完整内容
      const result = JSON.parse(content);
      return ResultUtil.success(result as T);
    } catch (jsonError) {
      this.logger.debug(`JSON解析失败,尝试从文本中提取`);

      // 尝试补全缺失的右大括号
      let fixedContent = content;
      if (fixedContent.trim().startsWith('{') && !fixedContent.trim().endsWith('}')) {
        fixedContent = fixedContent.trim() + '}';
        try {
          const result = JSON.parse(fixedContent);
          this.logger.warn('JSON缺失右大括号，已自动补全');
          return ResultUtil.success(result as T);
        } catch {}
      }

      // 首先尝试提取 ```json 中的内容
      const codeBlockMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        try {
          const result = JSON.parse(codeBlockMatch[1]);
          return ResultUtil.success(result as T);
        } catch (codeBlockError) {
          this.logger.warn(`代码块内容解析JSON失败: ${codeBlockMatch[1]}`);
        }
      }

      // 如果代码块提取失败，使用更通用的JSON匹配
      const jsonRegex = /(\{[\s\S]*\}|\[[\s\S]*\])/;
      const jsonMatch = content.match(jsonRegex);
      const extractedJson = jsonMatch && jsonMatch[0];

      if (extractedJson) {
        try {
          const result = JSON.parse(extractedJson);
          return ResultUtil.success(result as T);
        } catch (extractError) {
          // 尝试补全为对象
          const trimmed = extractedJson.trim();
          if (!trimmed.startsWith('{') && !trimmed.startsWith('[')) {
            try {
              const fixed = '{' + trimmed.replace(/^[,\\s]+|[,\\s]+$/g, '') + '}';
              const result = JSON.parse(fixed);
              this.logger.warn('extractedJson 缺少外层大括号，已自动补全');
              return ResultUtil.success(result as T);
            } catch {}
          }
          this.logger.warn(`提取的内容解析JSON失败: ${extractedJson}`);
          return ResultUtil.fail('提取的内容无法解析为有效JSON', 'INVALID_JSON_FORMAT');
        }
      }

      this.logger.warn('无法从响应中找到有效的JSON结构');
      return ResultUtil.fail('响应内容不包含有效的JSON格式', 'NO_JSON_FOUND');
    }
  }
}
