import { EmptyResponseUnionType } from '@graphql/graphql.model';
import { RoleBaseResolver, RoleService } from '@graphql/role';
import {
  CreateRoleInput,
  QueryRolesParams,
  QueryUsersOfRolesParams,
  RoleTypeEnum,
  UpdateRoleInput,
  UpdateUsersOfRoleInput,
} from '@graphql/role/role.dto';
import { UpdateSharedResourcePsInput } from '@graphql/user';
import { Type } from '@nestjs/common';
import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import {
  ComQueryRolesUnionType,
  ComRole,
  ComRoleResourcePermissionUnionType,
  ComUsersOfRolesUnionType,
} from './role.model';

export function ComRoleResolver<T extends Type<ComRole>>(classRef: T, prefix: string): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class ComResolver extends RoleBaseResolver(ComRole) {
    constructor(private readonly roleService: RoleService) {
      super();
    }

    // 请求-------------

    @Query(() => ComQueryRolesUnionType, {
      description: '查询角色信息',
      name: `${prefix}Roles`,
    })
    async queryRoles(
      @Args('params', { type: () => QueryRolesParams, nullable: true }) params?: QueryRolesParams,
    ) {
      return this.roleService.queryRoles(params);
    }

    @Query(() => ComQueryRolesUnionType, {
      description: '查询机构信息',
      name: `${prefix}Orgs`,
    })
    async queryOrgs(
      @Args('params', { type: () => QueryRolesParams, nullable: true }) params?: QueryRolesParams,
    ) {
      return this.roleService.queryRoles({ ...params, role_type: RoleTypeEnum.ORGANIZATION });
    }

    @Query(() => ComQueryRolesUnionType, {
      description: '查询群组信息',
      name: `${prefix}Groups`,
    })
    async queryGroups(
      @Args('params', { type: () => QueryRolesParams, nullable: true }) params?: QueryRolesParams,
    ) {
      return this.roleService.queryRoles({ ...params, role_type: RoleTypeEnum.GROUP });
    }

    @Query(() => ComUsersOfRolesUnionType, {
      description: '根据角色获取用户列表',
      name: `${prefix}UsersOfRoles`,
    })
    async queryUsersOfRoles(
      @Args('params', { type: () => QueryUsersOfRolesParams }) params?: QueryUsersOfRolesParams,
    ) {
      return this.roleService.queryUsersOfRoles(params);
    }

    @Query(() => ComRoleResourcePermissionUnionType, {
      description: '获取角色下的资源权限',
      name: `${prefix}SharedResourcePermissionsOfRole`,
    })
    async querySharedResourcePermissionsOfRole(
      @Args('appId', { type: () => Int }) appId: number,
      @Args('roleId', { type: () => Int }) roleId: number,
    ) {
      return this.roleService.querySharedResourcePermissionsOfRole(appId, roleId);
    }

    @Mutation(() => EmptyResponseUnionType, {
      description: '批量删除角色、机构、群组',
      name: `${prefix}DeleteRoles`,
    })
    async deleteRoles(@Args('roleIds', { description: '多个角色以逗号分割' }) roleIds: string) {
      return this.roleService.deleteRoles(roleIds);
    }

    @Mutation(() => EmptyResponseUnionType, {
      description: '修改角色',
      name: `${prefix}UpdateRole`,
    })
    async updateRole(@Args('data') data: UpdateRoleInput) {
      return this.roleService.updateRole(data);
    }

    @Mutation(() => EmptyResponseUnionType, {
      description: '修改角色下关联的用户',
      name: `${prefix}UpdateUsersOfRole`,
    })
    async updateUsersOfRole(@Args('data') data: UpdateUsersOfRoleInput) {
      return this.roleService.updateUsersOfRole(data);
    }

    @Mutation(() => EmptyResponseUnionType, {
      description: '新建角色',
      name: `${prefix}CreateRole`,
    })
    async createRole(@Args('data') data: CreateRoleInput) {
      return this.roleService.createRole(data);
    }

    @Mutation(() => EmptyResponseUnionType, {
      description: '修改角色资源权限',
      name: `${prefix}UpdateRoleResourcePs`,
    })
    async updateRoleResourcePs(
      @Args('roleId', { type: () => Int }) roleId: number,
      @Args('data', { type: () => UpdateSharedResourcePsInput })
      data: UpdateSharedResourcePsInput,
    ) {
      return this.roleService.updateRoleResourcePs(roleId, data);
    }
  }

  return ComResolver;
}
