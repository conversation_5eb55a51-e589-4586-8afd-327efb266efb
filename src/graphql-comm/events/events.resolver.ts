import {
  CreateEventsInput,
  DeleteEventsInput,
  EventsBaseResolver,
  EventsService,
} from '@graphql/events';
import { Type } from '@nestjs/common';
import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { ComEvents, ComEventsResponseUnionType } from './events.model';

export function ComEventsResolver<T extends Type<ComEvents>>(classRef: T, prefix: string): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class ComResolver extends EventsBaseResolver(ComEvents) {
    constructor(private readonly eventsService: EventsService) {
      super();
    }

    @Mutation(() => ComEventsResponseUnionType, {
      description: '新建事件',
      name: `${prefix}EventCreate`,
    })
    public eventCreate(
      @Args('data', { type: () => CreateEventsInput, nullable: false }) data: CreateEventsInput,
    ) {
      return this.eventsService.createEvents(data);
    }

    @Mutation(() => ComEventsResponseUnionType, {
      description: '删除事件',
      name: `${prefix}EventDelete`,
    })
    public eventDelete(
      @Args('data', { type: () => DeleteEventsInput, nullable: false }) data: DeleteEventsInput,
    ) {
      return this.eventsService.deleteEvents(data);
    }
  }

  return ComResolver;
}
