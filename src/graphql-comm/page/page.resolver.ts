import {
  CreatePageInput,
  PageBaseResolver,
  PageService,
  QueryPagesParams,
  UpdatePageInput,
} from '@graphql/page';
import { Type } from '@nestjs/common';
import { Args, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import {
  ComCreatePageUnionType,
  ComDeletePageUnionType,
  ComPage,
  ComPageUnionType,
  ComPaginatedPageUnionType,
  ComUpdatePageUnionType,
} from './page.model';

export function ComPageResolver<T extends Type<ComPage>>(classRef: T, prefix: string): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class ComResolver extends PageBaseResolver(ComPage) {
    constructor(protected readonly pageService: PageService) {
      super();
    }

    @ResolveField(() => String, { description: 'test', nullable: true })
    async test(@Parent() page: ComPage) {
      return page.id;
    }

    // ----------- Query ---------

    @Query(() => ComPaginatedPageUnionType, {
      description: 'page首页',
      name: `${prefix}PageFirstPage`,
    })
    async queryPageFirstPage(
      @Args('params', { type: () => QueryPagesParams, nullable: true })
      params?: QueryPagesParams,
    ) {
      const config = { needMetadata: true };
      return this.pageService.queryPages({ fetch_total_count: true, ...params }, config);
    }

    @Query(() => ComPaginatedPageUnionType, {
      description: 'page下一页',
      name: `${prefix}PageNextPage`,
    })
    async queryPageNextPage(
      @Args('params', { type: () => QueryPagesParams, nullable: true })
      params?: QueryPagesParams,
    ) {
      return this.pageService.queryPages(params);
    }

    @Query(() => ComPageUnionType, {
      description: 'page详情',
      name: `${prefix}PageDetail`,
    })
    async querypageDetail(@Args('id') id: string) {
      return this.pageService.queryPage(id);
    }

    // ----------- Mutation ---------

    @Mutation(() => ComCreatePageUnionType, {
      description: '创建page',
      name: `${prefix}CreatePage`,
    })
    async createPage(
      @Args('data', { type: () => CreatePageInput })
      data: CreatePageInput,
    ) {
      return this.pageService.createPage(data);
    }

    @Mutation(() => ComUpdatePageUnionType, {
      description: '修改page',
      name: `${prefix}UpdatePage`,
    })
    async updatePage(
      @Args('data', { type: () => UpdatePageInput })
      data: UpdatePageInput,
    ) {
      return this.pageService.updatePage(data);
    }

    @Mutation(() => ComDeletePageUnionType, {
      description: '删除page',
      name: `${prefix}DeletePage`,
    })
    async deletePage(@Args('id') id: string) {
      return this.pageService.deletePage(id);
    }
  }

  return ComResolver;
}
