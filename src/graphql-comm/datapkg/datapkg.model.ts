import {
  Datapkg,
  DatapkgChecklog,
  DatapkgCollaborate,
  DatapkgColumn,
  DatapkgConstraint,
  DatapkgConstraintDelete,
  DatapkgConstraintListDelete,
  DatapkgDdlElement,
  DatapkgGenealogy,
  DatapkgRows,
  DatapkgsDelete,
  PkgBoundWfspec,
} from '@graphql/datapkg';
import {
  DatapkgIdModel,
  getResponseUnionType,
  ResponseData,
  ResponsePaginationData,
} from '@graphql/graphql.model';
import { createUnionType, ObjectType } from '@nestjs/graphql';

// 需要单独的model 不然根model也会新增ResolveField
@ObjectType()
export class ComDatapkg extends Datapkg {}

// 数据包列表
@ObjectType()
class ComPaginatedDatapkgResponse extends ResponsePaginationData<ComDatapkg>(ComDatapkg) {}
export const ComDatapkgListUnionType = createUnionType(
  getResponseUnionType(ComPaginatedDatapkgResponse),
);

// 数据包详情
@ObjectType()
class ComDatapkgResponse extends ResponseData<ComDatapkg>(ComDatapkg) {}
export const ComDatapkgResponseUnionType = createUnionType(
  getResponseUnionType(ComDatapkgResponse),
);

// 数据包列信息
@ObjectType()
export class ComDatapkgColumn extends DatapkgColumn {}
@ObjectType()
class ComDatapkgColumnListResponse extends ResponseData<ComDatapkgColumn>(ComDatapkgColumn, true) {}
export const ComDatapkgColumnListResponseUnionType = createUnionType(
  getResponseUnionType(ComDatapkgColumnListResponse),
);

// 数据包DDL信息
@ObjectType()
export class ComDatapkgDdlElement extends DatapkgDdlElement {}
@ObjectType()
class ComDatapkgDdlElementListResponse extends ResponseData<ComDatapkgDdlElement>(
  ComDatapkgDdlElement,
  true,
) {}
export const ComDatapkgDdlElementListResponseUnionType = createUnionType(
  getResponseUnionType(ComDatapkgDdlElementListResponse),
);

// 数据包预览数据
@ObjectType()
export class ComDatapkgRows extends DatapkgRows {}
@ObjectType()
class ComDatapkgPreviewDataResponse extends ResponseData<ComDatapkgRows>(ComDatapkgRows) {}
export const ComDatapkgPreviewDataResponseUnionType = createUnionType(
  getResponseUnionType(ComDatapkgPreviewDataResponse),
);

// 数据包检索数据
@ObjectType()
class ComPaginatedDatapkgRowsResponse extends ResponsePaginationData<ComDatapkgRows>(
  ComDatapkgRows,
  false,
) {}
export const ComPaginatedDatapkgRowsResponseUnionType = createUnionType(
  getResponseUnionType(ComPaginatedDatapkgRowsResponse),
);

// 更新数据包富文本描述
@ObjectType()
class ComUpdateDatapkgsMetaResponse extends ResponseData<any>(Boolean) {}
export const ComUpdateDatapkgMetaUnionType = createUnionType(
  getResponseUnionType(ComUpdateDatapkgsMetaResponse),
);

// 更新数据包元数据
@ObjectType()
class ComUpdateDatapkgResponse extends ResponseData<any>(ComDatapkg) {}
export const ComUpdateDatapkgUnionType = createUnionType(
  getResponseUnionType(ComUpdateDatapkgResponse),
);

// 删除多个数据包
@ObjectType()
class ComDeleteDatapkgsResponse extends ResponseData<any>(DatapkgsDelete) {}
export const ComDeleteDatapkgsUnionType = createUnionType(
  getResponseUnionType(ComDeleteDatapkgsResponse),
);

// 删除多个数据包
@ObjectType()
class ComRefreshDatapkgResponse extends ResponseData<any>(ComDatapkg) {}
export const ComRefreshDatapkgUnionType = createUnionType(
  getResponseUnionType(ComRefreshDatapkgResponse),
);

// 数据包血缘图
@ObjectType()
export class ComDatapkgGenealogy extends DatapkgGenealogy {}
@ObjectType()
class ComDatapkgGenealogyListResponse extends ResponseData<ComDatapkgGenealogy>(
  ComDatapkgGenealogy,
  true,
) {}
export const ComDatapkgGenealogyListUnionType = createUnionType(
  getResponseUnionType(ComDatapkgGenealogyListResponse),
);

// 更新数据包单列信息
@ObjectType()
class ComUpdateDatapkgColumnResponse extends ResponseData<ComDatapkgColumn>(ComDatapkgColumn) {}
export const ComUpdateDatapkgColumnUnionType = createUnionType(
  getResponseUnionType(ComUpdateDatapkgColumnResponse),
);
// 更新数据包多列信息
@ObjectType()
class ComUpdateDatapkgColumnListResponse extends ResponseData<any>(Boolean) {}
export const ComUpdateDatapkgColumnListUnionType = createUnionType(
  getResponseUnionType(ComUpdateDatapkgColumnListResponse),
);

// 新增一列
@ObjectType()
class ComCreateDatapkgColumnResponse extends ResponseData<DatapkgColumn>(DatapkgColumn) {}
export const ComCreateDatapkgColumnUnionType = createUnionType(
  getResponseUnionType(ComCreateDatapkgColumnResponse),
);

// 数据包约束列表
@ObjectType()
class ComDatapkgConstraintListResponse extends ResponseData<DatapkgConstraint>(
  DatapkgConstraint,
  true,
) {}
export const ComDatapkgConstraintListUnionType = createUnionType(
  getResponseUnionType(ComDatapkgConstraintListResponse),
);

// 新增、修改约束
@ObjectType()
class ComDatapkgConstraintResponse extends ResponseData<DatapkgConstraint>(DatapkgConstraint) {}
export const ComDatapkgConstraintUnionType = createUnionType(
  getResponseUnionType(ComDatapkgConstraintResponse),
);

// 删除数据包所有约束
@ObjectType()
class ComDatapkgConstraintListDeleteResponse extends ResponseData<DatapkgConstraintListDelete>(
  DatapkgConstraintListDelete,
) {}
export const ComDatapkgConstraintListDeleteUnionType = createUnionType(
  getResponseUnionType(ComDatapkgConstraintListDeleteResponse),
);

// 删除数据包单个约束
@ObjectType()
class ComDatapkgConstraintDeleteResponse extends ResponseData<DatapkgConstraintDelete>(
  DatapkgConstraintDelete,
) {}
export const ComDatapkgConstraintDeleteUnionType = createUnionType(
  getResponseUnionType(ComDatapkgConstraintDeleteResponse),
);

// 数据包约束列表
@ObjectType()
class ComDatapkgChecklogListResponse extends ResponseData<DatapkgChecklog>(DatapkgChecklog, true) {}
export const ComDatapkgChecklogListUnionType = createUnionType(
  getResponseUnionType(ComDatapkgChecklogListResponse),
);

// 数据包关联多个主题库
@ObjectType()
class ComDatapkgBindAlbumsResponse extends ResponseData<DatapkgIdModel>(DatapkgIdModel) {}
export const ComDatapkgBindAlbumsUnionType = createUnionType(
  getResponseUnionType(ComDatapkgBindAlbumsResponse),
);

// 数据包取消关联多个主题库
@ObjectType()
class ComDatapkgUnbindAlbumsResponse extends ComDatapkgBindAlbumsResponse {}
export const ComDatapkgUnbindAlbumsUnionType = createUnionType(
  getResponseUnionType(ComDatapkgUnbindAlbumsResponse),
);

// 删除协同数据包
@ObjectType()
class ComDeleteDatapkgCollaborateResponse extends ResponseData(ComDatapkg) {}
export const ComDeleteDatapkgCollaborateUnionType = createUnionType(
  getResponseUnionType(ComDeleteDatapkgCollaborateResponse),
);
// 查询协同数据包
@ObjectType()
class ComQueryDatapkgCollaborateResponse extends ResponseData(DatapkgCollaborate) {}
export const ComQueryDatapkgCollaborateUnionType = createUnionType(
  getResponseUnionType(ComQueryDatapkgCollaborateResponse),
);
// 创建协同数据包
@ObjectType()
class ComCreateDatapkgCollaborateResponse extends ResponseData(ComDatapkg) {}
export const ComCreateDatapkgCollaborateUnionType = createUnionType(
  getResponseUnionType(ComCreateDatapkgCollaborateResponse),
);
// 更新协同数据包
@ObjectType()
class ComUpdateDatapkgCollaborateResponse extends ResponseData(ComDatapkg) {}
export const ComUpdateDatapkgCollaborateUnionType = createUnionType(
  getResponseUnionType(ComUpdateDatapkgCollaborateResponse),
);
@ObjectType()
class ComPkgBoundWfspecResponse extends ResponseData(PkgBoundWfspec) {}
export const ComPkgBoundWfspecUnionType = createUnionType(
  getResponseUnionType(ComPkgBoundWfspecResponse),
);

// 数据包更新数据
@ObjectType()
class ComDatapkgUpdateRowsResponse extends ResponseData<ComDatapkgRows>(ComDatapkgRows) {}
export const ComDatapkgUpdateRowsResponseUnionType = createUnionType(
  getResponseUnionType(ComDatapkgUpdateRowsResponse),
);
