import { EntityService, EntityTypeEnum } from '@graphql/entity';
import { Type } from '@nestjs/common';
import { Args, Int, Query, Resolver } from '@nestjs/graphql';
import { ComEntity, ComEntityNameResponseUnionType } from './entity.model';

export function ComEntityResolver<T extends Type<ComEntity>>(classRef: T, prefix: string): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class ComResolver {
    constructor(private readonly entityService: EntityService) {}

    @Query(() => ComEntityNameResponseUnionType, {
      description: '根据用户id列表获取name',
      name: `${prefix}EntityUserNames`,
    })
    public entityUserName(@Args('ids', { type: () => [Int] }) ids: number[]) {
      return this.entityService.findAll({
        entityType: EntityTypeEnum.USER,
        data: {
          ids,
        },
      });
    }

    @Query(() => ComEntityNameResponseUnionType, {
      description: '根据app id列表获取name',
      name: `${prefix}EntityAppNames`,
    })
    public entityAppName(@Args('ids', { type: () => [String] }) ids: string[]) {
      return this.entityService.findAll({
        entityType: EntityTypeEnum.APP,
        data: {
          ids,
        },
      });
    }

    @Query(() => ComEntityNameResponseUnionType, {
      description: '根据role id列表获取name',
      name: `${prefix}EntityRoleNames`,
    })
    public entityRoleName(@Args('ids', { type: () => [String] }) ids: string[]) {
      return this.entityService.findAll({
        entityType: EntityTypeEnum.ROLE,
        data: {
          ids,
        },
      });
    }
  }

  return ComResolver;
}
