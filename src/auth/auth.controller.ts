import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';

import { Request } from 'express';

import { ResultResponse } from '../common/utils/result.util';

import { AuthService } from './auth.service';
import { LoginResponseDto } from './dto/login-response-dto';
import { LoginUserDto } from './dto/login-user.dto';
import { LoginDto } from './dto/login.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

/**
 * 认证控制器
 */
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * 用户登录
   * @param loginDto 登录信息
   */
  @Post('login')
  async login(@Body() loginDto: LoginDto): Promise<ResultResponse<LoginResponseDto>> {
    return this.authService.login(loginDto.email, loginDto.password);
  }

  /**
   * 用户登出
   */
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  async logout(): Promise<ResultResponse> {
    return this.authService.logout();
  }

  /**
   * 获取用户信息
   */
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getUserInfo(
    @Req() req: Request & { user: { userId: number } },
  ): Promise<ResultResponse<LoginUserDto>> {
    return this.authService.getUserInfo(req.user.userId);
  }
}
