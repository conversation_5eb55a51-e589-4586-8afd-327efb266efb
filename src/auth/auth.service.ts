import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';
import { UsersService } from '../users/users.service';

import { LoginResponseDto } from './dto/login-response-dto';
import { LoginUserDto } from './dto/login-user.dto';

/**
 * 认证服务
 */
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  /**
   * 使用邮箱/用户名和密码进行登录
   * @param email 邮箱或用户名
   * @param password 密码
   * @returns 登录结果，包含token和用户信息
   */
  async login(email: string, password: string): Promise<ResultResponse<LoginResponseDto>> {
    // 参数在controller中已经验证
    // 使用UsersService验证用户
    const userResult = await this.usersService.validateUser(email, password);
    if (!userResult.success) {
      return userResult as ResultResponse;
    }

    const user = userResult.data;

    // 生成JWT令牌
    const payload = { sub: user.id };
    const access_token = this.jwtService.sign(payload);

    // 返回登录结果
    return ResultUtil.success<LoginResponseDto>(
      {
        access_token,
        user: { id: user.id, email: user.email, username: user.username },
      },
      '登录成功',
    );
  }

  /**
   * 用户登出
   */
  async logout(): Promise<ResultResponse> {
    return ResultUtil.success(undefined, '登出成功');
  }

  /**
   * 获取用户信息
   * @param userId 用户ID
   * @returns 用户信息
   */
  async getUserInfo(userId: number): Promise<ResultResponse<LoginUserDto>> {
    // 根据用户ID获取用户信息
    const userResult = await this.usersService.findById(userId);
    if (!userResult.success) {
      return userResult as ResultResponse;
    }

    const user = userResult.data;

    // 转换为LoginUserDto
    const userDto: LoginUserDto = {
      id: user.id,
      email: user.email,
      username: user.username,
    };

    return ResultUtil.success(userDto, '获取用户信息成功');
  }
}
