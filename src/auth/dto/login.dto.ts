import { IsNotEmpty, IsString, MinLength } from 'class-validator';

/**
 * 登录数据传输对象
 */
export class LoginDto {
  /**
   * 用户邮箱或用户名
   * @example "<EMAIL>"
   */
  @IsNotEmpty({ message: '邮箱或用户名不能为空' })
  @IsString({ message: '邮箱或用户名必须是字符串' })
  email: string;

  /**
   * 用户密码
   * @example "password123"
   */
  @IsNotEmpty({ message: '密码不能为空' })
  @MinLength(6, { message: '密码长度不能小于6位' })
  password: string;
}
