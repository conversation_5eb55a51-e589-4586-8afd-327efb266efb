import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';

import { UsersService } from '../../users/users.service';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(private usersService: UsersService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const userId = request.user?.userId;

    if (!userId) {
      throw new UnauthorizedException('用户未登录');
    }

    const userResult = await this.usersService.findById(userId);
    if (!userResult.success) {
      // 屏蔽错误提示泄露信息
      throw new UnauthorizedException('用户无权限');
    }

    const user = userResult.data;
    if (!user.isAdmin) {
      // 屏蔽错误提示泄露信息
      throw new UnauthorizedException('用户无权限');
    }

    return true;
  }
}
