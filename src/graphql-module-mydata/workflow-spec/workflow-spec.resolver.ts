import { WorkflowSpecService } from '@graphql/workflow-spec';
import { ComWorkflowSpec, ComWorkflowSpecResolver } from '@graphqlCom/workflow-spec';
import { Resolver } from '@nestjs/graphql';
import { ProductPrefixEnum } from '@comm/constants';

@Resolver(() => ComWorkflowSpec)
export class WorkflowSpecResolver extends ComWorkflowSpecResolver(
  ComWorkflowSpec,
  ProductPrefixEnum.MY_DATA,
) {
  constructor(protected readonly workflowSpecService: WorkflowSpecService) {
    super(workflowSpecService);
  }
}
