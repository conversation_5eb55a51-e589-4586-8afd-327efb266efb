import { Injectable } from '@nestjs/common';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';
import { AlbumPkgStats } from './album.model';
import { AlbumService } from './album.service';

@Injectable()
export class AlbumPkgStatsLoader extends NestDataLoaderFactory<string, AlbumPkgStats> {
  constructor(private readonly albumService: AlbumService) {
    super('album_id');
  }

  protected async loaderQuery(ids: string[]) {
    return this.albumService.findAlbumPkgStats({ album_ids: ids });
  }
}
