import { Field, Int, ObjectType, PartialType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { CreateDatasetInput } from './dataset.dto';

export type IRowValue = string | number | boolean | object | null | undefined;
@ObjectType()
export class Dataset extends PartialType(CreateDatasetInput, ObjectType) {
  @Field({ description: '数据源ID' })
  id: string;

  @Field(() => Int, { description: '数据源类型', nullable: true })
  role?: number;
}

// 数据源下数据包 --------------
@ObjectType()
export class DatasetDatapkgIds {
  @Field(() => [String], { description: '数据包id列表', nullable: false })
  datapkg_ids: string[];
}

@ObjectType()
export class DatasetRows {
  @Field(() => [String])
  columns: string[];

  // 有些特殊的表类型中的元素可能为null eg: types: [null, "array_str", "str", "str"]
  @Field(() => [String], { nullable: 'items' })
  types: string[];

  @Field(() => GraphQLJSON)
  values: IRowValue[][];
}

export type IDatasetTabelType = 'table' | 'view';

@ObjectType()
export class DatasetTable {
  @Field()
  name: string;

  @Field()
  schema: string;

  @Field(() => String)
  type: IDatasetTabelType;
}

@ObjectType()
export class DatasetId {
  @Field()
  dataset_id: string;
}
