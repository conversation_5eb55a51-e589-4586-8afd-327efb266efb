import { Field, InputType, Int, OmitType, PartialType } from '@nestjs/graphql';

export enum RoleTypeEnum {
  ORGANIZATION = 'organization',
  NORMAL = 'normal',
  GROUP = 'group',
}

@InputType()
export class QueryRolesParams {
  @Field(() => Int, { nullable: true })
  app_id?: number;

  @Field(() => String, { nullable: true })
  role_type?: string;

  @Field(() => String, { nullable: true })
  role_ids?: string;

  @Field(() => String, { nullable: true })
  tags?: string;

  @Field(() => Boolean, { nullable: true })
  user_must_in?: boolean;
}

@InputType()
export class QueryUsersOfRolesParams extends OmitType(QueryRolesParams, ['role_ids', 'tags']) {
  @Field(() => String, { nullable: true })
  role_ids: string;
}

@InputType()
export class CreateRoleInput {
  @Field(() => String, { nullable: true })
  name: string;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => [Int], { nullable: true })
  permission?: number[];

  @Field(() => [Int], { nullable: true })
  parent_id?: number[];

  @Field(() => Int, { nullable: true })
  app_id?: number;

  @Field(() => [String], { description: '角色所属标签', nullable: 'itemsAndList' })
  tags?: string[];

  @Field(() => [String], { description: '角色关联的用户', nullable: 'itemsAndList' })
  users?: string[];

  @Field(() => String, { nullable: true })
  role_type?: string;
}

@InputType()
export class UpdateRoleInput extends PartialType(CreateRoleInput) {
  @Field()
  id: number;

  @Field(() => [Int], { nullable: true })
  new_permission?: number[];

  @Field(() => [Int], { nullable: true })
  del_permission?: number[];
}

@InputType()
export class UpdateUsersOfRoleInput {
  @Field()
  app_id: number;

  @Field()
  role_id: number;

  @Field(() => [String], { nullable: true })
  new_users?: string[];

  @Field(() => [String], { nullable: true })
  del_users?: string[];

  @Field(() => [String], { nullable: true })
  new_user_ids?: string[];

  @Field(() => [String], { nullable: true })
  del_user_ids?: string[];
}
