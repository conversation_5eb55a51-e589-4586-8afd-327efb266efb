import { RequestRequestConfig } from '@graphql/graphql.dto';
import { Injectable } from '@nestjs/common';
import { AxiosService } from '@globalModule/axios';
import { ConfigService } from '@globalModule/config';
import { CreatePageInput, QueryPagesParams, UpdatePageInput } from './page.dto';
import { Page } from './page.model';

@Injectable()
export class PageService {
  constructor(private readonly axios: AxiosService, private readonly config: ConfigService) {}

  // page列表
  async queryPages(params: QueryPagesParams, config?: RequestRequestConfig) {
    return this.axios.request<Page[]>({
      ...(config || {}),
      method: 'get',
      url: `${this.config.getApiDatamapV2Url()}/pages`,
      params,
    });
  }

  // 查询单个page
  async queryPage(id: string, config?: RequestRequestConfig) {
    return this.axios.request<Page>({
      ...(config || {}),
      method: 'get',
      url: `${this.config.getApiDatamapV2Url()}/pages/${id}`,
    });
  }

  // 创建page
  async createPage(data: CreatePageInput, config?: RequestRequestConfig) {
    return this.axios.request<Page>({
      ...(config || {}),
      method: 'post',
      url: `${this.config.getApiDatamapV2Url()}/pages`,
      data,
    });
  }

  // 修改page
  async updatePage(data: UpdatePageInput, config?: RequestRequestConfig) {
    return this.axios.request<Page>({
      ...(config || {}),
      method: 'patch',
      url: `${this.config.getApiDatamapV2Url()}/pages/${data.id}`,
      data,
    });
  }

  // 删除page
  async deletePage(id: string, config?: RequestRequestConfig) {
    return this.axios.request<Page>({
      ...(config || {}),
      method: 'delete',
      url: `${this.config.getApiDatamapV2Url()}/pages/${id}`,
    });
  }
}
