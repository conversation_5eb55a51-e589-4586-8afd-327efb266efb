import { App } from '@graphql/app';
import { EntityLoader, EntityTypeEnum } from '@graphql/entity';
import { Page } from '@graphql/page';
import { User } from '@graphql/user';
import { Type } from '@nestjs/common';
import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { Loader } from '@comm/decorator';

export function GraphBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => App, { description: 'app', nullable: true })
    public appObj(@Parent() page: Page, @Loader(EntityLoader) loader: EntityLoader) {
      return loader
        .getDataLoader(EntityTypeEnum.APP, { entityType: EntityTypeEnum.APP })
        .load(page.app_id);
    }

    @ResolveField(() => User, { description: '创建者', nullable: true })
    public userObj(@Parent() page: Page, @Loader(EntityLoader) loader: EntityLoader) {
      return loader
        .getDataLoader(EntityTypeEnum.USER, { entityType: EntityTypeEnum.USER })
        .load(page.user_id);
    }
  }

  return BaseResolver;
}
