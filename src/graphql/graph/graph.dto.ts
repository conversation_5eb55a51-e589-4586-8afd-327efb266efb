import { OrderQuery } from '@graphql/graphql.dto';
import { Field, InputType, PartialType } from '@nestjs/graphql';

@InputType()
export class QueryGraphsParams extends OrderQuery {
  @Field({ nullable: true })
  ids?: string;

  @Field({ nullable: true })
  name_like?: string;

  @Field(() => [String], { nullable: true })
  chart_type?: string[];

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  datapkg_id?: number;

  @Field({ nullable: true })
  permission_level?: string;

  @Field({ nullable: true })
  config_type?: string;

  @Field({ nullable: true })
  editable?: boolean;

  @Field({ nullable: true })
  folder?: boolean;

  @Field({ nullable: true })
  nofolder?: boolean;

  @Field({ nullable: true })
  filter_drill_graph?: boolean;
}

@InputType()
export class CreateGraphInput {
  @Field({ nullable: true })
  name?: string;
}

@InputType()
export class UpdateGraphInput extends PartialType(CreateGraphInput) {
  @Field()
  id: string;
}
