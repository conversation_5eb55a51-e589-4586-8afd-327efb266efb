import { Field, ObjectType, PartialType } from '@nestjs/graphql';
import { CreateGraphInput } from './graph.dto';

@ObjectType()
export class Graph extends PartialType(CreateGraphInput, ObjectType) {
  @Field({ description: 'Id' })
  id: string;

  @Field({ nullable: true })
  packageUuid?: string;

  @Field({ nullable: true })
  sqlUuid?: string;

  @Field({ nullable: true })
  chartType?: string;

  @Field({ nullable: true })
  app_id?: number;

  @Field(() => [String], { nullable: 'itemsAndList' })
  permissions?: string[];

  @Field()
  user_id: number;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  configType?: string;

  @Field()
  vaultTitle: string;

  @Field({ nullable: true })
  isDrill?: string;

  @Field({ description: '创建时间' })
  create_time: number;

  @Field({ description: '更新时间', nullable: true })
  update_time?: number;
}
