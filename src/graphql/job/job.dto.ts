import { PaginationQuery } from '@graphql/graphql.dto';
import { Field, InputType, PartialType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

export type IJobType = 'lab' | 'datapkg_constraint' | 'flow';
export type IJobTriggerType = 'cron' | 'interval';

export interface IJobJobLab {
  job: 'lab';
  lab_uuid: string;
}

export interface IJobJobDatapkgConstraint {
  job: 'datapkg_constraint';
  datapkg_id: string;
  constraints?: string[];
}

export interface IJobJobFlow {
  job: 'flow';
  flow_uuid: string;
}

export type IJobJob = IJobJobLab | IJobJobDatapkgConstraint | IJobJobFlow;

export interface IJobRangeTime {
  start_time?: number;
  end_time?: number;
}

export interface IJobTriggerInterval extends IJobRangeTime {
  trigger: 'interval';
  weeks?: number;
  days?: number;
  hours?: number;
  minutes?: number;
  seconds?: number;
}

export interface IJobTriggerCron extends IJobRangeTime {
  trigger: 'cron';
  year?: string[];
  month?: string[];
  day_of_week?: string[];
  week?: string[];
  day?: string[];
  hour?: string[];
  minute?: string;
  second?: string;
  timezone?: string;
}

export type IJobTrigger = IJobTriggerInterval | IJobTriggerCron;

@InputType()
export class JobQuery extends PaginationQuery {
  @Field(() => String, { nullable: true })
  job?: IJobType;

  @Field(() => String, { nullable: true })
  trigger?: IJobTriggerType;

  @Field({ nullable: true })
  active?: boolean;

  @Field({ nullable: true })
  datapkgId?: string;

  @Field({ nullable: true })
  flowUuid?: string;
}

@InputType()
export class JobCreate {
  @Field({ nullable: true })
  name: string;

  @Field(() => GraphQLJSON, { nullable: true })
  job: IJobJob;

  @Field(() => GraphQLJSON, { nullable: true })
  trigger: IJobTrigger;

  @Field({ nullable: true })
  active?: boolean;
}

@InputType()
export class JobUpdate extends PartialType(JobCreate) {
  @Field()
  update_time: number;
}

@InputType()
export class JobDelete {
  @Field()
  update_time: number;
}
