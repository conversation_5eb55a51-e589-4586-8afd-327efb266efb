import { OrderQuery } from '@graphql/graphql.dto';
import { Field, InputType, PartialType } from '@nestjs/graphql';

@InputType()
export class QueryProjectsParams extends OrderQuery {
  @Field({ nullable: true })
  project_ids?: string;

  @Field({ nullable: true })
  fuzzy_name?: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  with_status?: boolean;
}

@InputType()
export class CreateProjectInput {
  @Field({ nullable: true })
  name?: string;
}

@InputType()
export class UpdateProjectInput extends PartialType(CreateProjectInput) {
  @Field()
  id: string;
}
