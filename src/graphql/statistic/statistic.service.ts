import { RequestRequestConfig } from '@graphql/graphql.dto';
import { Injectable } from '@nestjs/common';
import { AxiosService } from '@globalModule/axios';
import { ConfigService } from '@globalModule/config';
import { StatisticQuery } from './statistic.dto';
import { IStatistics } from './statistic.interface';

@Injectable()
export class StatisticService {
  constructor(private readonly axios: AxiosService, private readonly config: ConfigService) {}

  // 批量统计
  async findAll(data: StatisticQuery[], config?: RequestRequestConfig) {
    return this.axios.request<IStatistics[]>({
      ...(config || {}),
      method: 'post',
      url: `${this.config.getApiDatamapV2Url()}/statistics/batch/query`,
      data,
    });
  }

  // 单个统计
  async findOne(index: string, config?: RequestRequestConfig<StatisticQuery>) {
    return this.axios.request<IStatistics>({
      ...(config || {}),
      method: 'get',
      url: `${this.config.getApiDatamapV2Url()}/statistics/${index}`,
    });
  }
}
