import { HttpModule } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { AxiosModule } from '@comm/module/axios';
import { ConfigModule } from '@comm/module/config';
import { LogModule } from '@comm/module/log';
import { PermissionModule } from '../permission';
import { AppService } from './app.service';

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [HttpModule, PermissionModule, ConfigModule, AxiosModule, LogModule],
      providers: [AppService],
    }).compile();

    service = module.get(AppService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
