import { Injectable } from '@nestjs/common';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';
import { App } from './app.model';
import { AppService } from './app.service';

@Injectable()
export class AppLoader extends NestDataLoaderFactory<number, App> {
  constructor(private readonly appService: AppService) {
    super();
  }

  protected loaderQuery(ids: readonly number[], params?: Record<string, any>) {
    return this.appService.findAll(params);
  }
}
