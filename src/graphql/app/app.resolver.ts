import { EntityLoader, EntityTypeEnum } from '@graphql/entity';
import { Type } from '@nestjs/common';
import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { Loader } from '@comm/decorator';
import { ExternalApp } from './app.model';

export function AppBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    // @ResolveField(() => [String], { description: 'App的权限Id集合', nullable: 'itemsAndList' })
    // appPermissionIds(@Parent() app: App) {
    //   return flatMap(app.permission, (v) => v);
    // }
    // @ResolveField(() => [Permission], { description: 'App的权限集合', nullable: 'itemsAndList' })
    // appPermissionObjs(@Parent() app: App, @Loader(PermissionLoader) loader: PermissionLoader) {
    //   const ids = flatMap(app.permission, (v) => v);
    //   return loader.getDataLoader().loadMany(ids);
    // }
  }

  return BaseResolver;
}

export function ExternalAppBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => String, { description: 'app名称' })
    public name(@Parent() app: ExternalApp, @Loader(EntityLoader) loader: EntityLoader) {
      return loader
        .getDataLoader(EntityTypeEnum.APP, { entityType: EntityTypeEnum.APP })
        .load(app.app_id)
        .then((resp) => resp.name);
    }
  }

  return BaseResolver;
}
