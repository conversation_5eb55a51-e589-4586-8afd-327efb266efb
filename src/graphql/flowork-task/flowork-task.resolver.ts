import { EntityLoader, EntityTypeEnum } from '@graphql/entity';
import { User } from '@graphql/user';
import { WorkflowLoader } from '@graphql/workflow';
import { ComWorkflow } from '@graphqlCom/workflow';
import { Type } from '@nestjs/common';
import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { Loader } from '@comm/decorator';
import { FloworkTask, OneTableFlowTask, OneTableRootDispatchWorkflow } from './flowork-task.model';

export function FloworkTaskBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => ComWorkflow, { description: '详情', nullable: true })
    public workflow(@Parent() parent: FloworkTask, @Loader(WorkflowLoader) loader: WorkflowLoader) {
      return loader
        .getDataLoader(undefined, {
          with_spec: 'detail',
          initiator_is_me: false,
          with_form_data: true,
          with_data: true,
        })
        .load(parent.workflow_id);
    }

    @ResolveField(() => User, { description: '执行者', nullable: true })
    public executorObj(@Parent() parent: FloworkTask, @Loader(EntityLoader) loader: EntityLoader) {
      return parent.executor
        ? loader
            .getDataLoader(EntityTypeEnum.USER, { entityType: EntityTypeEnum.USER })
            .load(parent.executor)
        : null;
    }
  }

  return BaseResolver;
}

export function OneTableFlowTaskBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => ComWorkflow, { description: '详情', nullable: true })
    public workflow(
      @Parent() parent: OneTableFlowTask,
      @Loader(WorkflowLoader) loader: WorkflowLoader,
    ) {
      return loader
        .getDataLoader(undefined, {
          with_spec: 'detail',
          with_data: true,
          initiator_is_me: false,
          with_form_data: true,
        })
        .load(parent.workflow_id);
    }

    @ResolveField(() => User, { description: '执行者', nullable: true })
    public executorObj(
      @Parent() parent: OneTableFlowTask,
      @Loader(EntityLoader) loader: EntityLoader,
    ) {
      return parent.executor
        ? loader
            .getDataLoader(EntityTypeEnum.USER, { entityType: EntityTypeEnum.USER })
            .load(parent.executor)
        : null;
    }
  }

  return BaseResolver;
}

export function OneTableRootDispatchWorkflowResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => ComWorkflow, { description: '流程实例详情', nullable: true })
    public workflow(
      @Parent() parent: OneTableRootDispatchWorkflow,
      @Loader(WorkflowLoader) loader: WorkflowLoader,
    ) {
      return loader
        .getDataLoader(undefined, { with_data: true, with_spec: 'simple', initiator_is_me: false })
        .load(parent.root_dispatch_workflow_id);
    }
  }
  return BaseResolver;
}
