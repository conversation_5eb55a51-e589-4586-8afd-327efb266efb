import { Injectable } from '@nestjs/common';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';
import { QueryUsersInput } from './user.dto';
import { User } from './user.model';
import { UserService } from './user.service';

@Injectable()
export class UserLoader extends NestDataLoaderFactory<number, User> {
  constructor(private readonly userService: UserService) {
    super();
  }

  protected loaderQuery(keys: number[], params?: QueryUsersInput) {
    return this.userService.findAll(params);
  }
}
