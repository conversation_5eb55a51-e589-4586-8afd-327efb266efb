import { Field, ObjectType, PartialType } from '@nestjs/graphql';
import { CreateEventsInput } from './events.dto';

@ObjectType()
export class Events extends PartialType(CreateEventsInput, ObjectType) {
  @Field({ description: 'Id' })
  id: string;

  @Field({ description: '资源Id', nullable: true })
  resource_id?: string;

  @Field({ description: '事件类型', nullable: true })
  event_type: string;

  @Field({ description: '激活状态', nullable: true })
  active?: boolean;
}
