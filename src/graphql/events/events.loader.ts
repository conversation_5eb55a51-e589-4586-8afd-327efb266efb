import { Injectable } from '@nestjs/common';
import { join } from 'lodash';
import { LoaderParams, NestDataLoaderFactory } from '@comm/nest.dataloader.factory';
import { Events } from './events.model';
import { EventsService } from './events.service';

@Injectable()
export class EventsLoader extends NestDataLoaderFactory<string, Events> {
  constructor(private readonly eventsService: EventsService) {
    super('resource_id');
  }

  protected loaderQuery(ids?: string[], params?: LoaderParams) {
    const { eventTypes = [], ...rest } = params || {};
    return this.eventsService.queryEvents({
      resource_ids: join(ids || [], ','),
      event_types: join(eventTypes, ','),
      ...rest,
    });
  }
}
