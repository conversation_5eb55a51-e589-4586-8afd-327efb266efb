import { OrderQuery } from '@graphql/graphql.dto';
import { Field, InputType } from '@nestjs/graphql';

export type ITicketType =
  | 'apply_app_resource_permission'
  | 'apply_user_resource_permission'
  | 'apply_datapkg_on_album'
  | 'apply_public_datapkg_on_album'
  | 'apply_cross_app_resource_permission'
  | 'apply_publish_user_datapkg_to_app';

export type ITicketResolution = 'approved' | 'rejected' | 'cancelled';

export type ITicketStatus = 'open' | 'ongoing' | 'closed';

export type ITicketResourceType = 'datapkg';

@InputType()
export class TicketListQuery extends OrderQuery {
  @Field({ description: '申请人id', nullable: true })
  initiator?: number;

  @Field({ description: '申请人所属机构', nullable: true })
  initiator_app?: number;

  @Field({ description: '审批人id', nullable: true })
  resolver?: number;

  @Field({ nullable: true })
  title?: string;

  @Field({ description: '模糊查找title/description/resolution_comment', nullable: true })
  q?: string;

  @Field({ nullable: true })
  resource_id?: string;

  @Field({ nullable: true })
  ticket_type?: string;

  @Field({ description: '逗号分割', nullable: true })
  ticket_ids?: string;

  @Field({ nullable: true })
  resolution?: string;

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  resource_type?: string;

  @Field({ nullable: true })
  permission?: string;

  @Field({ nullable: true })
  assignee?: number;

  @Field({ nullable: true })
  album_id?: number;

  @Field({ nullable: true })
  auth_app?: number;
}

@InputType()
export class TicketParamsInput {
  @Field({ nullable: true })
  permission?: string;

  @Field({ nullable: true })
  album_id?: string;

  @Field({ nullable: true })
  tags?: string;

  @Field({ nullable: true })
  expire_ts?: string;

  @Field({ nullable: true })
  target_app?: number;
}

@InputType()
export class CreateTicketInput {
  @Field({ nullable: false })
  title: string;

  @Field({ nullable: false })
  resource_id: string;

  @Field({ nullable: false })
  resource_type: string;

  @Field({ nullable: false })
  ticket_type: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => TicketParamsInput, { nullable: true })
  params?: TicketParamsInput;
}

@InputType()
export class TicketInput {
  @Field()
  resource_id: string;

  @Field()
  title: string;
}

@InputType()
export class BatchCreateTicketInput {
  @Field({ nullable: true })
  description?: string;

  @Field(() => String)
  ticket_type: ITicketType;

  @Field(() => TicketParamsInput, { nullable: true })
  params?: TicketParamsInput;

  @Field(() => String)
  resource_type: ITicketResourceType;

  @Field(() => [TicketInput])
  ticket_list: TicketInput[];
}

@InputType()
export class TicketResolutionInput {
  @Field({ description: 'ITicketResolution', nullable: false })
  resolution: string;

  @Field({ nullable: true })
  expire?: number;

  @Field({ nullable: true })
  resolution_comment?: string;
}
