import {
  OrderQuery,
  PaginationQuery,
  RequestRequestConfig,
  TaggroupTagsInput,
} from '@graphql/graphql.dto';
import { Field, Float, InputType, Int, PartialType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

export type IDatapkgPermission =
  | 'read'
  | 'update'
  | 'insert'
  | 'delete'
  | 'download'
  | 'view_detail'
  | 'view_full_description';

export type IDatapkgGeometryType =
  | 'point'
  | 'line'
  | 'polygon'
  | 'plain'
  | 'point_to_point'
  | 'point_to_line'
  | 'point_to_polygon'
  | 'line_to_point'
  | 'line_to_line'
  | 'line_to_polygon'
  | 'polygon_to_point'
  | 'polygon_to_line'
  | 'polygon_to_polygon';

export type IDatapkgColumnType =
  | 'bool'
  | 'datetime'
  | 'date'
  | 'float'
  | 'geometry'
  | 'int'
  | 'json'
  | 'str'
  | 'time'
  | 'bytea'
  | 'bit'
  | 'bigint'
  | 'array'
  | 'imgurl'
  | 'videostreamurl'
  | 'timestamp';

export type IRowValue = string | number | boolean | object | null | undefined | any[];

export interface IDatapkgRichDescription {
  article_id?: string;
  content?: string;
  content_type?: string;
  version?: string;
}

export interface IDatapkgColumnProjections {
  type: IDatapkgColumnType;
  target: string;
  keypath: string;
  formula: string;
  source: string;
  is_extra: boolean;
  name?: string;
}

export interface IDatapkgFeatureFlags {
  disable_preview: boolean;
  disable_view_detail: boolean;
  inconsistent_cnt: boolean;
  mask_data: boolean;
  row_level_permission: boolean; // 是否协同数据包
}

export interface IDatapkgColumn {
  comment: string;
  id: string;
  name: string;
  type: IDatapkgColumnType;
  view_format: Record<string, string>;
}

export interface IDatapkgDdlElementQuery {
  show_hidden?: boolean;
  type?: IDatapkgDdlElementType;
}

export interface IDatapkgColumnPatchQuery {
  column_attr?: string; // 'id' | 'name'
  change_ddl?: boolean;
}

export interface IDatapkgColumnBatchPatch
  extends Omit<Partial<IDatapkgColumn>, 'id'>,
    IDatapkgColumnPatchQuery {
  column: string; // id或name
}

export interface IDatapkgDdlElementsBatchPost {
  to_create?: IDatapkgDdlElementPost[];
  to_drop?: IDatapkgDdlElementComm[];
}

export type IDatapkgDdlElementType = 'index' | 'unique_constraint' | 'primary_key';

export interface IDatapkgDdlElementComm {
  type: IDatapkgDdlElementType;
  name: string;
}

export interface IDatapkgDdlElementPost {
  type: IDatapkgDdlElementType;
  columns: string[];
  unique?: boolean;
}

export type IColumnSaveData = Partial<IDatapkgColumn>;

export type IDatapkgConstraintOperator =
  | 'eq'
  | 'ne'
  | 'gt'
  | 'ge'
  | 'lt'
  | 'le'
  | 'between'
  | 'in'
  | 'not_in'
  | 'not'
  | 'true'
  | 'false'
  | 'start_with'
  | 'end_with'
  | 'like'
  | 'not_like'
  | 'match'
  | 'not_match'
  | 'contain'
  | 'not_contain'
  | 'null'
  | 'not_null';

export type IDatapkgConstraintType = 'column' | 'sql' | 'formula' | 'rawsql';

export type IDatapkgGenealogyScope = 'all' | 'app' | 'user';

@InputType()
export class DatapkgListQuery extends OrderQuery {
  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  embedding_text?: string;

  @Field(() => [String], { nullable: true })
  id?: string[];

  @Field(() => [String], { nullable: true })
  dataset_id?: string[];

  @Field({ nullable: true })
  in_dataset?: boolean;

  @Field({ nullable: true })
  need_tags?: boolean;

  @Field({ nullable: true })
  need_permissions?: boolean;

  @Field({ nullable: true })
  fuzzy_tag?: string;

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  descending?: boolean;

  @Field({ nullable: true })
  exclude_customer?: boolean;

  @Field({ nullable: true })
  folder?: string;

  @Field({ nullable: true })
  nofolder?: boolean;

  @Field({ nullable: true })
  product_type?: string;

  @Field({ nullable: true })
  ownership?: string;

  @Field({ nullable: true })
  package_type?: string;

  @Field(() => [String], { nullable: true })
  album_id?: string[];

  @Field(() => [String], { nullable: true })
  geometry_type?: string[];

  // string[] | boolean
  @Field(() => GraphQLJSON, { nullable: true })
  privilege?: string[];

  @Field({ nullable: true })
  column?: string;

  @Field({ nullable: true })
  create_time_max?: number;

  @Field({ nullable: true })
  create_time_min?: number;

  @Field({ nullable: true })
  update_time_max?: number;

  @Field({ nullable: true })
  update_time_min?: number;
}

@InputType()
export class QueryPkgsArgs {
  @Field({ nullable: true })
  config?: RequestRequestConfig;

  @Field({ nullable: true })
  data?: DatapkgListQuery;
}

@InputType()
export class DatapkgQuery {
  @Field({ nullable: true })
  need_permissions?: boolean;
}

@InputType()
export class DatapkgRichDescriptionInput {
  @Field({ nullable: true })
  article_id?: string;

  @Field({ nullable: true })
  content?: string;

  @Field({ nullable: true })
  content_type?: string;

  @Field({ nullable: true })
  version?: string;
}

@InputType()
export class DatapkgMetaInput {
  @Field(() => DatapkgRichDescriptionInput, { nullable: true })
  rich_description?: DatapkgRichDescriptionInput;

  @Field(() => DatapkgRichDescriptionInput, { nullable: true })
  full_description?: DatapkgRichDescriptionInput;
}

@InputType()
export class UpdateDatapkgsMetaInput extends DatapkgMetaInput {
  @Field(() => [String], { nullable: true })
  datapkg_ids?: string[];
}

@InputType()
export class DatapkgUpdateFrequency extends DatapkgMetaInput {
  @Field(() => String, { nullable: true })
  unit: string;

  @Field(() => Float, { nullable: true })
  value: number;
}

@InputType()
export class UpdateDatapkgInput {
  @Field(() => String, { nullable: true })
  agg_type?: string;

  @Field(() => DatapkgUpdateFrequency, { nullable: true })
  update_frequency?: DatapkgUpdateFrequency;

  @Field(() => String, { nullable: true })
  dataset_id?: string;

  @Field(() => [String], { nullable: true })
  available_columns?: string[];

  @Field(() => GraphQLJSON, { nullable: true })
  write_options?: object;

  @Field(() => String, { nullable: true })
  table_name?: string;

  @Field(() => String, { nullable: true })
  table_cond?: string;

  @Field({ nullable: true })
  count?: number;

  @Field(() => String, { nullable: true })
  package_type?: string;

  @Field(() => String, { nullable: true })
  table_schema?: string;

  @Field(() => String, { nullable: true })
  update_method?: string;

  @Field(() => String, { nullable: true })
  geometry_type?: IDatapkgGeometryType;

  @Field(() => GraphQLJSON, { nullable: true })
  rich_description?: IDatapkgRichDescription;

  @Field(() => GraphQLJSON, { nullable: true })
  full_description?: IDatapkgRichDescription;

  @Field(() => GraphQLJSON, { nullable: true })
  data_schema?: object;

  @Field(() => String, { nullable: true })
  sql?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  column_projections?: IDatapkgColumnProjections;

  @Field(() => [TaggroupTagsInput], { nullable: true })
  tags?: TaggroupTagsInput[];

  @Field(() => String, { nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  feature_flags?: IDatapkgFeatureFlags;

  @Field(() => [TaggroupTagsInput], { nullable: true })
  remove_tags?: TaggroupTagsInput[];

  @Field(() => GraphQLJSON, { nullable: true })
  key_columns?: Record<string, string>;

  @Field({ nullable: true })
  force_refresh?: boolean;

  @Field({ nullable: true })
  refresh_timer?: string;

  @Field(() => Int, { nullable: true })
  next_refresh_time?: number;
}

@InputType()
export class DatapkgRowsQueryCondition {
  @Field()
  column: string;

  @Field()
  operator: string;

  // 类型为[String] 或 [Float]
  @Field(() => GraphQLJSON, { nullable: true })
  values?: any[];
}

@InputType()
export class DatapkgRowsQueryOrderBy {
  @Field({ nullable: true })
  asc?: boolean;

  @Field()
  field: string;
}

@InputType()
export class DatapkgRowsQuery extends PaginationQuery {
  @Field(() => [String], { nullable: true })
  excludes?: string[];

  @Field({ nullable: true })
  with_geometry?: boolean;

  @Field(() => [DatapkgRowsQueryCondition], { nullable: true })
  condition?: DatapkgRowsQueryCondition[];

  @Field(() => [String], { nullable: true })
  only?: string[];

  @Field(() => [DatapkgRowsQueryOrderBy], { nullable: true })
  orderby?: DatapkgRowsQueryOrderBy[];

  @Field(() => GraphQLJSON, { nullable: true })
  operator_filter?: any;
}

@InputType()
export class DatapkgGenealogysQuery {
  @Field({ nullable: true })
  offspring_level?: number;

  @Field({ nullable: true })
  ancestor_level?: number;
}

@InputType()
export class DatapkgRefreshInput {
  @Field({ nullable: true })
  force_refresh?: boolean;
}

@InputType()
export class CreateDatapkgColumnInput {
  @Field({ nullable: true })
  force_refresh?: boolean; // 是否强制刷新meta信息

  @Field({ nullable: true })
  autoincrement?: boolean;

  @Field({ nullable: true })
  comment?: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  nullable?: boolean;

  @Field({ nullable: true })
  unique?: boolean;

  @Field({ nullable: true })
  index?: boolean;

  @Field(() => String, { nullable: true })
  type?: IDatapkgColumnType;

  @Field(() => GraphQLJSON, { nullable: true })
  view_format?: object;
}

@InputType()
export class UpdateDatapkgColumnInput extends PartialType(CreateDatapkgColumnInput) {}

@InputType()
export class DatapkgDdlElementQuery {
  @Field({ nullable: true })
  show_hidden?: boolean;

  @Field({ nullable: true })
  type?: string;
}

@InputType()
export class DatapkgDdlElementPostInput {
  @Field()
  type: string;

  @Field(() => [String])
  columns: string[];

  @Field(() => Boolean, { nullable: true })
  unique?: boolean;
}

@InputType()
export class DatapkgDdlElementCommInput {
  @Field()
  type: string;

  @Field()
  name: string;
}

@InputType()
export class DatapkgColumnBatchPatchInput {
  @Field({ nullable: true })
  column_attr?: string;

  @Field(() => Boolean, { nullable: true })
  change_ddl?: boolean;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  type?: string;

  @Field({ nullable: true })
  comment?: string;

  @Field(() => Boolean, { nullable: true })
  nullable?: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  view_format?: { format: string; mask: any };

  @Field()
  column: string;
}

@InputType()
export class UpdateDatapkgColumnListInput {
  @Field()
  pkgId: string;

  @Field(() => GraphQLJSON, { nullable: true })
  keyColumns?: Record<string, string>;

  @Field(() => [String], { nullable: true })
  delColumns?: string[];

  @Field(() => [DatapkgColumnBatchPatchInput], { nullable: true })
  updateColumns?: IDatapkgColumnBatchPatch[];

  @Field(() => [DatapkgDdlElementPostInput], { nullable: true })
  addDllElements?: IDatapkgDdlElementPost[];

  @Field(() => [DatapkgDdlElementCommInput], { nullable: true })
  delDllElements?: IDatapkgDdlElementComm[];
}

@InputType()
export class CreateDatapkgConstraintInput {
  @Field()
  expression: string;

  @Field()
  name: string;

  @Field(() => String, { nullable: true })
  operator?: IDatapkgConstraintOperator;

  @Field({ nullable: true })
  description?: string;

  @Field(() => String, { nullable: false })
  constraint_type: IDatapkgConstraintType;

  @Field(() => GraphQLJSON, { nullable: true })
  threshold?: any;
}

@InputType()
export class UpdateDatapkgConstraintInput extends PartialType(CreateDatapkgConstraintInput) {}

@InputType()
export class DatapkgChecklogListQuery extends PaginationQuery {
  @Field({ nullable: true })
  all_version?: boolean;

  @Field({ nullable: true })
  constraint_id?: string;

  @Field({ nullable: true })
  datapkg_version?: number;
}

@InputType()
export class DatapkgGenealogyDelete {
  @Field({ nullable: true })
  children?: string;

  @Field({ nullable: true })
  parent?: string;

  @Field(() => String, { nullable: true })
  scope?: IDatapkgGenealogyScope;
}

@InputType()
export class DatapkgGenealogyInput {
  @Field(() => [String], { nullable: true })
  children?: string[];

  @Field(() => [String], { nullable: true })
  parent?: string[];

  @Field({ nullable: true })
  replace?: boolean;

  @Field(() => String, { nullable: true })
  scope?: IDatapkgGenealogyScope;
}

export type IDatapkgCollaboratePermission = 'read' | 'update' | 'delete' | 'insert' | 'view_detail';

@InputType()
export class DatapkgCollaborateInfo {
  @Field(() => [Int], { nullable: true })
  role_ids?: number[];

  @Field(() => String)
  permission: IDatapkgCollaboratePermission;

  @Field(() => GraphQLJSON, { nullable: true })
  conditions: DatapkgRowsQueryCondition;

  @Field(() => [Int], { nullable: true })
  user_ids?: number[];
}

@InputType()
export class DatapkgRowPermissionStrategy {
  @Field(() => String)
  strategy: string; // onetable | classic | operator_filter
  @Field(() => GraphQLJSON, { nullable: true })
  config?: Record<string, any>;
}

@InputType()
export class DatapkgCollaborateInput {
  @Field(() => GraphQLJSON, { nullable: true })
  row_permission_strategy?: DatapkgRowPermissionStrategy;

  @Field(() => GraphQLJSON, { nullable: true })
  collaborate_info?: DatapkgCollaborateInfo[];
}

@InputType()
export class DatapkgColumnBatchDelete {
  @Field(() => [String])
  columns: string[];

  @Field({ nullable: true })
  column_attr?: string;

  @Field({ nullable: true })
  change_ddl?: boolean;
}

@InputType()
export class DatapkgRowsInput {
  @Field(() => [String], { description: 'Id' })
  columns: string[];

  @Field(() => GraphQLJSON, { description: '列名称' })
  values: IRowValue[][];
}

@InputType()
export class DatapkgRowsDeleteInput {
  @Field(() => GraphQLJSON, { description: '行Id' })
  row_ids: number[];
}
