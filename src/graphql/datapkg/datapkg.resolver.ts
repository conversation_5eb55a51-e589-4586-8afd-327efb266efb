import { App } from '@graphql/app';
import { Dataset, DatasetLoader } from '@graphql/dataset';
import { EntityLoader } from '@graphql/entity';
import { EventsLoader, EventsTypeEnum } from '@graphql/events';
import { ResourcePermissionLoader } from '@graphql/resource';
import { Taggroup, TaggroupLoader } from '@graphql/taggroup';
import { User } from '@graphql/user';
import { ComWorkflowSpec, ComWorkflowSpecLoader } from '@graphqlCom/workflow-spec';
import { Type } from '@nestjs/common';
import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { forEach, get, map } from 'lodash';
import { Loader } from '@comm/decorator';
import { EntityTypeEnum } from '../entity/entity.dto';
import { Datapkg, PkgBoundWfspec } from './datapkg.model';

export function DatapkgBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => Dataset, { description: '数据源', nullable: true })
    public datasource(@Parent() pkg: Datapkg, @Loader(DatasetLoader) loader: DatasetLoader) {
      return loader.getDataLoader().load(pkg.dataset_id);
    }

    @ResolveField(() => String, { description: '数据包更新订阅', nullable: true })
    public pkgUpdate(@Parent() pkg: Datapkg, @Loader(EventsLoader) loader: EventsLoader) {
      return loader
        .getDataLoader('pkgUpdate', {
          eventTypes: [EventsTypeEnum.DATA_UPDATED],
        })
        .load(pkg.id)
        .then((resp) => resp?.id);
    }

    @ResolveField(() => String, { description: '质量监控任务执行结果订阅', nullable: true })
    public qualityMonitor(@Parent() pkg: Datapkg, @Loader(EventsLoader) loader: EventsLoader) {
      return loader
        .getDataLoader('constraintCheckResult', {
          eventTypes: [EventsTypeEnum.CONSTRAINT_CHECK_RESULT],
        })
        .load(pkg.id)
        .then((resp) => resp?.id);
    }

    @ResolveField(() => App, { description: 'app', nullable: true })
    public app(@Parent() pkg: Datapkg, @Loader(EntityLoader) loader: EntityLoader) {
      return loader
        .getDataLoader(EntityTypeEnum.APP, { entityType: EntityTypeEnum.APP })
        .load(pkg.app_id);
    }

    @ResolveField(() => User, { description: '数据包创建者', nullable: true })
    public user(@Parent() pkg: Datapkg, @Loader(EntityLoader) loader: EntityLoader) {
      return loader
        .getDataLoader(EntityTypeEnum.USER, { entityType: EntityTypeEnum.USER })
        .load(pkg.user_id);
    }

    // 数据包权限通过loader获取，不再需要通过接口传参的形式。
    @ResolveField(() => [String], { description: '数据包权限', nullable: 'itemsAndList' })
    public async permissions(
      @Parent() pkg: Datapkg,
      @Loader(ResourcePermissionLoader) loader: ResourcePermissionLoader,
    ) {
      return loader
        .getDataLoader(undefined, { resource_types: 'datapkg' })
        .load(pkg.id)
        .then((resp) => {
          return get(resp, 'privs', []);
        });
    }

    @ResolveField(() => [Taggroup], { description: '标签组列表', nullable: true })
    public taggroups(@Parent() pkg: Datapkg, @Loader(TaggroupLoader) loader: TaggroupLoader) {
      if (!pkg.tags) return null;
      const taggroupIds = map(pkg.tags, 'taggroup_id');
      return loader
        .getDataLoader()
        .loadMany(taggroupIds)
        .then((list) => {
          const result: Taggroup[] = [];
          forEach(pkg.tags, (group, indx) => {
            const tg = list[indx];
            if (tg) {
              result.push({
                id: group.taggroup_id,
                name: tg.name,
                choices: group.taggroup_tags,
              });
            }
          });
          return result.length ? result : null;
        });
    }
  }

  return BaseResolver;
}

export function PkgBoundWfspecBaseResolver<T extends Type<unknown>>(classRef: T): any {
  @Resolver(() => classRef, { isAbstract: true })
  abstract class BaseResolver {
    @ResolveField(() => [ComWorkflowSpec], { description: '流程', nullable: 'itemsAndList' })
    public workflowSpecs(
      @Parent() boundSpec: PkgBoundWfspec,
      @Loader(ComWorkflowSpecLoader) loader: ComWorkflowSpecLoader,
    ) {
      return loader.getDataLoader().loadMany(boundSpec.workflow_spec_ids);
    }
  }

  return BaseResolver;
}
