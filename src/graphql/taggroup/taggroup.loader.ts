import { Injectable } from '@nestjs/common';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';
import { Taggroup } from './taggroup.model';
import { TaggroupService } from './taggroup.service';

@Injectable()
export class TaggroupLoader extends NestDataLoaderFactory<string, Taggroup> {
  constructor(private readonly taggroupService: TaggroupService) {
    super();
  }

  protected loaderQuery() {
    return this.taggroupService.findAll();
  }
}
