import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import * as bcrypt from 'bcryptjs';
import { Repository } from 'typeorm';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

import { CreateUserDto } from './dto/create-user.dto';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  /**
   * 查找所有用户
   */
  async findAll(): Promise<ResultResponse<User[]>> {
    return ResultUtil.execute(async () => {
      const users = await this.usersRepository.find();
      return users.map((user) => this.omitPassword(user));
    }, '查询所有用户失败');
  }

  /**
   * 根据ID查找用户
   * @param id 用户ID
   */
  async findById(id: number): Promise<ResultResponse<User>> {
    return ResultUtil.execute(async () => {
      const user = await this.usersRepository.findOneBy({ id });

      if (!user) {
        return ResultUtil.fail(`用户ID ${id} 未找到`, 'USER_NOT_FOUND');
      }

      return this.omitPassword(user);
    }, 'ID查询用户失败');
  }

  /**
   * 根据邮箱查找用户
   * @param email 邮箱
   */
  async findByEmail(email: string): Promise<ResultResponse<User>> {
    return ResultUtil.execute(async () => {
      const user = await this.usersRepository.findOneBy({ email });

      if (!user) {
        return ResultUtil.fail(`邮箱 ${email} 未找到`, 'USER_NOT_FOUND');
      }

      return this.omitPassword(user);
    }, '邮箱查询用户失败');
  }

  /**
   * 根据用户名查找用户
   * @param username 用户名
   */
  async findByUsername(username: string): Promise<ResultResponse<User>> {
    return ResultUtil.execute(async () => {
      const user = await this.usersRepository.findOneBy({ username });

      if (!user) {
        return ResultUtil.fail(`用户名 ${username} 未找到`, 'USER_NOT_FOUND');
      }

      return this.omitPassword(user);
    }, '用户名查询用户失败');
  }

  /**
   * 根据邮箱或用户名查找用户
   * @param emailOrUsername 邮箱或用户名
   * @param needPassword 是否需要密码
   */
  async findByEmailOrUsername(
    emailOrUsername: string,
    needPassword = false,
  ): Promise<ResultResponse<User>> {
    return ResultUtil.execute(async () => {
      const user = await this.usersRepository.findOne({
        where: [{ email: emailOrUsername }, { username: emailOrUsername }],
      });

      if (!user) {
        return ResultUtil.fail(`未找到邮箱或用户名为 ${emailOrUsername} 的用户`, 'USER_NOT_FOUND');
      }

      return needPassword ? user : this.omitPassword(user);
    }, '邮箱或用户名查询用户失败');
  }

  /**
   * 创建用户
   * @param createUserDto 创建用户DTO
   */
  async createUser(createUserDto: CreateUserDto): Promise<ResultResponse<User>> {
    // 验证邮箱是否已存在
    const emailResult = await this.findByEmail(createUserDto.email);
    if (emailResult.success) {
      return ResultUtil.fail('创建用户失败: 邮箱已被使用', 'EMAIL_ALREADY_EXISTS');
    }

    // 验证用户名是否已存在
    const usernameResult = await this.findByUsername(createUserDto.username);
    if (usernameResult.success) {
      return ResultUtil.fail('创建用户失败: 用户名已被使用', 'USERNAME_ALREADY_EXISTS');
    }

    return ResultUtil.execute(async () => {
      const user = this.usersRepository.create(createUserDto);
      const savedUser = await this.usersRepository.save(user);
      return this.omitPassword(savedUser);
    }, '创建用户失败');
  }

  /**
   * 更新用户
   * @param id 用户ID
   * @param userData 用户数据
   */
  async updateUser(id: number, userData: Partial<User>): Promise<ResultResponse<User>> {
    // 检查用户是否存在
    const userResult = await this.findById(id);
    if (!userResult.success) {
      return ResultUtil.fail('更新用户失败: 用户不存在', 'USER_NOT_FOUND');
    }

    // 如果要更新email或username，检查是否与其他用户冲突
    if (userData.email) {
      const emailResult = await this.findByEmail(userData.email);
      if (emailResult.success && emailResult.data.id !== id) {
        return ResultUtil.fail('更新用户失败: 邮箱已被其他用户使用', 'EMAIL_ALREADY_EXISTS');
      }
    }

    if (userData.username) {
      const usernameResult = await this.findByUsername(userData.username);
      if (usernameResult.success && usernameResult.data.id !== id) {
        return ResultUtil.fail('更新用户失败: 用户名已被其他用户使用', 'USERNAME_ALREADY_EXISTS');
      }
    }

    return ResultUtil.execute(async () => {
      await this.usersRepository.update(id, userData);
      const updatedUser = await this.usersRepository.findOneBy({ id });
      return this.omitPassword(updatedUser);
    }, '更新用户失败');
  }

  /**
   * 删除用户
   * @param id 用户ID
   */
  async removeUser(id: number): Promise<ResultResponse> {
    return ResultUtil.execute(async () => {
      const resp = await this.usersRepository.delete(id);
      const msg = resp.affected === 0 ? '删除用户不存在' : '删除用户成功';
      return ResultUtil.success(undefined, msg);
    }, '删除用户失败');
  }

  /**
   * 验证用户
   * @param email 邮箱或用户名
   * @param password 密码
   */
  async validateUser(email: string, password: string): Promise<ResultResponse<User>> {
    // 尝试通过邮箱查找用户
    const userResult = await this.findByEmailOrUsername(email, true);
    if (!userResult.success) {
      return ResultUtil.fail('用户不存在或密码错误', 'INVALID_CREDENTIALS');
    }

    const user = userResult.data;

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return ResultUtil.fail('用户不存在或密码错误', 'INVALID_CREDENTIALS');
    }

    return ResultUtil.success(this.omitPassword(user));
  }

  private omitPassword(user: User): User {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }
}
