import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

import { AdminGuard } from '../auth/guards/admin.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResultResponse } from '../common/utils/result.util';

import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UsersService } from './users.service';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '获取所有用户' })
  @ApiResponse({ status: 200, description: '获取所有用户成功', type: User, isArray: true })
  findAll(): Promise<ResultResponse<User[]>> {
    return this.usersService.findAll();
  }

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 200, description: '创建用户成功', type: User })
  create(@Body() createUserDto: CreateUserDto): Promise<ResultResponse<User>> {
    return this.usersService.createUser(createUserDto);
  }

  @Put(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '更新用户' })
  @ApiResponse({ status: 200, description: '更新用户成功', type: User })
  update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<ResultResponse<User>> {
    return this.usersService.updateUser(+id, updateUserDto);
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({ status: 200, description: '删除用户成功', type: Boolean })
  remove(@Param('id') id: string): Promise<ResultResponse> {
    return this.usersService.removeUser(+id);
  }
}
