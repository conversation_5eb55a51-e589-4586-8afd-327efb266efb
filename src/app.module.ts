import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthModule } from './auth/auth.module';
import { ContractModule } from './contract/contract.module';
import { LlmModule } from './llm/llm.module';
import { PromptTemplate } from './prompt-template/entities/prompt-template.entity';
import { PromptTemplateModule } from './prompt-template/prompt-template.module';
import { User } from './users/entities/user.entity';
import { UsersModule } from './users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['config/.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'sqlite',
        database: configService.get('DATABASE_FILE', 'db.sqlite'),
        entities: [User, PromptTemplate],
        synchronize: true,
      }),
      inject: [ConfigService],
    }),
    EventEmitterModule.forRoot({
      // 设置为true以使用通配符事件
      wildcard: true,
      // 设置为true以继承父事件
      delimiter: '.',
      // 设置为true以使用newListener事件
      newListener: false,
      // 设置为true以使用removeListener事件
      removeListener: false,
      // 设置最大监听器数量
      maxListeners: 10,
      // 设置为true以使用verboseMemoryLeak
      verboseMemoryLeak: false,
      // 忽略错误
      ignoreErrors: false,
    }),
    AuthModule,
    UsersModule,
    LlmModule,
    PromptTemplateModule,
    ContractModule,
  ],
})
export class AppModule {}
