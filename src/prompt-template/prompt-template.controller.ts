import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';

import { AdminGuard } from '../auth/guards/admin.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ResultResponse } from '../common/utils/result.util';

import { CreatePromptTemplateDto } from './dto/create-prompt-template.dto';
import { UpdatePromptTemplateDto } from './dto/update-prompt-template.dto';
import { PromptTemplate } from './entities/prompt-template.entity';
import { PromptTemplateService } from './prompt-template.service';

@ApiTags('提示词模板')
@Controller('prompt-template')
@UseGuards(JwtAuthGuard)
export class PromptTemplateController {
  constructor(private readonly promptTemplateService: PromptTemplateService) {}

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '创建提示词模板' })
  @ApiResponse({ status: 201, description: '创建成功', type: PromptTemplate })
  create(
    @Body() createPromptTemplateDto: CreatePromptTemplateDto,
  ): Promise<ResultResponse<PromptTemplate>> {
    return this.promptTemplateService.create(createPromptTemplateDto);
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '更新提示词模板' })
  @ApiParam({ name: 'id', description: '提示词模板ID' })
  @ApiResponse({ status: 200, description: '更新成功', type: PromptTemplate })
  update(
    @Param('id') id: string,
    @Body() updatePromptTemplateDto: UpdatePromptTemplateDto,
  ): Promise<ResultResponse<PromptTemplate>> {
    return this.promptTemplateService.update(+id, updatePromptTemplateDto);
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '删除提示词模板' })
  @ApiParam({ name: 'id', description: '提示词模板ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  remove(@Param('id') id: string): Promise<ResultResponse> {
    return this.promptTemplateService.remove(+id);
  }

  @Get()
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '获取所有提示词模板' })
  @ApiResponse({ status: 200, description: '获取成功', type: [PromptTemplate] })
  findAll(): Promise<ResultResponse<PromptTemplate[]>> {
    return this.promptTemplateService.findAll();
  }

  @Post(':id/set_default')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: '设置默认模板' })
  @ApiParam({ name: 'id', description: '提示词模板ID' })
  @ApiResponse({ status: 200, description: '设置成功' })
  setAsDefault(@Param('id') id: string): Promise<ResultResponse<PromptTemplate>> {
    return this.promptTemplateService.setAsDefault(+id);
  }
}
