import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Equal, Not, Repository } from 'typeorm';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';

import { CreatePromptTemplateDto } from './dto/create-prompt-template.dto';
import { UpdatePromptTemplateDto } from './dto/update-prompt-template.dto';
import { PromptTemplate } from './entities/prompt-template.entity';

@Injectable()
export class PromptTemplateService {
  private readonly logger = new Logger(PromptTemplateService.name);

  constructor(
    @InjectRepository(PromptTemplate)
    private promptTemplateRepository: Repository<PromptTemplate>,
  ) {}

  /**
   * 创建提示词模板
   * @param createPromptTemplateDto 创建DTO
   */
  async create(
    createPromptTemplateDto: CreatePromptTemplateDto,
  ): Promise<ResultResponse<PromptTemplate>> {
    // 先检查模板名称是否已存在
    const existingTemplate = await this.findTemplate({
      name: createPromptTemplateDto.name,
    });

    if (existingTemplate.success) {
      return ResultUtil.fail(
        `创建提示词模板失败: 模板名 ${createPromptTemplateDto.name} 已存在`,
        'PROMPT_TEMPLATE_NAME_EXISTS',
      );
    }

    return ResultUtil.execute(async () => {
      // 使用事务确保操作的原子性
      return this.promptTemplateRepository.manager.transaction(async (manager) => {
        // 如果新模板设置为默认，则取消相同场景类型的其他默认模板
        if (createPromptTemplateDto.isDefault) {
          await manager.update(PromptTemplate, { isDefault: true }, { isDefault: false });
        }

        // 创建新模板
        const promptTemplate = manager.create(PromptTemplate, createPromptTemplateDto);
        return await manager.save(promptTemplate);
      });
    }, '创建提示词模板失败');
  }

  /**
   * 更新提示词模板
   * @param id 模板ID
   * @param updatePromptTemplateDto 更新DTO
   */
  async update(
    id: number,
    updatePromptTemplateDto: UpdatePromptTemplateDto,
  ): Promise<ResultResponse<PromptTemplate>> {
    // 检查模板是否存在
    const templateResult = await this.findTemplate({ id });
    if (!templateResult.success) {
      return ResultUtil.fail('更新提示词模板失败: 模板不存在', templateResult.errorCode);
    }

    // 场景类型不能更新
    const dbTemplate = templateResult.data;

    if (dbTemplate.isDefault && !updatePromptTemplateDto.isDefault) {
      return ResultUtil.fail(
        '更新提示词模板失败: 不能取消默认模板,请先设置其他模板为默认',
        'CANNOT_CANCEL_DEFAULT_TEMPLATE',
      );
    }

    // 如果更新模板代码，先检查是否存在相同的代码
    if (updatePromptTemplateDto.name) {
      const existingTemplate = await this.promptTemplateRepository.findOneBy({
        name: updatePromptTemplateDto.name,
        id: Not(Equal(id)),
      });

      if (existingTemplate) {
        return ResultUtil.fail(
          `更新提示词模板失败: 模板名称 ${updatePromptTemplateDto.name} 已被其他模板使用`,
          'PROMPT_TEMPLATE_NAME_EXISTS',
        );
      }
    }

    return ResultUtil.execute(async () => {
      // 使用事务确保操作的原子性
      return this.promptTemplateRepository.manager.transaction(async (manager) => {
        // 如果更新为默认模板，则取消相同场景类型的其他默认模板
        if (updatePromptTemplateDto.isDefault) {
          await manager.update(
            PromptTemplate,
            { isDefault: true, id: Not(Equal(id)) },
            { isDefault: false },
          );
        }

        // 更新模板
        await manager.update(PromptTemplate, id, updatePromptTemplateDto);
        // 获取更新后的模板
        const updatedTemplate = await manager.findOneBy(PromptTemplate, { id });
        if (!updatedTemplate) {
          throw new Error(`更新后未找到ID为 ${id} 的模板`);
        }
        return updatedTemplate;
      });
    }, '更新提示词模板失败');
  }

  /**
   * 删除提示词模板
   * @param id 模板ID
   */
  async remove(id: number): Promise<ResultResponse> {
    // 检查模板是否存在
    const templateResult = await this.findTemplate({ id });
    if (!templateResult.success) {
      return ResultUtil.success(undefined, '删除提示词模板不存在');
    }

    // 检查是否为默认模板
    const template = templateResult.data;
    if (template.isDefault) {
      return ResultUtil.fail(
        '删除提示词模板失败: 不能删除默认模板,请先设置其他模板为默认',
        'CANNOT_DELETE_DEFAULT_TEMPLATE',
      );
    }

    return ResultUtil.execute(async () => {
      const result = await this.promptTemplateRepository.delete(id);
      const message = result.affected > 0 ? '删除提示词模板成功' : '删除提示词模板不存在';
      return ResultUtil.success(undefined, message);
    }, '删除提示词模板失败');
  }

  /**
   * 查找所有提示词模板
   */
  async findAll(): Promise<ResultResponse<PromptTemplate[]>> {
    return ResultUtil.execute(async () => {
      return this.promptTemplateRepository.find({
        order: { isDefault: 'DESC', createdAt: 'DESC' },
      });
    }, '查询所有提示词模板失败');
  }

  /**
   * 获取提示词
   * @param name 模板名称
   * @param templateKey 模板key
   */
  async getLLmPrompt<T>(name?: string, templateKey?: string): Promise<ResultResponse<T>> {
    let template: PromptTemplate | null = null;

    // 优先按模板名称查找
    if (name) {
      const templateResult = await this.findTemplate({ name });
      templateResult.success && (template = templateResult.data);
    }

    // 如果未找到，查找默认
    if (!template) {
      const templateResult = await this.findTemplate({ isDefault: true });
      templateResult.success && (template = templateResult.data);
    }

    // 如果仍未找到，返回错误
    if (!template) {
      return ResultUtil.fail(`未找到可用提示词模板`, 'PROMPT_TEMPLATE_NOT_FOUND');
    }

    return ResultUtil.execute(async () => {
      // 解析提示词内容
      const promptObj = JSON.parse(template.content);
      // 根据场景类型返回相应提示词
      if (templateKey) {
        const prompt = promptObj[templateKey];
        if (!prompt) {
          return ResultUtil.fail(
            `模板中未找到可用的提示词: ${templateKey}`,
            'PROMPT_TEMPLATE_KEY_NOT_FOUND',
          );
        }
        return ResultUtil.success(prompt as T);
      }

      // 如果未指定模板key，则返回所有提示词
      return ResultUtil.success(promptObj as T);
    }, '获取提示词失败');
  }

  /**
   * 设置默认模板
   * @param id 模板ID
   */
  async setAsDefault(id: number): Promise<ResultResponse<PromptTemplate>> {
    // 检查模板是否存在
    const templateResult = await this.findTemplate({ id });
    if (!templateResult.success) {
      return ResultUtil.fail('模板不存在, 无法设置为默认模板', templateResult.errorCode);
    }

    const template = templateResult.data;
    // 如果已经是默认模板，直接返回
    if (template.isDefault) {
      return ResultUtil.success(template, '该模板已经是默认模板');
    }

    return ResultUtil.execute(async () => {
      // 使用事务确保操作的原子性
      return this.promptTemplateRepository.manager.transaction(async (manager) => {
        // 将同一场景类型的其他默认模板设置为非默认
        await manager.update(PromptTemplate, { isDefault: true }, { isDefault: false });

        // 将当前模板设置为默认
        await manager.update(PromptTemplate, { id }, { isDefault: true });

        // 获取更新后的模板
        const updatedTemplate = await manager.findOneBy(PromptTemplate, { id });

        if (!updatedTemplate) {
          throw new Error(`更新后未找到ID为 ${id} 的模板`);
        }
        return updatedTemplate;
      });
    }, '设置默认模板失败');
  }

  /**
   * 根据ID查找提示词模板
   * @param by 筛选条件
   */
  private async findTemplate(by: Record<string, any>): Promise<ResultResponse<PromptTemplate>> {
    return ResultUtil.execute(async () => {
      const template = await this.promptTemplateRepository.findOneBy(by);
      if (!template) {
        return ResultUtil.fail(
          `提示词模板 ${JSON.stringify(by)} 不存在`,
          'PROMPT_TEMPLATE_NOT_FOUND',
        );
      }
      return ResultUtil.success(template);
    }, '查找提示词模板失败');
  }
}
