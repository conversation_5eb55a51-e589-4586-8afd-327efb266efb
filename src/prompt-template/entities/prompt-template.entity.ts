import { ApiProperty } from '@nestjs/swagger';

import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class PromptTemplate {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '提示词模板ID' })
  id: number;

  @Column()
  @ApiProperty({ description: '模板名称', example: '默认' })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '模板描述', example: '系统默认提示词' })
  description: string;

  @Column({ type: 'text' })
  @ApiProperty({ description: '提示词内容,JSON格式字符串' })
  content: string;

  @Column({ default: false })
  @ApiProperty({ description: '是否为默认提示词', example: false })
  isDefault: boolean;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}
