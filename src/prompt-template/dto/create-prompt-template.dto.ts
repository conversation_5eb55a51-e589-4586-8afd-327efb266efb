import { ApiProperty } from '@nestjs/swagger';

import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreatePromptTemplateDto {
  @ApiProperty({ description: '模板名称', example: '默认提示词' })
  @IsString()
  @IsNotEmpty({ message: '模板名称不能为空' })
  name: string;

  @ApiProperty({
    description: '模板描述',
    example: '系统默认提示词',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '提示词内容,JSON格式字符串' })
  @IsString()
  @IsNotEmpty({ message: '提示词内容不能为空' })
  content: string;

  @ApiProperty({ description: '是否为默认提示词', example: false, required: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}
