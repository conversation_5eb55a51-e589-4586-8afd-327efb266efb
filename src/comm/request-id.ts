import { v4 as uuid } from 'uuid';

export interface IRequestIdOptions {
  reqHeader?: string;
  resHeader?: string;
  paramName?: string;
  generator?: () => string;
}

const mergeOptions = (options?: IRequestIdOptions) => {
  options = options || {};
  return {
    reqHeader: options.reqHeader || 'x-request-id',
    resHeader: options.resHeader || 'x-request-id',
    paramName: options.paramName || 'requestId',
    generator: options.generator || uuid,
  };
};

export const requestId = (options?: IRequestIdOptions) => {
  options = mergeOptions(options);

  return (req, res, next) => {
    const reqId =
      req[options.paramName] ||
      req.get(options.reqHeader) ||
      req.query[options.paramName] ||
      options.generator();
    req[options.paramName] = reqId;
    res.setHeader(options.resHeader, reqId);
    next();
  };
};
