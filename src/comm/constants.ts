// Context Key --- 用来loader存储
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';
export const IS_DEVELOP = !IS_PRODUCTION;
export const SERVICE_NAME = 'datlas_bff';
export const NEST_LOADER_CONTEXT_KEY = 'NEST_LOADER_CONTEXT_KEY';
export const GRAPHQL = 'graphql';

export enum ExtraDataType {
  PARTIAL_SUCCESS = 'partial_success', // 批量请求时部分成功
}

// 产品权限修改1
export enum ProductEnum {
  DATA_MARKET = 'datamarket',
  DATA_FACTORY = 'datafactory',
  MY_DATA = 'mydata',
  RESOURCE_SHARE = 'resourceshare',
  WORKFLOW = 'workflow',
  ONETABLE = 'onetable',
}

export enum ProductPrefixEnum {
  DATA_MARKET = 'dm',
  MY_DATA = 'md',
  RESOURCE_SHARE = 'rs',
  WORKFLOW = 'wf',
  ONETABLE = 'ot',
}
