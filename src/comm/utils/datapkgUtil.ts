export enum DatapkgGeometryTypeEnum {
  POINT = 'point',
  /** 线 */
  LINE = 'line',
  /** 面 */
  POLYGON = 'polygon',
  /** 文本 */
  PLAIN = 'plain',
  /** 点到点 */
  POINT_TO_POINT = 'point_to_point',
  /** 点到线 */
  POINT_TO_LINE = 'point_to_line',
  /** 点到面 */
  POINT_TO_POLYGON = 'point_to_polygon',
  /** 线到点 */
  LINE_TO_POINT = 'line_to_point',
  /** 线到线 */
  LINE_TO_LINE = 'line_to_line',
  /** 线到面 */
  LINE_TO_POLYGON = 'line_to_polygon',
  /** 面到点 */
  POLYGON_TO_POINT = 'polygon_to_point',
  /** 面到线 */
  POLYGON_TO_LINE = 'polygon_to_line',
  /** 面到面 */
  POLYGON_TO_POLYGON = 'polygon_to_polygon',
}

// 获取地理类型名称
const datapkgGeometryTypeLabelMap: Record<string, string> = {
  [DatapkgGeometryTypeEnum.POINT]: '点数据',
  [DatapkgGeometryTypeEnum.LINE]: '线数据',
  [DatapkgGeometryTypeEnum.POLYGON]: '面数据',
  [DatapkgGeometryTypeEnum.PLAIN]: '非地理数据',
  [DatapkgGeometryTypeEnum.POINT_TO_POINT]: '点->点数据',
  [DatapkgGeometryTypeEnum.POINT_TO_LINE]: '点->线数据',
  [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: '点->面数据',
  [DatapkgGeometryTypeEnum.LINE_TO_POINT]: '线->点数据',
  [DatapkgGeometryTypeEnum.LINE_TO_LINE]: '线->线数据',
  [DatapkgGeometryTypeEnum.LINE_TO_POLYGON]: '线->面数据',
  [DatapkgGeometryTypeEnum.POLYGON_TO_POINT]: '面->点数据',
  [DatapkgGeometryTypeEnum.POLYGON_TO_LINE]: '面->线数据',
  [DatapkgGeometryTypeEnum.POLYGON_TO_POLYGON]: '面->面数据',
};
export const getDatapkgGeometryTypeLabel = (type: string) => {
  return (
    datapkgGeometryTypeLabelMap[type] || datapkgGeometryTypeLabelMap[DatapkgGeometryTypeEnum.PLAIN]
  );
};

// 获取数据包地理类型对应的图标
const dup_icon_polygon = 'location-to-polygon';
const datapkgGeometryTypeIconMap: Record<string, string> = {
  [DatapkgGeometryTypeEnum.POINT]: 'location',
  [DatapkgGeometryTypeEnum.LINE]: 'line',
  [DatapkgGeometryTypeEnum.POLYGON]: 'layer',
  [DatapkgGeometryTypeEnum.PLAIN]: 'file',
  [DatapkgGeometryTypeEnum.POINT_TO_POINT]: 'location-to-location',
  [DatapkgGeometryTypeEnum.POINT_TO_LINE]: 'location-to-polygon',
  [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.LINE_TO_POINT]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.LINE_TO_LINE]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.LINE_TO_POLYGON]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POINT]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.POLYGON_TO_LINE]: dup_icon_polygon,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POLYGON]: dup_icon_polygon,
};
export const getDatapkgGeometryTypeIcon = (type: string) => {
  return (
    datapkgGeometryTypeIconMap[type] || datapkgGeometryTypeIconMap[DatapkgGeometryTypeEnum.PLAIN]
  );
};

// 获取数据包地理类型对应的颜色
const dup_color_magenta = 'var(--dmc-magenta-500-color)';
const datapkgGeometryTypeColorMap: Record<string, string> = {
  [DatapkgGeometryTypeEnum.POINT]: 'var(--dmc-blue-500-color)',
  [DatapkgGeometryTypeEnum.LINE]: 'var(--dmc-red-500-color)',
  [DatapkgGeometryTypeEnum.POLYGON]: 'var(--dmc-yellow-500-color)',
  [DatapkgGeometryTypeEnum.PLAIN]: 'var(--dmc-green-500-color)',
  [DatapkgGeometryTypeEnum.POINT_TO_POINT]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POINT_TO_LINE]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POINT_TO_POLYGON]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.LINE_TO_POINT]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.LINE_TO_LINE]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.LINE_TO_POLYGON]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POINT]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POLYGON_TO_LINE]: dup_color_magenta,
  [DatapkgGeometryTypeEnum.POLYGON_TO_POLYGON]: dup_color_magenta,
};
export const getDatapkgGeometryTypeColor = (type: string) => {
  return (
    datapkgGeometryTypeColorMap[type] || datapkgGeometryTypeColorMap[DatapkgGeometryTypeEnum.PLAIN]
  );
};

export enum DatapkgStoreTypeEnum {
  TABLE = 'table',
  VIEW = 'view',
  SQL = 'sql',
  EXTABLE = 'extable',
  COLLABORATION = 'collaboration',
  COLLABORATION_V2 = 'collaboration_v2',
  COLLABORATION_CHILD = 'collaboration_child',
  CUSTOMER = 'customer',
  MVIEW = 'mview',
}

// 获取数据包存储名称
export const datapkgStoreTypeLabelMap: Record<string, string> = {
  [DatapkgStoreTypeEnum.TABLE]: 'Table',
  [DatapkgStoreTypeEnum.VIEW]: '数据库视图',
  [DatapkgStoreTypeEnum.SQL]: 'SQL',
  [DatapkgStoreTypeEnum.EXTABLE]: '外链表格',
  [DatapkgStoreTypeEnum.COLLABORATION]: '协同',
  [DatapkgStoreTypeEnum.COLLABORATION_V2]: '协同',
  [DatapkgStoreTypeEnum.COLLABORATION_CHILD]: '协同数据子包',
  [DatapkgStoreTypeEnum.CUSTOMER]: '私有数据',
  [DatapkgStoreTypeEnum.MVIEW]: '数据库物化视图',
};
export const getDatapkgStoreTypeLabel = (packageType?: string, defaultLabel = '') => {
  return (packageType && datapkgStoreTypeLabelMap[packageType]) || defaultLabel;
};

// 获取数据包存储类型对应的颜色
export const datapkgStoreTypeColorMap: Record<string, string> = {
  [DatapkgStoreTypeEnum.TABLE]: 'blue-700',
  [DatapkgStoreTypeEnum.SQL]: 'purple-700',
  [DatapkgStoreTypeEnum.EXTABLE]: 'blue-900',
  [DatapkgStoreTypeEnum.COLLABORATION]: 'orange-700',
  [DatapkgStoreTypeEnum.COLLABORATION_V2]: 'orange-700',
};
export const getDatapkgStoreTypeColor = (packageType?: string, defaultColor = 'magenta-700') => {
  return (packageType && datapkgStoreTypeColorMap[packageType]) || defaultColor;
};
