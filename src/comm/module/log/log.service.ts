import { join } from 'path';
import { HttpException, Injectable } from '@nestjs/common';
import * as dayJs from 'dayjs';
import { get } from 'lodash';
import { createLogger, format, LogEntry, Logger as WinstonLogger, transports } from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import * as Transport from 'winston-transport';
import { IS_DEVELOP } from '@comm/constants';
import { WinstonSentryTransport } from '@comm/winston-sentry-transport';
import { ConfigService } from '@globalModule/config';
import { RequestContext } from '@globalModule/request-context';
import { LOG_PATH, LOG_TIME_FORMAT } from './constants';

export type ErrorType = Error | string | Record<string, any>;

const { combine, printf } = format;
const enum LogLevelEnum {
  ERROR = 'error',
  WARN = 'warn',
  HELP = 'help',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
  SILLY = 'silly',
}

@Injectable()
export class LogService {
  private winstonLogger: WinstonLogger;

  constructor(private readonly config: ConfigService) {
    const ts: Transport[] = [
      new transports.Console({
        level: config.getServiceLogCmdLevel(),
        handleExceptions: false,
      }),
    ];
    if (IS_DEVELOP || config.getServiceLogFileEnable()) {
      ts.push(
        new DailyRotateFile({
          level: config.getServiceLogFileLevel(),
          filename: join(LOG_PATH, 'datlas_bff.%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          zippedArchive: false, // 是否压缩日志文件
          maxSize: '300m',
          maxFiles: '30d',
        }),
      );
    }
    if (config.getServiceLogSentryEnable()) {
      ts.push(
        new WinstonSentryTransport({
          handleExceptions: false,
          sentry: {
            dsn: config.getServiceLogSentryDsn(),
            logLevel: config.getServiceLogSentryLevel(),
            environment: config.getServiceLogSentryEnv(),
          },
        }),
      );
    }

    this.winstonLogger = createLogger({
      format: combine(
        printf(
          // 日志信息格式化
          (info) =>
            `\n${info.timestamp} [${info.level.toUpperCase()}] [REQUEST_ID: ${info.requestId}]${
              info.backendRequestId ? ` [BACKEND_REQUEST_ID: ${info.backendRequestId}]` : ''
            }\n${info.message}`,
        ),
      ),
      exitOnError: false,
      transports: ts,
    });
  }

  debug(message: ErrorType): WinstonLogger {
    return this.winstonLogger.log(LogService.serialize(LogLevelEnum.DEBUG, message));
  }

  info(message: ErrorType): WinstonLogger {
    return this.winstonLogger.log(LogService.serialize(LogLevelEnum.INFO, message));
  }

  warn(message: ErrorType): WinstonLogger {
    return this.winstonLogger.log(LogService.serialize(LogLevelEnum.WARN, message));
  }

  error(message: ErrorType): WinstonLogger {
    return this.winstonLogger.log(LogService.serialize(LogLevelEnum.ERROR, message));
  }

  static serialize(level: LogLevelEnum, error: ErrorType): LogEntry {
    const st = RequestContext.currentContext;
    const requestId = st?.getRequestId();
    const timestamp = dayJs().format(LOG_TIME_FORMAT);

    const info: LogEntry = { level, message: '', requestId, timestamp };

    if (typeof error === 'string') {
      info.message = error;
      return info;
    }

    if (error instanceof Error && error.stack) {
      info.message = error.stack;
      info.error = error;
      if (error instanceof HttpException) {
        info.backendRequestId = get(error.getResponse(), 'backendRequestId');
      }
      return info;
    }

    info.message = JSON.stringify(error, null, 2);
    info.error = error;
    return info;
  }
}
