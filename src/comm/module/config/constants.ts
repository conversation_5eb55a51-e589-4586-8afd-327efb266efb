export const COMMIT_TAG = 'COMMIT_TAG';
export const COMMIT_HASH = 'COMMIT_HASH';
export const SERVICE_PORT = 'SERVICE_PORT';
export const SERVICE_MOCK = 'SERVICE_MOCK';
export const SERVICE_GRAPHQL_DEBUG = 'SERVICE_GRAPHQL_DEBUG';
export const SERVICE_SWAGGER_ENABLE = 'SERVICE_SWAGGER_ENABLE';
export const SERVICE_LOG_PATH = 'SERVICE_LOG_PATH';
export const SERVICE_LOG_CMD_LEVEL = 'SERVICE_LOG_CMD_LEVEL';
export const SERVICE_LOG_FILE_ENABLE = 'SERVICE_LOG_FILE_ENABLE';
export const SERVICE_LOG_FILE_LEVEL = 'SERVICE_LOG_FILE_LEVEL';
export const SERVICE_LOG_SENTRY_LEVEL = 'SERVICE_LOG_SENTRY_LEVEL';
export const SERVICE_LOG_SENTRY_ENABLE = 'SERVICE_LOG_SENTRY_ENABLE';
export const SERVICE_LOG_SENTRY_DSN = 'SERVICE_LOG_SENTRY_DSN';
export const SERVICE_LOG_SENTRY_ENV = 'SERVICE_LOG_SENTRY_ENV';
export const SERVICE_MDT_PRODUCT_AUTH_MAP = 'SERVICE_MDT_PRODUCT_AUTH_MAP';
export const SERVICE_CLOUD_APP_ADMIN = 'SERVICE_CLOUD_APP_ADMIN';
export const SERVICE_AUTH_FAKE_TOKEN = 'SERVICE_AUTH_FAKE_TOKEN';
export const SERVICE_DISPATCHER_MAX_CONCURRENCY = 'SERVICE_DISPATCHER_MAX_CONCURRENCY';
// export const SERVICE_ONT_TABLE_REMINDERS_TEMPLATE = 'SERVICE_ONT_TABLE_REMINDERS_TEMPLATE';
// 默认配置加载url
export const DEFAULT_MDT_CONFIG_SERVER_FORCE = 'consul://**************:8501/mdt/config';
export const DEFAULT_SERVICE_LOG_SENTRY_ENV = 'dev';
export const DEFAULT_COMMIT_VALUE = 'unkown';
// consul key 及 默认值
export const CONSUL_SERVICE_NAME = 'datlas_bff';
export const CONSUL_SERVICE_PORT = `port`;
export const CONSUL_SERVICE_MOCK = `mock`;
export const CONSUL_SERVICE_GRAPHQL_DEBUG = `graphql_debug`;
export const CONSUL_SERVICE_SWAGGER_ENABLE = `swagger_enable`;
export const CONSUL_SERVICE_LOG_CMD_LEVEL = `log_cmd_level`;
export const CONSUL_SERVICE_LOG_FILE_ENABLE = `log_file_enable`;
export const CONSUL_SERVICE_LOG_FILE_LEVEL = `log_file_level`;
export const CONSUL_SERVICE_LOG_SENTRY_ENABLE = `log_sentry_enable`;
export const CONSUL_SERVICE_LOG_SENTRY_LEVEL = `log_sentry_level`;
export const CONSUL_SERVICE_LOG_SENTRY_DSN = `log_sentry_dsn`;
export const CONSUL_SERVICE_MDT_PRODUCT_AUTH_MAP = `mdt_product_auth_map`;
export const CONSUL_SERVICE_CLOUD_APP_ADMIN = `cloud_app_admin`;
export const CONSUL_SERVICE_AUTH_FAKE_TOKEN = `auth_fake_token`;
export const CONSUL_SERVICE_DISPATCHER_MAX_CONCURRENCY = `dispatcher_max_concurrency`;
// export const CONSUL_SERVICE_ONT_TABLE_REMINDERS_TEMPLATE = `ont_table_reminders_template`;
// 默认值定义
export const DEFAULT_CONSUL_SERVICE_PORT = 4000;
export const DEFAULT_CONSUL_SERVICE_MOCK = false;
export const DEFAULT_CONSUL_SERVICE_GRAPHQL_DEBUG = false;
export const DEFAULT_CONSUL_SERVICE_SWAGGER_ENABLE = true;
export const DEFAULT_CONSUL_SERVICE_LOG_CMD_LEVEL = 'info';
export const DEFAULT_CONSUL_SERVICE_LOG_FILE_ENABLE = true;
export const DEFAULT_CONSUL_SERVICE_LOG_FILE_LEVEL = 'info';
export const DEFAULT_CONSUL_SERVICE_LOG_SENTRY_ENABLE = true;
export const DEFAULT_CONSUL_SERVICE_LOG_SENTRY_LEVEL = 'error';
export const DEFAULT_CONSUL_SERVICE_LOG_SENTRY_DSN =
  'https://<EMAIL>/21';
export const DEFAULT_CONSUL_SERVICE_MDT_PRODUCT_AUTH_MAP =
  'dataapp,681301,datamarket,681302,datafactory,681309,workflow,684323,onetable,2003617';
export const DEFAULT_CONSUL_SERVICE_CLOUD_APP_ADMIN = '682535,682536';
export const DEFAULT_CONSUL_SERVICE_DISPATCHER_MAX_CONCURRENCY = 4;
// export const DEFAULT_CONSUL_SERVICE_ONT_TABLE_REMINDERS_TEMPLATE = '催办@@@Reminders';
// dev
// export const DEFAULT_CONSUL_SERVICE_CLOUD_APP_ADMIN = '682423,682430';
export const DEFAULT_CONSUL_SERVICE_AUTH_FAKE_TOKEN = '';
// api key
export const API_FLOWORK_URL = 'API_FLOWORK_URL';
export const API_AUTH_URL = 'API_AUTH_URL';
export const API_SCHEDULER_URL = 'API_SCHEDULER_URL';
export const API_DATAMAP_V2_URL = 'API_DATAMAP_V2_URL';
export const API_DISPATCHER_URL = 'API_DISPATCHER_URL';
export const API_RPC_URL = 'API_RPC_URL';
export const API_PROXY_URL = 'API_PROXY_URL';
export const API_OPENAI_MODEL = 'API_OPENAI_MODEL';
export const REQUEST_MODE_HEADER = 'header';
export const REQUEST_MODE_K8S = 'k8s';
export const REQUEST_MODE_INTERNAL_URL = 'internal_url';
export const API_CITIZEN_OAUTH2 = 'API_CITIZEN_OAUTH2';
export const API_CITIZEN_KEY = 'API_CITIZEN_KEY';
export const API_CITIZEN_SECRET = 'API_CITIZEN_SECRET';
export const API_CITIZEN_ALGO = 'API_CITIZEN_ALGO';
export const API_CITIZEN_URL = 'API_CITIZEN_URL';
// consul key
export const CONSUL_API_REQUEST_MODE = `api_request_mode`;
export const CONSUL_API_GATEWAY_IP = `api_gateway_ip`;
export const CONSUL_API_GATEWAY_PORT = `api_gateway_port`;
export const CONSUL_API_FLOWORK_URL = `api_flowork_url`;
export const CONSUL_API_SCHEDULER_URL = `api_scheduler_url`;
export const CONSUL_API_AUTH_URL = `api_auth_url`;
export const CONSUL_API_DATAMAP_V2_URL = `api_datamap_v2_url`;
export const CONSUL_API_DISPATCHER_URL = `api_dispatcher_url`;
export const CONSUL_API_RPC_URL = `api_rpc_url`;
export const CONSUL_API_PROXY_URL = `api_proxy_url`;
export const CONSUL_API_OPENAI_MODEL = `api_openai_model`;
// 市民云相关
export const CONSUL_API_CITIZEN_OAUTH2 = `api_citizen_oauth2`;
export const CONSUL_API_CITIZEN_KEY = `api_citizen_key`;
export const CONSUL_API_CITIZEN_SECRET = `api_citizen_secret`;
export const CONSUL_API_CITIZEN_ALGO = `api_citizen_algo`;
export const CONSUL_API_CITIZEN_URL = `api_citizen_url`;
// 默认值定义
export const DEFAULT_CONSUL_API_REQUEST_MODE = REQUEST_MODE_K8S;
export const DEFAULT_CONSUL_API_GATEWAY_IP = `**************`;
export const DEFAULT_CONSUL_API_GATEWAY_PORT = 9999;
export const DEFAULT_CONSUL_API_OPENAI_MODEL = 'gpt-4-vision-preview';
export const DEFAULT_CONSUL_API_FLOWORK_URL = 'https://datlas.maicedata-dev.com/api/flowork';
export const DEFAULT_CONSUL_API_SCHEDULER_URL = 'https://datlas.maicedata-dev.com/api/scheduler';
export const DEFAULT_CONSUL_API_AUTH_URL = 'https://datlas.maicedata-dev.com/api/auth';
export const DEFAULT_CONSUL_API_DATAMAP_V2_URL = 'https://datlas.maicedata-dev.com/api/datamap/v2';
export const DEFAULT_CONSUL_API_DISPATCHER_URL = 'https://api.maicedata-dev.com/dispatcher';
export const DEFAULT_CONSUL_API_RPC_URL = 'https://datlas.maicedata-dev.com/worker';
export const DEFAULT_CONSUL_API_PROXY_URL = 'http://proxy.maicedata-dev.com';
export const DEFAULT_CONSUL_API_CITIZEN_OAUTH2 = 'https://delivery.metrodata.cn/delta_dev/oauth2';
export const DEFAULT_CONSUL_API_CITIZEN_KEY = `datlas_bff`;
export const DEFAULT_CONSUL_API_CITIZEN_SECRET = ``;
export const DEFAULT_CONSUL_API_CITIZEN_ALGO = `hmac_sm3`;
export const DEFAULT_CONSUL_API_CITIZEN_URL = 'https://delivery.metrodata.cn/delta_dev';
