import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { split } from 'lodash';
import {
  API_AUTH_URL,
  API_CITIZEN_ALGO,
  API_CITIZEN_KEY,
  API_CITIZEN_OAUTH2,
  API_CITIZEN_SECRET,
  API_CITIZEN_URL,
  API_DATAMAP_V2_URL,
  API_DISPATCHER_URL,
  API_FLOWORK_URL,
  API_OPENAI_MODEL,
  API_PROXY_URL,
  API_RPC_URL,
  API_SCHEDULER_URL,
  COMMIT_HASH,
  COMMIT_TAG,
  SERVICE_AUTH_FAKE_TOKEN,
  SERVICE_CLOUD_APP_ADMIN,
  SERVICE_DISPATCHER_MAX_CONCURRENCY,
  SERVICE_GRAPHQL_DEBUG,
  SERVICE_LOG_CMD_LEVEL,
  SERVICE_LOG_FILE_ENABLE,
  SERVICE_LOG_FILE_LEVEL,
  SERVICE_LOG_PATH,
  SERVICE_LOG_SENTRY_DSN,
  SERVICE_LOG_SENTRY_ENABLE,
  SERVICE_LOG_SENTRY_ENV,
  SERVICE_LOG_SENTRY_LEVEL,
  SERVICE_MDT_PRODUCT_AUTH_MAP,
  SERVICE_MOCK,
  SERVICE_PORT,
  SERVICE_SWAGGER_ENABLE,
} from './constants';

@Injectable()
export class ConfigService {
  constructor(private readonly config: NestConfigService) {}

  getCommitTag() {
    return this.config.get(COMMIT_TAG);
  }

  getCommitHash() {
    return this.config.get(COMMIT_HASH);
  }

  getServicePort() {
    return this.config.get(SERVICE_PORT);
  }

  getServiceMocks() {
    return this.config.get(SERVICE_MOCK);
  }

  getServiceGraphqlDebug() {
    return this.config.get(SERVICE_GRAPHQL_DEBUG);
  }

  getServiceLogPath() {
    return this.config.get(SERVICE_LOG_PATH);
  }

  getServiceLogCmdLevel() {
    return this.config.get(SERVICE_LOG_CMD_LEVEL);
  }

  getServiceLogFileEnable() {
    return this.config.get(SERVICE_LOG_FILE_ENABLE);
  }

  getServiceLogFileLevel() {
    return this.config.get(SERVICE_LOG_FILE_LEVEL);
  }

  getServiceLogSentryEnable() {
    return this.config.get(SERVICE_LOG_SENTRY_ENABLE);
  }

  getServiceLogSentryLevel() {
    return this.config.get(SERVICE_LOG_SENTRY_LEVEL);
  }

  getServiceLogSentryDsn() {
    return this.config.get(SERVICE_LOG_SENTRY_DSN);
  }

  getServiceLogSentryEnv() {
    return this.config.get(SERVICE_LOG_SENTRY_ENV);
  }

  getServiceSwaggerEnable() {
    return this.config.get(SERVICE_SWAGGER_ENABLE);
  }

  getServiceMdtProductAuthMap() {
    const prodAuthConfig = this.config.get(SERVICE_MDT_PRODUCT_AUTH_MAP);
    const map = {};
    const list = split(prodAuthConfig, ',');
    for (let i = 0, len = list.length; i < len; ) {
      map[list[i + 1]] = list[i];
      i += 2;
    }
    return map;
  }

  getServiceCloudAppAdmin() {
    return this.config.get(SERVICE_CLOUD_APP_ADMIN);
  }

  getServiceDispatcherMaxConcurrency() {
    return this.config.get(SERVICE_DISPATCHER_MAX_CONCURRENCY);
  }

  getServiceAuthFakeToken() {
    return this.config.get(SERVICE_AUTH_FAKE_TOKEN);
  }

  getApiAuthUrl() {
    return this.config.get(API_AUTH_URL);
  }

  getApiSchedulerUrl() {
    return this.config.get(API_SCHEDULER_URL);
  }

  getApiDatamapV2Url() {
    return this.config.get(API_DATAMAP_V2_URL);
  }

  getApiFloworkUrl() {
    return this.config.get(API_FLOWORK_URL);
  }

  getApiDispatcherUrl() {
    return this.config.get(API_DISPATCHER_URL);
  }

  getApiRpcUrl() {
    return this.config.get(API_RPC_URL);
  }

  getApiProxyUrl() {
    return this.config.get(API_PROXY_URL);
  }

  getApiOpenaiModel() {
    return this.config.get(API_OPENAI_MODEL);
  }

  getApiCitizenOauth2() {
    return this.config.get(API_CITIZEN_OAUTH2);
  }

  getApiCitizenKey() {
    return this.config.get(API_CITIZEN_KEY);
  }

  getApiCitizenSecret() {
    return this.config.get(API_CITIZEN_SECRET);
  }

  getApiCitizenAlgo() {
    return this.config.get(API_CITIZEN_ALGO);
  }

  getApiCitizenUrl() {
    return this.config.get(API_CITIZEN_URL);
  }
}
