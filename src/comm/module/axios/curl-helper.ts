import { AxiosRequestConfig, AxiosRequestHeaders } from 'axios';

const IGNORED_HEADERS = ['common', 'delete', 'get', 'head', 'patch', 'post', 'put'];

export const r2curl = (config: AxiosRequestConfig) => {
  const configHeaders = config.headers;
  const configlData = config.data;
  const configMethod = config.method.toUpperCase();
  const configBaseUrl = config.baseURL;
  const configUrl = config.url;
  const configParams = config.params;

  function getHeaders() {
    const headers = {};
    for (const property in configHeaders) {
      if (!IGNORED_HEADERS.includes(property)) {
        headers[property] = configHeaders[property];
      }
    }
    return headers as AxiosRequestHeaders;
  }

  function getBody(headers: AxiosRequestHeaders) {
    let body = '';
    if (
      typeof configlData !== 'undefined' &&
      configlData !== '' &&
      configlData !== null &&
      configMethod !== 'GET'
    ) {
      if (typeof config.data === 'object') {
        body = JSON.stringify(config.data);
        headers['Content-Type'] = 'application/json;charset=utf-8';
      } else {
        body = config.data;
      }
    }
    return body;
  }

  function getQueryString() {
    if (config.paramsSerializer) {
      const params = config.paramsSerializer(configParams);
      if (!params || params.length === 0) return '';
      if (params.startsWith('?')) return params;
      return `?${params}`;
    }

    let params = '';
    let i = 0;
    for (const param in configParams) {
      if (Object.hasOwnProperty.call(configParams, param)) {
        params += i !== 0 ? `&${param}=${configParams[param]}` : `?${param}=${configParams[param]}`;
        i++;
      }
    }
    return params;
  }

  function getUrl() {
    let url = configUrl;
    if (configBaseUrl) {
      url = (configBaseUrl + '/' + url)
        .replace(/\/{2,}/g, '/')
        .replace('http:/', 'http://')
        .replace('https:/', 'https://');
    }

    const qs = getQueryString();
    if (qs !== '') {
      url += qs;
    }
    return url;
  }

  const headers = getHeaders();
  const body = getBody(headers);
  const curlMethod = `-X ${configMethod}`;
  const curlUrl = `'${getUrl()}'`;
  let curlHeaders = '';
  for (const property in headers) {
    curlHeaders += ` -H '${property}:${headers[property]}'`;
  }
  let curlBody = '';
  if (body) {
    curlBody = `--data '${body}'`;
  }

  return `curl ${curlMethod} ${curlUrl} ${curlHeaders} ${curlBody}`.trim().replace(/\s{2,}/g, ' ');
};
