import {
  createParamDecorator,
  ExecutionContext,
  InternalServerErrorException,
  Type,
} from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { GRAPHQL, NEST_LOADER_CONTEXT_KEY } from '@comm/constants';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';

/**
 * 自定义装饰器 Loader
 */
export const Loader = createParamDecorator(
  (factory: Type<NestDataLoaderFactory>, context: ExecutionContext) => {
    if (context.getType<string>() !== GRAPHQL) {
      throw new InternalServerErrorException('@Loader 只能在Graphql请求中使用');
    }

    if (!factory) {
      throw new InternalServerErrorException(`@Loader 需要传入Factory`);
    }

    const ctx = GqlExecutionContext.create(context).getContext();
    if (!ctx[NEST_LOADER_CONTEXT_KEY]) {
      throw new InternalServerErrorException(`Loader使用前需要全局注册 ${APP_INTERCEPTOR}`);
    }

    return ctx[NEST_LOADER_CONTEXT_KEY].getLoader(factory);
  },
);
