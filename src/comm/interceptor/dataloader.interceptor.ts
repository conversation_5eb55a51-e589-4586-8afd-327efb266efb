import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  InternalServerErrorException,
  NestInterceptor,
  Type,
} from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Observable } from 'rxjs';
import { GRAPHQL, NEST_LOADER_CONTEXT_KEY } from '@comm/constants';
import { NestDataLoaderFactory } from '@comm/nest.dataloader.factory';

@Injectable()
export class DataloaderInterceptor implements NestInterceptor {
  constructor(private readonly moduleRef: ModuleRef) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (context.getType<string>() !== GRAPHQL) {
      return next.handle();
    }

    const ctx = GqlExecutionContext.create(context).getContext();
    if (ctx[NEST_LOADER_CONTEXT_KEY] === undefined) {
      // 缓存loader实例
      const loaders: Record<string, Promise<NestDataLoaderFactory>> = {};
      ctx[NEST_LOADER_CONTEXT_KEY] = {
        loaders,
        getLoader: (factory: Type<NestDataLoaderFactory>) => {
          const name = factory.name;
          if (loaders[name] === undefined) {
            loaders[name] = (async () => {
              try {
                return await this.moduleRef.create(factory);
              } catch (e) {
                throw new InternalServerErrorException(`${name} Loader实例化失败：` + e);
              }
            })();
          }
          return loaders[name];
        },
      };
    }
    return next.handle();
  }
}
