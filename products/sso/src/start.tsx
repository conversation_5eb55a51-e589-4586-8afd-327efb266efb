import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON><PERSON><PERSON><PERSON>outer, HashRouter } from 'react-router-dom';
import { microMount } from '@mdtProSso/utils/microUtil';
import { AppController } from './app/AppController';
import App from './app';
import { BASE_NAME, ENABLE_HASH_ROUTER, IS_MICRO_ENV, MICRO_APP_NAME } from './config';
import reportWebVitals from './reportWebVitals';

export const mount = () => {
  console.log(MICRO_APP_NAME, 'mount');
  ReactDOM.render(
    <React.StrictMode>
      {ENABLE_HASH_ROUTER ? (
        <HashRouter basename={BASE_NAME}>
          <App />
        </HashRouter>
      ) : (
        <BrowserRouter basename={BASE_NAME}>
          <App />
        </BrowserRouter>
      )}
    </React.StrictMode>,
    document.getElementById('root'),
  );
};

export const unmount = () => {
  console.log(MICRO_APP_NAME, 'unmount');
  AppController.destroy();
  const ele = document.getElementById('root');
  ele && ReactDOM.unmountComponentAtNode(ele);
};

// 启动
IS_MICRO_ENV ? microMount(mount, unmount) : mount();

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
