import { initCommConfig } from '@mdtProMicroModules/datlas/datlasConfig';

const cfs = initCommConfig('my-data', {
  isDevelop: __IS_DEVELOPMENT,
  developProxyApiUrl: __DEVELOP_PROXY_API_URL,
  developEnvOrigin: __DEVELOP_ENV_ORIGIN,
});
__webpack_public_path__ = cfs.deployPublicPath;

export * from '@mdtProMicroModules/datlas/datlasConfig';
// 文件上传配置
export const UPLOAD_MAX_MB = cfs.uploadMaxMb || 500;
