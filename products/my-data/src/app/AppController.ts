import { History } from 'history';
import { initBffCommonService } from '@mdtProComm/bff-services';
import { ModuleIdEnum, ProductPrefixEnum } from '@mdtProComm/constants';
import { DatasetsModel } from '@mdtProComm/models/DatasetsModel';
import { DatasetsModelBff } from '@mdtProComm/models/DatasetsModelBff';
import { DatlasAppController } from '@mdtProMicroModules/datlas/app/DatlasAppController';
import { RouterController } from '../_util/RouterController';
import { UserPermissionController } from '../_util/UserPermissionController';
import { ENABLE_BFF } from '../config';
import i18n from '../languages';
import { AppLayout } from './AppLayout';
import { AppSideMenuController } from './AppSideMenuController';

/**
 * 使用单例模式
 */
class AppController extends DatlasAppController<RouterController, UserPermissionController, AppSideMenuController> {
  private constructor(history: History) {
    super({ i18n });
    initBffCommonService(ProductPrefixEnum.MY_DATA);
    this.routerController = new RouterController(history, this);
  }

  public getCustomAppLayout() {
    return AppLayout;
  }

  protected async afterAuthSuccess() {
    await super.afterAuthSuccess();
    this.initAppReleation();
  }

  // 构造
  private initAppReleation() {
    // 初始化完必备信息后，构建用户的权限
    const upc = new UserPermissionController(this.getUserPermission()!, this.getPermissionController());
    this.userPermissionController = upc;
    this.appHeaderController = this.initAppHeader({
      defaultProduct: ModuleIdEnum.MY_DATA,
      defaultModule: ModuleIdEnum.HOME,
      dynamicOpt: { enableMenuMd: false },
    });
    // 初始化路由
    this.routerController!.initRoutes();
    // 初始化左侧菜单
    this.appSideMenuController = new AppSideMenuController(this);
    this.appSideMenuController.initMenus();
    if (!ENABLE_BFF) {
      DatasetsModel.init({
        hasUserManageDbLinkPermission: upc.getUserManageDbLinkPermission(),
      });
    } else {
      DatasetsModelBff.init({
        hasUserManageDbLinkPermission: upc.getUserManageDbLinkPermission(),
      });
    }
  }
}

export { AppController };
