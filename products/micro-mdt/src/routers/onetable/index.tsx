import { ModuleIdEnum } from '@mdtProComm/constants';
import { useSubModule } from '../../_util/useSubModule';
import { MicroOneTable } from '../../pages/micro-one-table';
import { useOneTableSubModule } from './useOneTableSubModule';

const OneTable = () => {
  const subModule = useSubModule(ModuleIdEnum.ONE_TABLE);
  const oneTableSubModule = useOneTableSubModule();
  return subModule || oneTableSubModule || <MicroOneTable />;
};

export default OneTable;
