import { FC } from 'react';
import loadable from '@loadable/component';
import { IRoute } from '@mdtProComm/controllers/BaseRouterController';
import { AppController } from './AppController';

const AppHeader = loadable(() => import('@mdtProMicroModules/pages/app-header'));

interface IAppLayoutProps {
  headerLess?: boolean;
  sideMenuLess?: boolean;
  item: IRoute;
  controller: AppController;
}

const AppLayout: FC<IAppLayoutProps> = ({ headerLess, item, controller }) => {
  return (
    <div className="micro_app_wrap">
      {headerLess ? null : <AppHeader controller={controller.getAppHeaderController()} />}
      <item.View />
    </div>
  );
};

export { AppLayout };
