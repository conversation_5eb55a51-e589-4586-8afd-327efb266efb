{"name": "micro-mdt", "cnName": "<PERSON><PERSON><PERSON>", "description": "Datlas---集数据收集，数据治理，数据监控，数据分析，数据可视化于一体的综合平台", "version": "2.40.23", "private": true, "scripts": {"start": "cross-env DEVELOP_ENV=dev craco start", "start:staging": "cross-env DEVELOP_ENV=staging craco start", "start:debug": "cross-env DEVELOP_ENV=debug craco start", "release": "CI=false craco build", "release:analyze": "craco build --analyze", "test": "craco test"}, "cracoConfig": "craco.config.js", "dependencies": {"@mdt/product-micro-modules": "^1.48.23", "@micro-zoe/micro-app": "alpha"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}