.quality-control-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 20px;
}

.home-table-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  .icon {
    width: 24px;
    height: 24px;
    padding: 6px;
    cursor: pointer;
  }
}

.table-quality-list_title {
  display: flex;
  flex-grow: 1;
  gap: 12px;
  color: var(--dmc-text-3);
  font-weight: 400;
  font-size: 13px;
  line-height: 18.2px;
  cursor: pointer;

  &-tag {
    display: inline-flex;
    margin-left: 3px;
    padding: 0 6px;
    color: var(--dmc-text-5);
    background-color: var(--dmc-text-1);
    border-radius: 12px;
  }

  &-select {
    color: var(--dmc-text-8);
    font-weight: 500;
    line-height: 18px;

    .table-quality-list_title-tag {
      color: var(--dmc-text-8);
      background-color: var(--dmc-red-700-color);
    }
  }
}

.datapkgName {
  color: var(--dmc-blue-500-color);
  cursor: pointer;
}

.table-quality-list_refresh-btn {
  margin-left: 8px;
}
