import React, { useEffect } from 'react';
import { GlobalRegistry } from '@designable/core';
import { TextWidget, useDesigner } from '@designable/react';
import { observer } from '@formily/react';
import { useMemoizedFn } from 'ahooks';
import { Button, Radio, Space, Upload } from 'antd';
import { CrossWindowActionEnum, LanguageEnum } from '@mdtProComm/constants';
import { getQFromUrl } from '@mdtProComm/utils/urlUtil';
import i18n from '../../../languages';
import { initSchema, saveSchema, sentInited, uploadSchema, useSchema } from '../service';

const enum DesignerLangEnum {
  CN = 'zh-cn',
  EN = 'en-us',
}

const langMap: Record<LanguageEnum, DesignerLangEnum> = {
  [LanguageEnum.CN]: DesignerLangEnum.CN,
  [LanguageEnum.EN]: DesignerLangEnum.EN,
};

export const ActionsWidget = observer(() => {
  const designer = useDesigner();
  const { enableSchemaSelect } = getQFromUrl();

  const listenMessage = useMemoizedFn(({ data }: MessageEvent) => {
    const { action, data: outData } = data;
    if (CrossWindowActionEnum.FORMILY_INIT_SCHEMA === action) {
      initSchema(designer, outData);
    } else if (CrossWindowActionEnum.REQUEST_FORMILY_SCHEMA === action) {
      useSchema(designer);
    }
  });

  useEffect(() => {
    GlobalRegistry.setDesignerLanguage(langMap[i18n.getLocaleName() as LanguageEnum] || DesignerLangEnum.CN);

    if (enableSchemaSelect) {
      window.addEventListener('message', listenMessage);
      sentInited();
    }
    return () => {
      window.removeEventListener('message', listenMessage);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const schemaSelectTools = enableSchemaSelect ? (
    <Space style={{ marginRight: 50 }}>
      <Button
        type="primary"
        onClick={() => {
          useSchema(designer);
        }}
      >
        <TextWidget>{i18n.chain.designable.useForm}</TextWidget>
      </Button>
    </Space>
  ) : null;

  return (
    <Space style={{ marginRight: 10 }}>
      {schemaSelectTools}
      <Radio.Group
        value={GlobalRegistry.getDesignerLanguage()}
        optionType="button"
        options={[
          { label: 'English', value: 'en-us' },
          { label: '简体中文', value: 'zh-cn' },
        ]}
        onChange={(e) => {
          GlobalRegistry.setDesignerLanguage(e.target.value);
        }}
      />
      <Button
        onClick={() => {
          saveSchema(designer);
        }}
      >
        <TextWidget>{i18n.chain.designable.download}</TextWidget>
      </Button>
      <Upload showUploadList={false} defaultFileList={[]} beforeUpload={(file: File) => uploadSchema(file, designer)}>
        <Button type="primary">{i18n.chain.designable.upload}</Button>
      </Upload>
    </Space>
  );
});
