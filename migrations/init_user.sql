-- 创建用户表
CREATE TABLE IF NOT EXISTS user (
                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    isAdmin BOOLEAN DEFAULT FALSE,
    isActive BOOLEAN DEFAULT TRUE,
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    );

-- 插入默认用户（密码：mdt@123）
INSERT OR IGNORE INTO user (username, email, password, isAdmin, isActive, createdAt, updatedAt)
VALUES (
    'test_mdt',
    '<EMAIL>',
    '$2a$10$XWFaoM.i9NY7PCxJUVFhDOqk239Z1K.40fiJBC9lXmpaD8Kdrx.f6',
    1,
    1,
    datetime('now'),
    datetime('now')
);
