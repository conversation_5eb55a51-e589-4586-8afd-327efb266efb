<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同助手</title>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        .container { max-width: 900px; margin: auto; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; }
        textarea { width: 98%; padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; min-height: 100px; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .section { margin-top: 20px; padding: 15px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9; }
        .section h2 { margin-top: 0; color: #555; font-size: 1.2em; }
        #logs { white-space: pre-wrap; background-color: #e9e9e9; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; }
        #preview { white-space: pre-wrap; background-color: #e9e9e9; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto; }
        .error { color: red; font-weight: bold; }
        .info { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>合同助手</h1>

        <div class="section">
            <h2>1. 输入您的合同需求：</h2>
            <textarea id="userInput" placeholder="例如：我需要一份数据保密协议，甲方是ABC科技公司，乙方是XYZ创新工作室..."></textarea>
            <button id="generateBtn">生成合同</button>
        </div>

        <div class="section">
            <h2>2. 处理日志与状态：</h2>
            <div id="logs">等待操作...</div>
        </div>

        <div class="section">
            <h2>3. 合同预览 (Markdown)：</h2>
            <div id="preview">尚未生成合同。</div>
            <button id="downloadBtn" style="margin-top: 10px;" disabled>下载Word文档 (.docx)</button>
        </div>
    </div>

    <script>
        const userInputEl = document.getElementById('userInput');
        const generateBtn = document.getElementById('generateBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const logsEl = document.getElementById('logs');
        const previewEl = document.getElementById('preview');

        let currentMarkdownContent = "";
        let currentContractName = "GeneratedContract";

        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            if (type === 'error') {
                logEntry.classList.add('error');
            } else if (type === 'info') {
                logEntry.classList.add('info');
            }
            logsEl.appendChild(logEntry);
            logsEl.scrollTop = logsEl.scrollHeight; // Scroll to bottom
        }

        generateBtn.addEventListener('click', async () => {
            const userInput = userInputEl.value.trim();
            if (!userInput) {
                addLog("用户输入不能为空！", "error");
                return;
            }

            addLog("开始生成合同...");
            generateBtn.disabled = true;
            downloadBtn.disabled = true;
            previewEl.textContent = "正在生成，请稍候...";

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_input: userInput })
                });

                const data = await response.json();

                if (response.ok) {
                    addLog(`合同已生成 (模板: ${data.selected_template_name})`, 'info');
                    currentMarkdownContent = data.contract_markdown;
                    currentContractName = data.selected_template_name || "GeneratedContract";
                    previewEl.textContent = currentMarkdownContent;
                    downloadBtn.disabled = false;

                    // 更新日志区域以显示后端传来的详细日志
                    if (data.logs && Array.isArray(data.logs)) {
                        data.logs.forEach(logEntry => {
                            // 根据日志级别（如果后端提供）或默认方式添加日志
                            // 假设日志条目是字符串，可以直接添加
                            // 如果日志条目是对象，需要调整格式
                            if (typeof logEntry === 'string') {
                                addLog(logEntry); // 默认使用 'info' 级别
                            } else if (typeof logEntry === 'object' && logEntry.message) {
                                addLog(logEntry.message, logEntry.level || 'info');
                            }
                        });
                    }
                } else {
                    addLog(`生成失败: ${data.error || '未知错误'} (模板: ${data.selected_template_name || 'N/A'})`, 'error');
                    previewEl.textContent = `错误: ${data.error || '生成失败'}`;
                }
            } catch (error) {
                addLog(`请求 /chat 失败: ${error}`, 'error');
                previewEl.textContent = `网络错误或服务器无响应: ${error}`;
            } finally {
                generateBtn.disabled = false;
            }
        });

        downloadBtn.addEventListener('click', async () => {
            if (!currentMarkdownContent) {
                addLog("没有可下载的Markdown内容。", "error");
                return;
            }

            addLog("准备下载Word文档...");
            downloadBtn.disabled = true;

            try {
                const response = await fetch('/download_contract', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        markdown_content: currentMarkdownContent,
                        contract_name: currentContractName.replace(/[^a-zA-Z0-9_\-]+/g, '_') // Sanitize name
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const filenameFromServer = response.headers.get('content-disposition');
                    let filename = `${currentContractName}.docx`;
                    if (filenameFromServer) {
                        const match = filenameFromServer.match(/filename="?([^"]+)"?/);
                        if (match && match[1]) {
                            filename = match[1];
                        }
                    }
                    
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    addLog("Word文档下载成功。", 'info');
                } else {
                    const errorData = await response.json();
                    addLog(`下载失败: ${errorData.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                addLog(`请求 /download_contract 失败: ${error}`, 'error');
            } finally {
                downloadBtn.disabled = false;
            }
        });
    </script>
</body>
</html> 