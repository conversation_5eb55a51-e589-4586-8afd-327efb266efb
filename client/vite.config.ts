import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';
import { createHtmlPlugin } from 'vite-plugin-html';
import svgr from 'vite-plugin-svgr';

export default defineConfig(({ mode }) => {
  // 根据环境设置不同的基础路径
  const base = mode === 'production' ? '/aistatics/contract-assistant/' : '/';

  return {
    base,
    plugins: [
      react(),
      svgr(),
      createHtmlPlugin({
        inject: {
          data: {
            injectScript: `<script src="./config.js?v=${new Date().getTime()}"></script>`,
          },
        },
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@vavt/v3-extension': path.resolve(__dirname, 'node_modules/@vavt/v3-extension'),
      },
    },
    publicDir: 'public',
    server: {
      port: 3000,
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Embedder-Policy': 'require-corp',
      },
    },
    worker: {
      format: 'es',
    },
  } as any;
});
