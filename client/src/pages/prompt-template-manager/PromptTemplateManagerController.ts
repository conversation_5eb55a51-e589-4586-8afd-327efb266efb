import { BehaviorSubject, skip } from 'rxjs';

import type { PromptTemplateDto, UpdatePromptTemplateBody } from '@/types/api';

// 导入相关控制器
import { PromptTemplateFormController } from './components/prompt-template-form';
import { PromptTemplateListController } from './components/prompt-template-list';

export enum TabTypeEnum {
  LIST = 'list',
  FORM = 'form',
}

export class PromptTemplateManagerController {
  // 页面状态
  public loading$ = new BehaviorSubject<boolean>(false);
  public selectedTemplate$ = new BehaviorSubject<PromptTemplateDto | null>(null);
  public activeTab$ = new BehaviorSubject<TabTypeEnum>(TabTypeEnum.LIST);

  // 子控制器
  public listController: PromptTemplateListController;
  public formController: PromptTemplateFormController | null = null;

  constructor() {
    // 创建列表控制器
    this.listController = new PromptTemplateListController({
      onEdit: this.handleCreateOrEdit,
      onCreateOrUpdateSuccess: this.backToList,
    });

    // 初始化监听器
    this.initListeners();
  }

  public destroy() {
    // 释放资源
    this.loading$.complete();
    this.selectedTemplate$.complete();
    this.activeTab$.complete();

    // 释放子控制器
    this.listController?.destroy();
    this.listController = null!;
    this.formController?.destroy();
    this.formController = null;
  }

  public backToList = () => {
    this.activeTab$.next(TabTypeEnum.LIST);
    this.selectedTemplate$.next(null);
  };

  // 处理表单提交
  private handleFormSubmit = async (formData: UpdatePromptTemplateBody) => {
    const selectedTemplate = this.selectedTemplate$.getValue();
    const editingTemplateId = selectedTemplate?.id;

    // 使用列表控制器处理创建或更新操作
    await this.listController.createOrUpdate(formData, editingTemplateId);
  };

  // 处理创建或编辑模板
  handleCreateOrEdit = (template?: PromptTemplateDto) => {
    // 设置选中模板
    this.selectedTemplate$.next(template || null);
    // 切换到表单视图
    this.activeTab$.next(TabTypeEnum.FORM);
  };

  private initListeners = () => {
    // 监听选中模板变化, 重建表单控制器
    this.selectedTemplate$.pipe(skip(1)).subscribe((template) => {
      this.formController?.destroy();
      this.formController = new PromptTemplateFormController({
        onSubmit: this.handleFormSubmit,
        onCancel: this.backToList,
        initialTemplate: template as UpdatePromptTemplateBody,
      });
    });
  };
}
