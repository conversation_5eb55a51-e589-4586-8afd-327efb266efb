import { type FC, memo } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { PromptTemplateFormComp } from './PromptTemplateFormComp';
import { PromptTemplateFormController } from './PromptTemplateFormController';

interface PromptTemplateFormProps {
  controller: PromptTemplateFormController;
}

// 默认开关组件 - 负责模板默认状态切换
const DefaultSwitch = memo<PromptTemplateFormProps>(({ controller }) => {
  const isDefault = useObservableState(controller.isDefault$);
  const loading = useObservableState(controller.loading$);

  return (
    <div className="mb-5 flex justify-end bg-white py-2">
      <div className="flex items-center space-x-2">
        <Switch
          id="isDefault"
          checked={isDefault}
          onCheckedChange={controller.updateIsDefault}
          disabled={loading}
        />
        <Label htmlFor="isDefault" className="cursor-pointer text-sm text-gray-600">
          设为默认模板
        </Label>
      </div>
    </div>
  );
});

// 表单底部操作按钮组件
const FormActions = memo<PromptTemplateFormProps>(({ controller }) => {
  const loading = useObservableState<boolean>(controller.loading$);
  const selectedTemplate = useObservableState(controller.selectedTemplate$);
  const isFormValid = useObservableState(controller.isValid$);

  return (
    <div className="flex justify-end gap-3 border-t border-slate-200 bg-white py-4 pt-8">
      <Button
        variant="outline"
        onClick={controller.handleCancel}
        disabled={loading}
        className="px-5"
      >
        取消
      </Button>
      <Button
        onClick={controller.handleSubmit}
        disabled={loading || !isFormValid}
        className="bg-blue-600 px-5 hover:bg-blue-700"
      >
        {selectedTemplate ? '更新模板' : '创建模板'}
      </Button>
    </div>
  );
});

// 主表单容器组件
const PromptTemplateForm: FC<PromptTemplateFormProps> = memo(({ controller }) => {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-0">
        <DefaultSwitch controller={controller} />
        <PromptTemplateFormComp controller={controller} />
        <FormActions controller={controller} />
      </CardContent>
    </Card>
  );
});

export { PromptTemplateForm };
