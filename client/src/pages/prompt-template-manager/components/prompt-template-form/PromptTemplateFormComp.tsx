import { ChangeEvent, type FocusEvent, memo, useMemo } from 'react';

import _ from 'lodash';

import { PromptInput } from '@/components/PromptInput';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useObservableState } from '@/shared/hooks/useObservableState';
import { parseStrToJson } from '@/shared/utils/stringUtil.ts';

import type { PromptTemplateFormController } from '../prompt-template-form/PromptTemplateFormController';

interface PromptTemplateSceneProps {
  controller: PromptTemplateFormController;
}

// 名称输入框
const NameInput = memo<{ controller: PromptTemplateFormController }>(({ controller }) => {
  const name = useObservableState(controller.name$);
  const loading = useObservableState<boolean>(controller.loading$);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    controller.updateName(e.target.value);
  };

  return (
    <div className="space-y-3">
      <Label htmlFor="name" className="text-sm font-medium">
        模板名称 <span className="text-red-500">*</span>
      </Label>
      <Input
        id="name"
        name="name"
        placeholder="输入模板名称"
        value={name}
        onChange={handleChange}
        disabled={loading}
        className="focus-visible:ring-offset-1"
      />
    </div>
  );
});

// 描述输入框
const DescriptionInput = memo<{ controller: PromptTemplateFormController }>(({ controller }) => {
  const description = useObservableState(controller.description$);
  const loading = useObservableState<boolean>(controller.loading$);

  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    controller.updateDescription(e.target.value);
  };

  return (
    <div className="space-y-3">
      <Label htmlFor="description" className="text-sm font-medium">
        模板描述
      </Label>
      <Textarea
        id="description"
        name="description"
        placeholder="输入模板描述"
        className="h-20 focus-visible:ring-offset-1"
        value={description}
        onChange={handleChange}
        disabled={loading}
      />
    </div>
  );
});

// 场景特定输入组件
const PromptInputs = memo<{ controller: PromptTemplateFormController }>(({ controller }) => {
  const content = useObservableState(controller.content$);
  const loading = useObservableState<boolean>(controller.loading$);

  const obj = useMemo(() => {
    return parseStrToJson(content);
  }, [content]);

  const handleContentChange = (e: FocusEvent<HTMLTextAreaElement>) => {
    const { value, id } = e.target;
    const p = (id || '').split('-');
    _.set(obj, p, value);
    controller.updateContent(JSON.stringify(obj));
  };

  const pi1 = obj.template_selection?.system_message || '';
  const pi2 = obj.template_selection?.user_prompt_template || '';
  const pi3 = obj.content_processing?.system_message || '';
  const pi4 = obj.content_processing?.user_prompt_template || '';
  const pi5 = obj.attachment_processing?.system_message || '';
  const pi6 = obj.attachment_processing?.user_prompt_template || '';

  return (
    <div className="space-y-6">
      <PromptInput
        id="template_selection-system_message"
        label="选择合同模版系统提示词"
        required={true}
        value={pi1}
        onBlur={handleContentChange}
        disabled={loading}
        placeholder="输入选择合同模版系统提示词"
      />
      <PromptInput
        id="template_selection-user_prompt_template"
        label="选择合同模版用户输入提示词"
        required={true}
        value={pi2}
        onBlur={handleContentChange}
        disabled={loading}
        placeholder="输入选择合同模版用户输入提示词"
      />
      <PromptInput
        id="content_processing-system_message"
        label="处理合同条款片段系统提示词"
        required={true}
        value={pi3}
        onBlur={handleContentChange}
        disabled={loading}
        placeholder="输入处理合同条款片段系统提示词"
      />
      <PromptInput
        id="content_processing-user_prompt_template"
        label="处理合同条款片段用户输入提示词"
        required={true}
        value={pi4}
        onBlur={handleContentChange}
        disabled={loading}
        placeholder="输入处理合同条款片段用户输入提示词"
      />
      <PromptInput
        id="attachment_processing-system_message"
        label="处理合同附件片段系统输入提示词"
        required={true}
        value={pi5}
        onBlur={handleContentChange}
        disabled={loading}
        placeholder="输入合同附件处理提示词"
      />
      <PromptInput
        id="attachment_processing-user_prompt_template"
        label="处理合同附件片段用户输入提示词"
        required={true}
        value={pi6}
        onBlur={handleContentChange}
        disabled={loading}
        placeholder="输入处理合同附件片段用户输入提示词"
      />
    </div>
  );
});

// 主场景组件
export const PromptTemplateFormComp = memo<PromptTemplateSceneProps>(({ controller }) => {
  return (
    <div className="space-y-6 bg-white">
      <NameInput controller={controller} />
      <DescriptionInput controller={controller} />
      <PromptInputs controller={controller} />
    </div>
  );
});
