import { BehaviorSubject, combineLatest } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { toast } from 'sonner';

import type { UpdatePromptTemplateBody } from '@/types/api';

interface ControllerOptions {
  onSubmit: (
    formData: UpdatePromptTemplateBody,
    originalTemplate: UpdatePromptTemplateBody | undefined,
  ) => Promise<void>;
  onCancel: () => void;
  initialTemplate?: UpdatePromptTemplateBody;
}

export class PromptTemplateFormController {
  name$: BehaviorSubject<string>;
  description$: BehaviorSubject<string>;
  content$: BehaviorSubject<string>;
  isDefault$: BehaviorSubject<boolean>;
  loading$: BehaviorSubject<boolean>;
  selectedTemplate$: BehaviorSubject<UpdatePromptTemplateBody | undefined>;

  // 派生状态 - 表单是否有效
  isValid$: BehaviorSubject<boolean>;

  // 回调函数
  private onSubmit: ControllerOptions['onSubmit'];
  private onCancel: ControllerOptions['onCancel'];

  constructor({ onSubmit, onCancel, initialTemplate }: ControllerOptions) {
    this.onSubmit = onSubmit;
    this.onCancel = onCancel;

    // 初始化基础状态变量
    this.loading$ = new BehaviorSubject<boolean>(false);
    this.selectedTemplate$ = new BehaviorSubject<UpdatePromptTemplateBody | undefined>(
      initialTemplate,
    );

    // 初始化表单状态变量，使用模板值或默认值
    const template = initialTemplate || {};
    // 模板名称
    this.name$ = new BehaviorSubject<string>(template.name || '');
    // 模板描述
    this.description$ = new BehaviorSubject<string>(template.description || '');
    // 模板内容
    this.content$ = new BehaviorSubject<string>(template.content || '');
    // 是否默认
    this.isDefault$ = new BehaviorSubject<boolean>(template.isDefault || false);

    // 创建派生状态
    this.isValid$ = new BehaviorSubject<boolean>(false);
    this.setupDerivedStates();
  }

  updateName = (value: string) => {
    this.name$.next(value);
  };

  updateDescription = (value: string) => {
    this.description$.next(value);
  };

  updateContent = (value: string) => {
    this.content$.next(value);
  };

  updateIsDefault = (value: boolean) => {
    this.isDefault$.next(value);
  };

  // 获取当前表单数据
  getFormData(): UpdatePromptTemplateBody {
    return {
      name: this.name$.getValue(),
      description: this.description$.getValue(),
      content: this.content$.getValue(),
      isDefault: this.isDefault$.getValue(),
    };
  }

  // 检查表单是否有变化
  hasFormChanged(): boolean {
    const formData = this.getFormData();
    const originalTemplate = this.selectedTemplate$.getValue();

    if (!originalTemplate) {
      // 新建表单，只要有必填字段内容就认为有变化
      return !!(formData.name && formData.content);
    }

    // 编辑表单，比较每个字段是否有变化
    return (
      formData.name !== originalTemplate.name ||
      formData.description !== originalTemplate.description ||
      formData.content !== originalTemplate.content ||
      formData.isDefault !== originalTemplate.isDefault
    );
  }

  // 提交表单
  handleSubmit = async () => {
    if (!this.isValid$.getValue()) {
      toast.error('请完善必填信息');
      return;
    }

    // 表单无变化时直接调用取消
    if (!this.hasFormChanged()) {
      toast.info('表单未发生变化');
      this.handleCancel();
      return;
    }

    // 开始加载
    this.loading$.next(true);

    // 调用父组件提供的提交方法
    const formData = this.getFormData();
    const originalTemplate = this.selectedTemplate$.getValue();

    await this.onSubmit(formData, originalTemplate);

    this.loading$.next(false);
  };

  // 取消编辑
  handleCancel = () => {
    this.onCancel();
  };

  // 销毁释放资源
  destroy() {
    // 取消派生状态订阅
    this.isValid$.complete();

    this.name$.complete();
    this.description$.complete();
    this.content$.complete();
    this.isDefault$.complete();
    this.selectedTemplate$.complete();
    // 复杂对象需要设置为null
    this.selectedTemplate$.next(null!);
    this.loading$.complete();

    this.onSubmit = null!;
    this.onCancel = null!;
  }

  // 设置派生状态
  private setupDerivedStates() {
    // 表单有效性派生状态
    combineLatest([this.name$, this.content$])
      .pipe(
        map(([name, content]) => !!(name && content)),
        distinctUntilChanged(),
      )
      .subscribe((isValid) => this.isValid$.next(isValid));
  }
}
