import { BehaviorSubject } from 'rxjs';
import { toast } from 'sonner';

import { promptTemplateApi } from '@/api/services/prompt-template';
import { ResultUtil } from '@/shared/utils/resultUtil';
import type {
  CreatePromptTemplateBody,
  PromptTemplateDto,
  UpdatePromptTemplateBody,
} from '@/types/api';
import { ResultResponse } from '@/types/common';

// 控制器选项接口
interface ControllerOptions {
  onEdit?: (template: PromptTemplateDto) => void; // 编辑回调
  onCreateOrUpdateSuccess?: () => void; // 创建或更新成功回调
}

/**
 * 提示词模板列表控制器
 * 负责提示词模板列表的加载、删除、默认状态切换等操作
 */
export class PromptTemplateListController {
  // 基础状态
  public loading$ = new BehaviorSubject<boolean>(false);
  public templates$ = new BehaviorSubject<PromptTemplateDto[]>([]);

  // 回调函数
  private onEdit?: (template: PromptTemplateDto) => void;
  private onCreateOrUpdateSuccess?: () => void;

  constructor(options?: ControllerOptions) {
    this.onEdit = options?.onEdit;
    this.onCreateOrUpdateSuccess = options?.onCreateOrUpdateSuccess;
    this.fetchTemplates();
  }

  /**
   * 加载模板列表
   */
  public fetchTemplates = async () => {
    this.loading$.next(true);
    const resp = await promptTemplateApi.getAllTemplates();
    !resp.success && toast.error('获取提示词模板失败:' + resp.message);
    const data = resp.success ? resp.data : [];
    this.templates$.next(data || []);
    this.loading$.next(false);
  };

  /**
   * 删除模板
   */
  public handleDelete = async (id: number) => {
    if (!confirm('确定要删除这个提示词模板吗？此操作不可恢复。')) {
      return;
    }

    this.loading$.next(true);
    const resp = await promptTemplateApi.deleteTemplate(id);
    if (!resp.success) {
      toast.error(resp.message);
      this.loading$.next(false);
      return;
    }

    toast.success('删除提示词模板成功');
    await this.fetchTemplates();
    this.loading$.next(false);
  };

  /**
   * 切换默认状态
   */
  public toggleDefaultStatus = async (template: PromptTemplateDto, isDefault: boolean) => {
    this.loading$.next(true);

    // 创建表单数据
    const formData = { ...template, isDefault };
    // 取消自身默认模板
    const changeDefaultTemplateResult = this.validateDefaultTemplate(
      formData,
      template.id,
      template,
    );
    if (!changeDefaultTemplateResult.success) {
      toast.error(changeDefaultTemplateResult.message);
      this.loading$.next(false);
      return;
    }

    // 处理默认模板冲突
    const defaultConflictResult = this.handleDefaultTemplateConflict(formData, template.id);
    if (!defaultConflictResult.success) {
      toast.error(defaultConflictResult.message);
      this.loading$.next(false);
      return;
    }

    const resp = await promptTemplateApi.setDefaultTemplate(template.id);
    if (!resp.success) {
      toast.error(resp.message);
      this.loading$.next(false);
      return;
    }

    toast.success(`${isDefault ? '设置' : '取消'}默认模板成功`);
    await this.fetchTemplates();
  };

  /**
   * 处理编辑操作
   */
  public handleEdit = (template: PromptTemplateDto) => {
    this.onEdit?.(template);
  };

  /**
   * 创建或更新模板
   */
  public createOrUpdate = async (
    formData: UpdatePromptTemplateBody,
    editingTemplateId?: number,
  ) => {
    this.loading$.next(true);

    // 验证模板唯一性
    const validateResult = this.validateTemplateUniqueness(formData, editingTemplateId);
    if (!validateResult.success) {
      toast.error(validateResult.message);
      this.loading$.next(false);
      return;
    }

    // 取消自身默认模板
    const changeDefaultTemplateResult = this.validateDefaultTemplate(formData, editingTemplateId);
    if (!changeDefaultTemplateResult.success) {
      toast.error(changeDefaultTemplateResult.message);
      this.loading$.next(false);
      return;
    }

    // 处理默认模板冲突
    const defaultConflictResult = this.handleDefaultTemplateConflict(formData, editingTemplateId);
    if (!defaultConflictResult.success) {
      toast.error(defaultConflictResult.message);
      this.loading$.next(false);
      return;
    }

    // 执行创建或更新操作
    const resp = editingTemplateId
      ? await promptTemplateApi.updateTemplate(editingTemplateId, formData)
      : await promptTemplateApi.createTemplate(formData as CreatePromptTemplateBody);

    if (!resp.success) {
      toast.error(resp.message);
      this.loading$.next(false);
      return;
    }

    toast.success(`${editingTemplateId ? '更新' : '创建'}提示词模板成功`);
    this.fetchTemplates();

    // 通知外部创建或更新成功
    this.onCreateOrUpdateSuccess?.();
  };

  /**
   * 验证模板唯一性
   */
  private validateTemplateUniqueness = (
    formData: UpdatePromptTemplateBody,
    editingTemplateId?: number,
  ): ResultResponse => {
    // 获取所有模板进行校验
    const templates = this.templates$.getValue();

    // 检查版本号唯一性
    const existingTemplate = templates.find(
      (t) =>
        t.name === formData.name && (editingTemplateId === undefined || t.id !== editingTemplateId),
    );

    return existingTemplate
      ? ResultUtil.fail(`已有相同名称[${existingTemplate.name}]的模板`, 'TEMPLATE_UNIQUE_ERROR')
      : ResultUtil.success(undefined);
  };

  /**
   * 处理默认模板冲突
   */
  private handleDefaultTemplateConflict = (
    formData: UpdatePromptTemplateBody,
    templateId?: number,
  ): ResultResponse => {
    // 获取所有模板进行校验
    const templates = this.templates$.getValue();

    // 检查是否需要更新其他默认模板
    const hasDefault =
      formData.isDefault &&
      templates.some((t) => t.isDefault && (templateId === undefined || t.id !== templateId));

    if (hasDefault) {
      const confirmChange = confirm('设置此模板为默认将取消其他模板的默认状态，是否继续？');

      if (!confirmChange) {
        return ResultUtil.fail('你取消了继续操作', 'DEFAULT_TEMPLATE_CONFLICT');
      }
    }

    return ResultUtil.success(undefined);
  };

  private validateDefaultTemplate = (
    template: UpdatePromptTemplateBody,
    editingTemplateId?: number,
    editingTemplate?: PromptTemplateDto,
  ): ResultResponse => {
    // 如果正在编辑的模板是默认模板,则不能取消默认模板
    if (editingTemplateId && !template.isDefault) {
      // 如果没有正在编辑的模板,则从列表中查找
      const previousTemplate =
        editingTemplate || this.templates$.getValue().find((t) => t.id === editingTemplateId);
      if (previousTemplate?.isDefault) {
        return ResultUtil.fail(
          '更新提示词模板失败: 不能取消默认模板,请先设置其他模板为默认',
          'CANNOT_CANCEL_DEFAULT_TEMPLATE',
        );
      }
    }
    return ResultUtil.success(undefined);
  };

  /**
   * 释放资源
   */
  public destroy(): void {
    this.templates$.complete();
    this.templates$.next(null!);
    this.loading$.complete();
    this.loading$.next(null!);
    this.onEdit = undefined;
    this.onCreateOrUpdateSuccess = undefined;
  }
}
