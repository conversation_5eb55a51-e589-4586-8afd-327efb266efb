import { memo } from 'react';

import { Edit, Refresh<PERSON>w, Trash } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useObservableState } from '@/shared/hooks/useObservableState';
import type { PromptTemplateDto } from '@/types/api';

import { PromptTemplateListController } from './PromptTemplateListController';

interface PromptTemplateListProps {
  controller: PromptTemplateListController;
}

/**
 * 提示词模板列表组件（容器组件）
 * 通过传入的控制器管理状态和操作
 */
export const PromptTemplateList = memo<PromptTemplateListProps>(({ controller }) => {
  // 获取控制器状态
  const templates = useObservableState<PromptTemplateDto[]>(controller.templates$);
  const loading = useObservableState<boolean>(controller.loading$);

  // 渲染部分
  if (loading && (!templates || templates.length === 0)) {
    return <LoadingState />;
  }

  if (!templates || templates.length === 0) {
    return <EmptyState />;
  }

  return (
    <div className="space-y-4">
      <ListHeader controller={controller} />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>名称</TableHead>
            <TableHead>描述</TableHead>
            <TableHead>默认</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {templates.map((template) => (
            <TableRow key={template.id}>
              <TableCell>{template.name}</TableCell>
              <TableCell>{template.description || '-'}</TableCell>
              <TableCell>
                <DefaultSwitch template={template} controller={controller} />
              </TableCell>
              <TableCell>
                {template.createdAt ? new Date(template.createdAt).toLocaleString() : '-'}
              </TableCell>
              <TableCell>
                <TemplateActions controller={controller} template={template} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
});

// 加载状态组件
const LoadingState = memo(() => (
  <div className="flex h-40 w-full items-center justify-center">
    <div className="text-md text-gray-500">加载中...</div>
  </div>
));

// 空状态组件
const EmptyState = memo(() => (
  <div className="flex h-40 w-full items-center justify-center">
    <div className="text-md text-gray-500">暂无数据</div>
  </div>
));

// 默认状态切换组件
const DefaultSwitch = memo<{
  template: PromptTemplateDto;
  controller: PromptTemplateListController;
}>(({ template, controller }) => {
  const loading = useObservableState<boolean>(controller.loading$);

  const handleToggle = async (checked: boolean) => {
    await controller.toggleDefaultStatus(template, checked);
  };

  return <Switch checked={template.isDefault} onCheckedChange={handleToggle} disabled={loading} />;
});

// 模板操作按钮
const TemplateActions = memo<{
  controller: PromptTemplateListController;
  template: PromptTemplateDto;
}>(({ controller, template }) => {
  return (
    <div className="flex space-x-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" onClick={() => controller.handleEdit(template)}>
              <Edit className="size-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>编辑</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" onClick={() => controller.handleDelete(template.id)}>
              <Trash className="size-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>删除</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
});

// 列表头部
const ListHeader = memo<{ controller: PromptTemplateListController }>(({ controller }) => {
  return (
    <div className="flex items-center justify-between">
      <h2 className="text-lg font-semibold">提示词模板列表</h2>
      <Button variant="outline" size="sm" onClick={() => controller.fetchTemplates()}>
        <RefreshCw className="mr-2 size-4" />
        刷新
      </Button>
    </div>
  );
});
