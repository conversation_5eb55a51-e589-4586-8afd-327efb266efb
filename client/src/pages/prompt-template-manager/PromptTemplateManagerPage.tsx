import { type FC, memo } from 'react';

import { useController } from '@/shared/hooks/useController';

import PromptTemplateManager from './PromptTemplateManager';
import { PromptTemplateManagerController } from './PromptTemplateManagerController';

const PromptTemplateManagerPage: FC = () => {
  const [controller] = useController(() => {
    const controller = new PromptTemplateManagerController();
    return [controller, {}];
  }, []);

  return <PromptTemplateManager controller={controller} />;
};

export default memo(PromptTemplateManagerPage);
