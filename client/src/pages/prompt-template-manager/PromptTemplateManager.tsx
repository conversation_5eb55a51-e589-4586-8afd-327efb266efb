import { type FC, memo } from 'react';

import { ArrowLeft } from 'lucide-react';

import { AppController } from '@/app/AppController';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { PromptTemplateManagerController, TabTypeEnum } from './PromptTemplateManagerController';
import { PromptTemplateForm } from './components/prompt-template-form';
import { PromptTemplateList } from './components/prompt-template-list/PromptTemplateList';

interface PromptTemplateManagerProps {
  controller: PromptTemplateManagerController;
}

// 页面头部 - 静态组件
const PageHeader = memo(() => {
  const handleGoBack = () => {
    // 返回首页
    AppController.getInstance().navigate('/');
  };

  return (
    <div className="mx-auto w-full max-w-6xl border-b border-slate-200 bg-white p-4">
      <div className="flex items-center justify-between">
        <div>
          <div className="mb-2 flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleGoBack}
              className="size-9 rounded-full hover:bg-slate-100"
            >
              <ArrowLeft className="size-5" />
            </Button>
            <h1 className="text-2xl font-bold">提示词模板管理</h1>
          </div>
          <p className="ml-12 text-muted-foreground">管理用于智能生成合同提示词模板,支持版本控制</p>
        </div>
      </div>
    </div>
  );
});

// 标签页 - 依赖selectedTemplate状态
const TemplateTabs = memo<{ controller: PromptTemplateManagerController }>(({ controller }) => {
  const selectedTemplate = useObservableState(controller.selectedTemplate$);
  const activeTab = useObservableState(controller.activeTab$);

  const handleTabChange = (value: string) => {
    if (value === TabTypeEnum.FORM && !selectedTemplate) {
      controller.handleCreateOrEdit();
    } else {
      controller.backToList();
    }
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="flex size-full flex-col">
      <TabsList className="mb-6 grid w-full max-w-md shrink-0 grid-cols-2">
        <TabsTrigger value="list" className="text-sm font-medium">
          模板列表
        </TabsTrigger>
        <TabsTrigger value="form" className="text-sm font-medium">
          {selectedTemplate ? '编辑模板' : '新建模板'}
        </TabsTrigger>
      </TabsList>
      <TemplateContent controller={controller} />
    </Tabs>
  );
});

// 内容区域 - 依赖activeTab状态
const TemplateContent = memo<{ controller: PromptTemplateManagerController }>(({ controller }) => {
  const activeTab = useObservableState(controller.activeTab$);
  const isList = activeTab === TabTypeEnum.LIST;
  const isForm = activeTab === TabTypeEnum.FORM;

  return (
    <div className="flex-1 overflow-auto px-1 pb-2">
      <TabsContent
        value={TabTypeEnum.LIST}
        className="mt-0 h-full data-[state=active]:flex data-[state=active]:h-full data-[state=active]:flex-1 data-[state=active]:flex-col"
        style={{ display: isList ? 'flex' : 'none' }}
      >
        {isList && <PromptTemplateList controller={controller.listController} />}
      </TabsContent>
      <TabsContent
        value={TabTypeEnum.FORM}
        className="mt-0 h-full data-[state=active]:flex data-[state=active]:h-full data-[state=active]:flex-1 data-[state=active]:flex-col"
        style={{ display: isForm ? 'flex' : 'none' }}
      >
        {isForm && <PromptTemplateForm controller={controller.formController!} />}
      </TabsContent>
    </div>
  );
});

// 主容器组件 - 仅组合其他组件
const PromptTemplateManager: FC<PromptTemplateManagerProps> = ({ controller }) => {
  return (
    <div className="flex h-full flex-col bg-white">
      <PageHeader />

      <div className="flex flex-1 flex-col overflow-hidden bg-white">
        <div className="mx-auto flex size-full max-w-6xl flex-col px-4 pb-0 pt-6">
          <TemplateTabs controller={controller} />
        </div>
      </div>
    </div>
  );
};

export default memo(PromptTemplateManager);
