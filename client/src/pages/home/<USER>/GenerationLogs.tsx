import { type FC, memo } from 'react';

import { Button } from '@/components/ui/button.tsx';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { type HomeController } from '../HomeController';

interface GenerationLogsProps {
  controller: HomeController;
}

export const GenerationLogs: FC<GenerationLogsProps> = memo(({ controller }) => {
  const logs = useObservableState(controller.generationLogs$);
  const viewLogs = controller.viewLogs$.getValue();

  const contentEle = logs.length ? (
    <div className="h-full max-h-[calc(100vh-200px)] overflow-y-auto space-y-2">
      {logs.map((log, index) => (
        <div
          key={index}
          className="text-sm text-gray-700 font-mono bg-gray-50 px-3 py-2 rounded border-l-4 border-blue-200"
        >
          <span className="text-gray-500 text-xs mr-2">[{String(index + 1).padStart(2, '0')}]</span>
          {log}
        </div>
      ))}
    </div>
  ) : (
    <div className="flex items-center justify-center h-64">
      <p className="text-gray-500">等待生成开始...</p>
    </div>
  );

  const viewContract = () => {
    controller.toggleViewLogs(false);
  };

  const viewBtn = viewLogs ? (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={viewContract}>
        查看合同
      </Button>
    </div>
  ) : null;

  return (
    <Card className="size-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>生成日志</CardTitle>
        {viewBtn}
      </CardHeader>
      <CardContent>{contentEle}</CardContent>
    </Card>
  );
});

GenerationLogs.displayName = 'GenerationLogs';
