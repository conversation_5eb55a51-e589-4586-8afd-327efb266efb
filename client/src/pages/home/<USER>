import { type FC, memo } from 'react';

import { Button } from '@/components/ui/button';
import { useController } from '@/shared/hooks/useController';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { GenerationStatus, HomeController } from './HomeController';
import { ContractInputForm } from './components/ContractInputForm';
import { ContractPreview } from './components/ContractPreview';
import { GenerationLogs } from './components/GenerationLogs';
import { InitialInputForm } from './components/InitialInputForm';

// 初始状态组件 - 居中显示输入框
const InitialView: FC<{ controller: HomeController }> = memo(({ controller }) => {
  return (
    <div className="flex flex-col flex-1 items-center justify-center pt-[6%]">
      <div className="text-center space-y-6 mb-12">
        <h1 className="text-5xl font-bold text-gray-900">智能合同生成助手</h1>
        <p className="text-xl text-gray-600">基于AI技术，快速生成专业合同文档</p>
      </div>

      <InitialInputForm controller={controller} />
    </div>
  );
});

// 分栏布局组件
const SplitLayoutView: FC<{ controller: HomeController }> = memo(({ controller }) => {
  const contractResult = useObservableState(controller.contractResult$);
  const generationStatus = useObservableState(controller.generationStatus$);

  // 当有结果且生成成功时，显示markdown预览；否则显示日志
  const shouldShowPreview =
    contractResult !== null && generationStatus === GenerationStatus.SUCCESS;

  const handleBackToInitial = () => {
    controller.resetToInitialState();
  };

  return (
    <div className="h-screen flex flex-col">
      {/* 顶部标题栏 */}
      <div className="shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToInitial}
              className="text-gray-600 hover:text-gray-900"
            >
              ← 返回
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">智能合同生成助手</h1>
              <p className="text-sm text-gray-600">基于AI技术，快速生成专业合同文档</p>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：输入表单 - 40% */}
        <div className="w-2/5 border-r border-gray-200 p-6 overflow-y-auto">
          <ContractInputForm controller={controller} />
        </div>

        {/* 右侧：预览区域或日志 - 60% */}
        <div className="w-3/5 p-6 overflow-y-auto">
          {shouldShowPreview ? (
            <ContractPreview controller={controller} />
          ) : (
            <GenerationLogs controller={controller} />
          )}
        </div>
      </div>
    </div>
  );
});

// 页面内容
const PageMain: FC<{ controller: HomeController }> = memo(({ controller }) => {
  const showSplitLayout = useObservableState(controller.showSplitLayout$);

  return (
    <div className="size-full relative overflow-hidden">
      {/* 初始视图 */}
      <div
        className={`absolute inset-0 transition-all duration-500 ease-in-out ${
          showSplitLayout
            ? 'opacity-0 -translate-x-full pointer-events-none'
            : 'opacity-100 translate-x-0'
        }`}
      >
        <InitialView controller={controller} />
      </div>

      {/* 分栏视图 */}
      <div
        className={`absolute inset-0 transition-all duration-500 ease-in-out ${
          showSplitLayout
            ? 'opacity-100 translate-x-0'
            : 'opacity-0 translate-x-full pointer-events-none'
        }`}
      >
        <SplitLayoutView controller={controller} />
      </div>
    </div>
  );
});

// 主页面组件
const HomePage: FC = () => {
  const [controller] = useController(() => {
    const controller = new HomeController();
    return [controller, {}];
  }, []);

  return (
    <div className="h-full bg-gray-50">
      <PageMain controller={controller} />
    </div>
  );
};

export default memo(HomePage);
