import { BehaviorSubject } from 'rxjs';

import { contractApi } from '@/api/services/contract';
import { EXAMPLE_INPUT } from '@/shared/config.ts';

// 生成状态枚举
export enum GenerationStatus {
  IDLE = 'idle',
  GENERATING = 'generating',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  selected_template_name: string;
}

export class HomeController {
  // 基础状态
  public userInput$ = new BehaviorSubject<string>('');
  public generationStatus$ = new BehaviorSubject<GenerationStatus>(GenerationStatus.IDLE);
  public generationLogs$ = new BehaviorSubject<string[]>([]);
  public contractResult$ = new BehaviorSubject<ContractGenerationResult | null>(null);
  public contractText$ = new BehaviorSubject<string>('');

  // 布局状态：控制是否显示左右分栏布局
  public showSplitLayout$ = new BehaviorSubject<boolean>(false);
  // 查看日志
  public viewLogs$ = new BehaviorSubject<boolean>(false);

  public constructor() {}

  // 更新用户输入
  public updateUserInput(input: string): void {
    this.userInput$.next(input);
  }

  // 生成合同
  public async generateContract(): Promise<void> {
    let userInput = this.userInput$.getValue();
    if (!userInput.trim()) {
      userInput = EXAMPLE_INPUT;
      this.userInput$.next(userInput);
    }

    // 切换到分栏布局
    this.showSplitLayout$.next(true);

    this.generationStatus$.next(GenerationStatus.GENERATING);
    this.generationLogs$.next([]);
    this.contractResult$.next(null);
    this.contractText$.next('');

    const response = await contractApi.generateContractPreview(userInput, (log: string) => {
      // 处理实时日志
      const currentLogs = this.generationLogs$.getValue();
      this.generationLogs$.next([...currentLogs, log]);
    });

    if (response.success) {
      // 先添加完成日志，再设置结果（这样可以确保日志显示完整）
      const currentLogs = this.generationLogs$.getValue();
      this.generationLogs$.next([...currentLogs, '合同生成完成']);

      // 设置结果和状态（这会触发UI切换到markdown预览）
      this.contractResult$.next(response.data);
      this.contractText$.next(response.data.contract_markdown);
      this.generationStatus$.next(GenerationStatus.SUCCESS);
    } else {
      this.generationStatus$.next(GenerationStatus.ERROR);
      const currentLogs = this.generationLogs$.getValue();
      this.generationLogs$.next([...currentLogs, '合同生成失败']);
    }
  }

  // 下载合同
  public async downloadContract(): Promise<void> {
    // TODO
  }

  // 和模版进行比对
  public complateWithTemplate() {}

  public toggleViewLogs(visible: boolean) {
    this.viewLogs$.next(visible);
  }

  // 清空结果
  public clearResults(): void {
    this.contractResult$.next(null);
    this.viewLogs$.next(false);
    this.contractText$.next('');
    this.generationLogs$.next([]);
    this.generationStatus$.next(GenerationStatus.IDLE);
  }

  // 重置到初始状态
  public resetToInitialState(): void {
    this.showSplitLayout$.next(false);
    this.clearResults();
    this.updateUserInput('');
  }

  // 销毁
  public destroy() {
    this.userInput$.complete();
    this.generationStatus$.complete();
    this.generationLogs$.complete();
    this.generationLogs$.next([]);
    this.contractResult$.complete();
    this.contractResult$.next(null);
    this.contractText$.complete();
    this.showSplitLayout$.complete();
  }
}
