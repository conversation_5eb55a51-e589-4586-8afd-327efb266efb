import { type FC, memo } from 'react';

import { MarkdownEditor } from '@/components/MarkdownEditor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { GenerationStatus, type HomeController } from '../HomeController';

interface ContractPreviewProps {
  controller: HomeController;
}

const PreviewView = (props: ContractPreviewProps) => {
  return <MarkdownEditor noKatex noMermaid />;
};

export const ContractPreview: FC<ContractPreviewProps> = memo(({ controller }) => {
  const handleDownload = () => {
    controller.downloadContract();
  };

  return (
    <Card className="size-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>合同预览</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleDownload}>
            下载 Markdown
          </Button>
        </div>
      </CardHeader>
      <CardContent className="h-full">
        <div className="h-full max-h-[calc(100vh-200px)] overflow-y-auto border rounded-md p-4 bg-white">
          <PreviewView controller={controller} />
        </div>
      </CardContent>
    </Card>
  );
});

ContractPreview.displayName = 'ContractPreview';
