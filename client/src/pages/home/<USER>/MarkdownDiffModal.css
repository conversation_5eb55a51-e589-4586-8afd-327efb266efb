/* 自定义diff样式 */
.diff-container {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.diff-container .diff-gutter {
  width: 60px;
  text-align: center;
  background-color: #f8f9fa;
  border-right: 1px solid #e1e4e8;
  color: #586069;
  user-select: none;
}

.diff-container .diff-line {
  padding: 0 8px;
  white-space: pre-wrap;
  word-break: break-word;
}

.diff-container .diff-line-add {
  background-color: #e6ffed;
  border-left: 3px solid #28a745;
}

.diff-container .diff-line-delete {
  background-color: #ffeef0;
  border-left: 3px solid #d73a49;
}

.diff-container .diff-line-normal {
  background-color: #ffffff;
}

.diff-container .diff-line-add .diff-code {
  background-color: #acf2bd;
}

.diff-container .diff-line-delete .diff-code {
  background-color: #fdb8c0;
}

/* 分割视图样式 */
.diff-container .diff-split {
  display: flex;
}

.diff-container .diff-split-old,
.diff-container .diff-split-new {
  flex: 1;
  border-right: 1px solid #e1e4e8;
}

.diff-container .diff-split-new {
  border-right: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .diff-container {
    font-size: 12px;
  }
  
  .diff-container .diff-gutter {
    width: 40px;
  }
}
