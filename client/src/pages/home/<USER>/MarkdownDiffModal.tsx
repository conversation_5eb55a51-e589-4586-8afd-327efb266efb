import { FC, useMemo } from 'react';
import { Diff, Hunk, parseDiff } from 'react-diff-view';
import 'react-diff-view/style/index.css';

import { diffLines } from 'diff';
import { X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import './MarkdownDiffModal.css';

interface MarkdownDiffModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentMarkdown: string;
  templateMarkdown: string;
  templateName?: string;
}

export const MarkdownDiffModal: FC<MarkdownDiffModalProps> = ({
  isOpen,
  onClose,
  currentMarkdown,
  templateMarkdown,
  templateName = '模板合同',
}) => {
  const diffData = useMemo(() => {
    if (!currentMarkdown || !templateMarkdown) {
      return null;
    }

    // 使用 diffLines 生成差异
    const changes = diffLines(templateMarkdown, currentMarkdown);

    // 构建 unified diff 格式
    let diffText = `--- a/${templateName || '模板合同'}\n+++ b/当前合同\n`;

    // 计算行号范围
    const oldStart = 1;
    const newStart = 1;
    let oldLines = 0;
    let newLines = 0;

    // 先计算总行数
    changes.forEach((change) => {
      const lines = change.value
        .split('\n')
        .filter((line) => line !== '' || change.value.endsWith('\n'));
      if (change.value.endsWith('\n') && lines[lines.length - 1] === '') {
        lines.pop();
      }

      if (change.removed) {
        oldLines += lines.length;
      } else if (change.added) {
        newLines += lines.length;
      } else {
        oldLines += lines.length;
        newLines += lines.length;
      }
    });

    // 添加 hunk 头部
    diffText += `@@ -${oldStart},${oldLines} +${newStart},${newLines} @@\n`;

    // 添加变更内容
    changes.forEach((change) => {
      const lines = change.value.split('\n');
      if (change.value.endsWith('\n') && lines[lines.length - 1] === '') {
        lines.pop();
      }

      lines.forEach((line) => {
        if (change.removed) {
          diffText += `-${line}\n`;
        } else if (change.added) {
          diffText += `+${line}\n`;
        } else {
          diffText += ` ${line}\n`;
        }
      });
    });

    try {
      const [file] = parseDiff(diffText);
      return file;
    } catch (error) {
      console.error('解析diff失败:', error);
      return null;
    }
  }, [currentMarkdown, templateMarkdown, templateName]);

  const renderFile = () => {
    if (!diffData) {
      return (
        <div className="flex items-center justify-center h-64 text-gray-500">无法生成比对数据</div>
      );
    }

    return (
      <div className="diff-container">
        <Diff
          viewType="split"
          diffType={diffData.type}
          hunks={diffData.hunks}
          renderGutter={({ renderDefault }) => {
            return renderDefault();
          }}
        >
          {(hunks) => hunks.map((hunk) => <Hunk key={hunk.content} hunk={hunk} />)}
        </Diff>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] size-full">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">
            合同比对 - {templateName} vs 当前合同
          </DialogTitle>
          <Button variant="ghost" size="sm" onClick={onClose} className="size-8 p-0">
            <X className="size-4" />
          </Button>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-auto border rounded-lg bg-white">{renderFile()}</div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
