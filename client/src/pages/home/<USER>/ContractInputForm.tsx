import { type FC, memo } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { EXAMPLE_INPUT } from '@/shared/config.ts';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { GenerationStatus, type HomeController } from '../HomeController';

interface ContractInputFormProps {
  controller: HomeController;
}

export const ContractInputForm: FC<ContractInputFormProps> = memo(({ controller }) => {
  const userInput = useObservableState(controller.userInput$);
  const generationStatus = useObservableState(controller.generationStatus$);
  const isGenerating = generationStatus === GenerationStatus.GENERATING;

  const handleInputChange = (value: string) => {
    controller.updateUserInput(value);
  };

  const handleGenerate = () => {
    controller.generateContract();
  };

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle>合同需求描述</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Textarea
            placeholder={`请描述您的合同需求，例如：${EXAMPLE_INPUT}`}
            value={userInput}
            onChange={(e) => handleInputChange(e.target.value)}
            rows={6}
            className="resize-none focus:ring-2 focus:ring-blue-500"
            disabled={isGenerating}
          />
        </div>

        <div className="flex justify-center">
          <Button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="w-full max-w-xs bg-blue-600 hover:bg-blue-700 text-base py-2"
          >
            {isGenerating ? '合同生成中...' : '重新生成'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});

ContractInputForm.displayName = 'ContractInputForm';
