import { type FC, memo } from 'react';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { GenerationStatus, type HomeController } from '../HomeController';

interface InitialInputFormProps {
  controller: HomeController;
}

export const InitialInputForm: FC<InitialInputFormProps> = memo(({ controller }) => {
  const userInput = useObservableState(controller.userInput$, '');
  const generationStatus = useObservableState(controller.generationStatus$, GenerationStatus.IDLE);
  const errorMessage = useObservableState(controller.errorMessage$, '');

  const isGenerating = generationStatus === GenerationStatus.GENERATING;

  const handleInputChange = (value: string) => {
    controller.updateUserInput(value);
  };

  const handleGenerate = () => {
    controller.generateContract();
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      <div className="space-y-2">
        <Textarea
          placeholder="请描述您的合同需求，例如：我需要一份技术服务合同，甲方是ABC公司，乙方是XYZ科技有限公司，服务内容包括软件开发和技术支持..."
          value={userInput}
          onChange={(e) => handleInputChange(e.target.value)}
          rows={8}
          className="resize-none focus:ring-2 focus:ring-blue-500 text-base p-4 border-2 border-gray-200 rounded-lg shadow-sm"
          disabled={isGenerating}
        />
        {errorMessage && <p className="text-sm text-red-600 text-center">{errorMessage}</p>}
      </div>

      <div className="flex justify-center">
        <Button
          onClick={handleGenerate}
          disabled={isGenerating}
          className="px-12 py-4 text-lg bg-blue-600 hover:bg-blue-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
        >
          {isGenerating ? '生成中...' : '生成合同'}
        </Button>
      </div>
    </div>
  );
});

InitialInputForm.displayName = 'InitialInputForm';
