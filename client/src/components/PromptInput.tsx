import { type FC, type FocusEvent } from 'react';

import { Braces } from 'lucide-react';

import { Label } from './ui/label';
import { Textarea } from './ui/textarea';

interface PromptInputProps {
  id: string;
  label: string;
  value: string;
  onBlur: (e: FocusEvent<HTMLTextAreaElement>) => void;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const PromptInput: FC<PromptInputProps> = ({
  id,
  label,
  value,
  onBlur,
  required = false,
  disabled = false,
  placeholder = '输入提示词内容',
}) => {
  // 解析标签中的星号并添加红色样式
  const renderLabel = () => {
    if (required) {
      return (
        <>
          {label}
          <span className="text-red-500">*</span>
        </>
      );
    }
    return label;
  };

  return (
    <div className="mb-6 space-y-3">
      <Label htmlFor={id} className="flex items-center text-sm font-medium">
        <Braces className="mr-2 size-4 text-slate-400" />
        {renderLabel()}
      </Label>
      <Textarea
        id={id}
        defaultValue={value}
        onBlur={onBlur}
        placeholder={placeholder}
        className="h-64 border-slate-200 bg-slate-50 font-mono text-sm focus-visible:ring-offset-1"
        disabled={disabled}
      />
    </div>
  );
};
