import { parseStrToJson } from './stringUtil.ts';

/**
 * 获取token
 * @returns
 */
const TOKEN_KEY = 'token';
export const getTokenFromLocalStorage = (): string => {
  return localStorage.getItem(TOKEN_KEY) || '';
};

/**
 * 保存token
 * @param token
 */
export const saveTokenToLocalStorage = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 删除token
 */
export const removeTokenFromLocalStorage = () => {
  localStorage.removeItem(TOKEN_KEY);
};
