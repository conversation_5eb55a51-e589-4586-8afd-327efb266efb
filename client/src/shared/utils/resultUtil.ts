import type { ErrorResponse, ResultResponse, SuccessResponse } from '@/types/common';

/**
 * 结果处理工具类
 * 用于统一管理操作结果，避免过度使用异常处理
 */
export class ResultUtil {
  /**
   * 创建成功结果
   * @param data 返回数据
   * @param message 成功消息
   */
  static success<T>(data: T, message: string = '操作成功'): SuccessResponse<T> {
    return {
      success: true,
      message,
      data,
    };
  }

  /**
   * 创建失败结果
   * @param errorCode 错误代码
   * @param message 提示消息
   */
  static fail(message: string, errorCode: string = 'UNKNOWN_ERROR'): ErrorResponse {
    return {
      success: false,
      message,
      errorCode,
    };
  }

  /**
   * 判断是否为Result类型
   * @param obj 待检查对象
   */
  static isResult(obj: any): obj is ResultResponse {
    return obj && typeof obj === 'object' && 'success' in obj;
  }

  /**
   * 安全执行函数并返回Result
   * @param fn 要执行的异步函数
   * @param errorMessage 错误信息前缀
   */
  static async execute<T>(
    fn: () => Promise<T | undefined | SuccessResponse<T> | ErrorResponse>,
    errorMessage: string = '操作执行失败',
  ): Promise<SuccessResponse<T> | ErrorResponse> {
    try {
      const response = await fn();

      if (this.isResult(response)) {
        return response as SuccessResponse<T>;
      }

      return this.success(response as T);
    } catch (error: any) {
      const result = this.fail(
        `${errorMessage}: ${error.message}`,
        error.errorCode || 'EXECUTION_ERROR',
      );
      return result as ErrorResponse;
    }
  }
}
