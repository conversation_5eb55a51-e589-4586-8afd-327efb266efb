/**
 * 将字符串转换为JSON对象
 * @param str 字符串
 * @param defaultValue 默认值
 * @returns JSON对象
 */
export const parseStrToJson = (str?: string, defaultValue = {}) => {
  try {
    return str ? JSON.parse(str) : defaultValue;
  } catch (error) {
    console.error('parseStrToJson error', error);
    return defaultValue;
  }
};

/**
 * 将时间字符串(HH:MM:SS.mmm)转换为秒数
 * @param timeString 时间字符串，格式为 HH:MM:SS.mmm
 * @returns 秒数
 */
export function timeStringToSeconds(timeString: string): number {
  if (!timeString) return 0;

  const parts = timeString.split(':');

  // 处理 "HH:MM:SS.mmm" 格式
  if (parts.length === 3) {
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parseFloat(parts[2]);

    return hours * 3600 + minutes * 60 + seconds;
  }

  // 处理 "MM:SS.mmm" 格式
  if (parts.length === 2) {
    const minutes = parseInt(parts[0], 10);
    const seconds = parseFloat(parts[1]);
    return minutes * 60 + seconds;
  }

  // 处理 "SS.mmm" 格式
  if (parts.length === 1) {
    const seconds = parseFloat(parts[0]);
    return isNaN(seconds) ? 0 : seconds;
  }

  return 0;
}

/**
 * 将毫秒数转换为时间字符串(HH:MM:SS.mmm)
 * @param milliseconds 毫秒数
 * @returns 时间字符串
 */
export function millisecondsToTimeString(milliseconds: number): string {
  if (typeof milliseconds !== 'number' || isNaN(milliseconds)) {
    return '00:00:00.000';
  }

  const hours = Math.floor(milliseconds / 3600000);
  const minutes = Math.floor((milliseconds % 3600000) / 60000);
  const secs = Math.floor((milliseconds % 60000) / 1000);
  const fms = Math.floor((milliseconds % 1000) / 10);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${fms.toString().padStart(3, '0')}`;
}

/**
 * 格式化文件大小，转换为人类可读的格式
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
