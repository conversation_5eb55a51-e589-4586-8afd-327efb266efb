import type { ResultResponse } from '@/types/common';

import api from '../api';

const API_PATH = '/contract';

export const contractApi = {
  /**
   * 音频转写服务
   * @param audioBlob 音频文件
   * @param model 可选的模型名称
   * @returns 转写结果
   */
  async generateContract(
    model?: string,
  ): Promise<ResultResponse<AudioTranscriptDataDto>> {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'audio.mp3');

    return api.post(`${API_PATH}/audio_transcribe`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      params: { model },
    });
  },

  /**
   * 根据音频片段, 应用反诈模式分析重点时刻, 重点信息摘录, 人员标签分析, 风险分析
   * @param segments 音频片段
   * @param sessionId 会话ID
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async antiFraudAnalysisAudioBySegments(
    segments: AudioSegmentDto[],
    sessionId: string,
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<AudioTranscriptDataDto>> {
    return api.post(`${API_PATH}/anti_fraud/analysis_audio_by_segments`, {
      segments,
      sessionId,
      model,
      templateCode,
    });
  },

  /**
   * 根据音频片段, 应用社区矫正模式分析重点时刻
   * @param segments 音频片段
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionAnalysisAudioBySegments(
    segments: AudioSegmentDto[],
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<CommunityCorrectionKeyMomentItemDto[]>> {
    return api.post(`${API_PATH}/community_correction/analysis_audio_by_segments`, {
      segments,
      model,
      templateCode,
    });
  },

  /**
   * 根据图片, 应用社区矫正模式分析重点时刻
   * @param body 请求体
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionAnalysisKeyMomentImage(
    body: CommunityCorrectionKeyMomentImageBody,
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<string>> {
    return api.post(`${API_PATH}/community_correction/analysis_key_moment_image`, {
      ...body,
      model,
      templateCode,
    });
  },

  /**
   * 根据图片, 应用社区矫正模式分析环境
   * @param body 请求体
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionEnvironmentAnalysis(
    body: CommunityCorrectionEnvironmentAnalysisBody,
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<string>> {
    return api.post(`${API_PATH}/community_correction/environment_analysis`, {
      ...body,
      model,
      templateCode,
    });
  },

  /**
   * 根据音频片段, 应用社区矫正模式分析访谈内容
   * @param segments 音频片段
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionAnalyzeInterviewContent(
    segments: AudioSegmentDto[],
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<string>> {
    return api.post(`${API_PATH}/community_correction/analyze_interview_content`, {
      segments,
      model,
      templateCode,
    });
  },

  /**
   * 根据音频片段, 应用社区矫正模式分析重点信息摘录
   * @param segments 音频片段
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionAnalyzeImportantInfo(
    segments: AudioSegmentDto[],
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<CommunityCorrectionAnalyzeImportantInfoResultDto>> {
    return api.post(`${API_PATH}/community_correction/analyze_important_info`, {
      segments,
      model,
      templateCode,
    });
  },

  /**
   * 根据重点时刻, 访谈内容, 环境, 应用社区矫正模式分析人员标签
   * @param body 请求体
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionAnalyzePersonnelTag(
    body: CommunityCorrectionPersonnelTagAnalysisBody,
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<CommunityCorrectionPersonnelTagAnalysisResultDto>> {
    return api.post(`${API_PATH}/community_correction/analyze_personnel_tag`, {
      ...body,
      model,
      templateCode,
    });
  },

  /**
   * 根据访谈内容, 环境, 重点信息摘录, 人员标签, 应用社区矫正模式分析管控工作建议
   * @param body 请求体
   * @param model 模型名称
   * @param templateCode 提示词代码
   * @returns ApiResponse
   */
  async communityCorrectionAnalyzeControlWorkSuggestion(
    body: CommunityCorrectionAnalyzeControlWorkSuggestionBody,
    model?: string,
    templateCode?: string,
  ): Promise<ResultResponse<string>> {
    return api.post(`${API_PATH}/community_correction/analyze_control_work_suggestion`, {
      ...body,
      model,
      templateCode,
    });
  },
};
