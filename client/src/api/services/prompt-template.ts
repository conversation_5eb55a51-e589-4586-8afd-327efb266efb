import { CreatePromptTemplateBody, PromptTemplateDto, UpdatePromptTemplateBody } from '@/types/api';
import type { ResultResponse } from '@/types/common';

import api from '../api';

const API_PATH = '/prompt-template';

/**
 * 提示词模板API
 */
export const promptTemplateApi = {
  /**
   * 获取所有提示词模板
   */
  getAllTemplates: (): Promise<ResultResponse<PromptTemplateDto[]>> => {
    return api.get(API_PATH);
  },

  /**
   * 创建提示词模板
   * @param data 提示词模板数据
   */
  createTemplate: (data: CreatePromptTemplateBody): Promise<ResultResponse<PromptTemplateDto>> => {
    return api.post(API_PATH, data);
  },

  /**
   * 更新提示词模板
   * @param id 模板ID
   * @param data 提示词模板数据
   */
  updateTemplate: (
    id: number,
    data: UpdatePromptTemplateBody,
  ): Promise<ResultResponse<PromptTemplateDto>> => {
    return api.patch(`${API_PATH}/${id}`, data);
  },

  /**
   * 删除提示词模板
   * @param id 模板ID
   */
  deleteTemplate: (id: number): Promise<ResultResponse> => {
    return api.delete(`${API_PATH}/${id}`);
  },

  /**
   * 设置默认提示词模板
   * @param id 模板ID
   */
  setDefaultTemplate: (id: number): Promise<ResultResponse<PromptTemplateDto>> => {
    return api.post(`${API_PATH}/${id}/set_default`);
  },
};
