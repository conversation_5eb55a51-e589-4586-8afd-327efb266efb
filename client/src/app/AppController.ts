import type { NavigateFunction } from 'react-router-dom';

import { BehaviorSubject } from 'rxjs';

import { setDealUnauthorized } from '@/api/api';
import { authApi } from '@/api/services/auth';
import {
  getTokenFromLocalStorage,
  removeTokenFromLocalStorage,
} from '@/shared/utils/localStorageUtil';
import { UserProfileDto } from '@/types/api';

export class AppController {
  private static instance: AppController | null = null;

  public navigate: NavigateFunction = null!;
  public user$ = new BehaviorSubject<UserProfileDto | null>(null);
  public isAuthenticated$ = new BehaviorSubject<boolean>(false);
  public isLoading$ = new BehaviorSubject<boolean>(true);
  public readonly onlyHomePage = false;

  private constructor() {
    setDealUnauthorized(this.dealUnauthorized);
    // 初始化时检查认证状态
    this.checkAuth();
  }

  public static getInstance(): AppController {
    if (!AppController.instance) {
      AppController.instance = new AppController();
    }
    return AppController.instance;
  }

  public destroy() {
    setDealUnauthorized();
    this.navigate = null!;
    this.isLoading$.complete();
    this.isLoading$.next(false);
    this.isAuthenticated$.complete();
    this.isAuthenticated$.next(false);
    this.user$.complete();
    this.user$.next(null);
  }

  public login(user: UserProfileDto) {
    // 先更新状态，不立即检查认证
    this.user$.next(user);
    this.isAuthenticated$.next(true);
  }

  public async logout() {
    // api发出请求后立即执行,不用等待后端结果
    // authApi.logout();
    removeTokenFromLocalStorage();
    this.user$.next(null);
    this.isAuthenticated$.next(false);
    this.gotoLogin();
  }

  private dealUnauthorized = () => {
    // 清除本地token
    removeTokenFromLocalStorage();
    // 调整到登录页面
    this.gotoLogin();
  };

  private gotoLogin = () => {
    // 使用 navigate 进行导航
    this.navigate?.(`/login`);
  };

  private async checkAuth() {
    // 暂不需要登录
    if (this.onlyHomePage) {
      this.isAuthenticated$.next(true);
      this.isLoading$.next(false);
      return;
    }

    const token = getTokenFromLocalStorage();

    if (!token) {
      this.isLoading$.next(false);
      return;
    }

    const result = await authApi.getUserInfo();

    if (!result.success) {
      this.isLoading$.next(false);
      return;
    }

    // 更新状态
    this.user$.next(result.data!);
    this.isAuthenticated$.next(true);
    this.isLoading$.next(false);
  }
}
