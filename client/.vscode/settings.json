{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": true, "eslint.alwaysShowStatus": true, "editor.rulers": [100], "workbench.colorCustomizations": {"editorRuler.foreground": "#3c404a"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "prettier.configPath": ".prettier<PERSON>", "editor.wordWrapColumn": 100, "files.eol": "\n"}