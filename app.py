import os
import json
import uuid
import subprocess
import logging
import requests
import re
from flask import Flask, request, jsonify, send_file, send_from_directory

app = Flask(__name__)

# --- 配置日志 ---
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 全局变量存储加载的数据 ---
CONTRACT_TEMPLATES = []
PROMPTS = {}

GENERATED_CONTRACTS_DIR = 'generated_contracts'
REFERS_DIR = 'refers' # 假设 refers 文件夹与 app.py 同级

# --- LLM API 配置 ---
LLM_API_URL = "http://192.168.10.119:8080/v1/chat/completions"
LLM_API_HEADERS = {
    "x-model": "llm-reasoning-default",
    "Content-Type": "application/json"
}
LLM_REQUEST_TIMEOUT = 120 # LLM请求超时时间（秒）

# --- 数据加载 ---
def load_data():
    """应用启动时加载 JSON 文件到内存"""
    global CONTRACT_TEMPLATES, PROMPTS
    
    # 加载单个合同模板
    loaded_templates = []
    if not os.path.exists(REFERS_DIR):
        logger.error(f"模板目录 '{REFERS_DIR}' 未找到。请确保目录存在。")
    else:
        for filename in os.listdir(REFERS_DIR):
            if filename.endswith("-json.json") or filename.endswith("_processed.json"): # 支持两种文件名约定
                filepath = os.path.join(REFERS_DIR, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                        template_data['template_filename'] = filename # 将文件名存入模板数据
                        loaded_templates.append(template_data)
                    logger.info(f"成功加载合同模板: {filename}")
                except FileNotFoundError: # 虽然 listdir 后不太可能，但以防万一
                    logger.error(f"合同模板文件 '{filepath}' 未找到。")
                except json.JSONDecodeError:
                    logger.error(f"合同模板文件 '{filepath}' 格式错误。")
    
    CONTRACT_TEMPLATES = loaded_templates
    if CONTRACT_TEMPLATES:
        logger.info(f"成功加载 {len(CONTRACT_TEMPLATES)} 个合同模板从 '{REFERS_DIR}' 目录。")
    else:
        logger.warning(f"未从 '{REFERS_DIR}' 目录加载任何合同模板。")

    try:
        with open('prompts.json', 'r', encoding='utf-8') as f:
            PROMPTS = json.load(f)
        logger.info(f"成功加载 {len(PROMPTS)} 条 Prompts。")
    except FileNotFoundError:
        logger.error("'prompts.json' 未找到。请确保文件存在。")
        PROMPTS = {}
    except json.JSONDecodeError:
        logger.error("'prompts.json' 格式错误。")
        PROMPTS = {}

    if not os.path.exists(GENERATED_CONTRACTS_DIR):
        os.makedirs(GENERATED_CONTRACTS_DIR)
        logger.info(f"创建文件夹: {GENERATED_CONTRACTS_DIR}")

# 在应用启动前加载数据
load_data()

# --- LLM 调用 ---
def get_llm_response(prompt_name: str, user_input: str, context_data: dict = None) -> dict:
    """
    调用大模型API获取响应。
    """
    logger.info(f"LLM调用 - Prompt: {prompt_name}")
    if not PROMPTS:
        logger.error("Prompts 未加载，无法进行 LLM 调用。")
        return {"error": "Prompts未加载"}

    prompt_config = PROMPTS.get(prompt_name)
    if not prompt_config:
        logger.error(f"未找到名为 '{prompt_name}' 的Prompt配置。")
        return {"error": f"Prompt '{prompt_name}' 未配置"}

    system_message = prompt_config.get("system_message", "")
    user_prompt_template = prompt_config.get("user_prompt_template", "")

    placeholder_values = {}
    if prompt_name == "template_selection":
        placeholder_values = {
            "__USER_INPUT__": user_input,
            "__TEMPLATES_SUMMARY_JSON__": context_data.get("templates_summary_json", "[]")
        }
    elif prompt_name == "content_processing": # 修改以适应批处理
        placeholder_values = {
            "__USER_OVERALL_INPUT__": user_input, 
            "__CURRENT_TEXT_BLOCK_JSON__": context_data.get("current_text_block_json", "{}") # 键名修改
        }
    else:
        logger.warning(f"未为 prompt_name '{prompt_name}' 配置特定的 placeholder_values 构建逻辑。")
        placeholder_values = {
            "__USER_INPUT__": user_input,
            "__USER_OVERALL_INPUT__": user_input
        }
        if context_data:
            placeholder_values.update(context_data)

    try:
        final_user_prompt = user_prompt_template
        for key, value in placeholder_values.items():
            if key in final_user_prompt:
                 final_user_prompt = final_user_prompt.replace(key, str(value))
        
    except Exception as e:
        logger.error(f"填充Prompt模板 '{prompt_name}' 时发生未知错误: {e}")
        return {"error": f"填充Prompt模板时发生未知错误: {e}"}

    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": final_user_prompt}
    ]
    request_body = {"messages": messages, "stream": False}

    logger.info(f"向LLM API发送请求: URL={LLM_API_URL}, Prompt='{prompt_name}'")
    # logger.debug(f"LLM Request Body for {prompt_name}: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

    try:
        response = requests.post(LLM_API_URL, headers=LLM_API_HEADERS, json=request_body, timeout=LLM_REQUEST_TIMEOUT)
        response.raise_for_status()
        response_json = response.json()
        logger.info(f"LLM API响应成功接收, Prompt='{prompt_name}'")

        if not response_json.get("choices") or not isinstance(response_json["choices"], list) or len(response_json["choices"]) == 0:
            logger.error("LLM响应格式错误: 'choices' 数组为空或不存在。")
            return {"error": "LLM响应格式错误: choices为空"}
        
        message_content = response_json["choices"][0].get("message", {}).get("content")
        if message_content is None:
            logger.error("LLM响应格式错误: 未找到 'content'。")
            return {"error": "LLM响应格式错误: content未找到"}

        # logger.debug(f"LLM Raw Message Content for {prompt_name}: {message_content}")

        if prompt_name == "template_selection":
            return {"selected_template_filename": message_content.strip()}
        elif prompt_name == "content_processing":
            # 对于批处理，期望LLM返回一个JSON字符串，该字符串解析后是一个字典
            # 例如：{"path.to.text1": "new text1", "path.to.text2": "[nochange]"}
            try:
                # 尝试去除可能的markdown代码块标记
                cleaned_content = message_content.strip()
                if cleaned_content.startswith("```json"):
                    cleaned_content = cleaned_content[7:]
                if cleaned_content.startswith("```"): # 有些模型可能只用 ```
                    cleaned_content = cleaned_content[3:]
                if cleaned_content.endswith("```"):
                    cleaned_content = cleaned_content[:-3]
                
                processed_block_data = json.loads(cleaned_content)
                if not isinstance(processed_block_data, dict):
                    logger.error(f"LLM content_processing响应不是预期的字典格式: {type(processed_block_data)}. Content: {cleaned_content[:200]}")
                    return {"error": "LLM content_processing响应不是字典"}
                return {"processed_block": processed_block_data} # 返回解析后的字典
            except json.JSONDecodeError as e:
                logger.error(f"解析LLM content_processing响应时发生JSONDecodeError: {e}. LLM响应文本: {message_content[:500]}")
                return {"error": f"解析LLM content_processing响应失败: {e}"}
        else:
            logger.warning(f"未为 prompt_name '{prompt_name}' 配置特定的响应处理逻辑。返回原始content。")
            return {"llm_raw_content": message_content}

    except requests.exceptions.Timeout:
        logger.error(f"LLM API请求超时 ({LLM_REQUEST_TIMEOUT}秒)。")
        return {"error": "LLM API请求超时"}
    except requests.exceptions.HTTPError as e:
        logger.error(f"LLM API HTTP错误: {e} - 响应体: {e.response.text if e.response else 'N/A'}")
        return {"error": f"LLM API HTTP错误: {e.response.status_code if e.response else 'N/A'}"}
    except requests.exceptions.RequestException as e:
        logger.error(f"LLM API请求失败: {e}")
        return {"error": f"LLM API请求失败: {e}"}
    except json.JSONDecodeError as e: 
        logger.error(f"解析LLM API原始响应时发生JSONDecodeError: {e}. 响应文本: {response.text if 'response' in locals() else 'N/A'}")
        return {"error": "解析LLM API原始响应失败"}
    except Exception as e:
        logger.error(f"处理LLM响应时发生未知错误: {e}")
        return {"error": f"处理LLM响应时发生未知错误: {e}"}

# --- Markdown 转 DOCX ---
def markdown_to_docx(markdown_content: str, contract_name: str) -> str | None:
    """使用pandoc将Markdown字符串转换为DOCX文件。返回文件路径或None。"""
    try:
        file_id = uuid.uuid4()
        md_filename = os.path.join(GENERATED_CONTRACTS_DIR, f"{contract_name}_{file_id}.md")
        docx_filename = os.path.join(GENERATED_CONTRACTS_DIR, f"{contract_name}_{file_id}.docx")

        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.info(f"尝试将 {md_filename} 转换为 {docx_filename} 使用 pandoc")
        # pandoc input.md -o output.docx
        result = subprocess.run(['pandoc', md_filename, '-o', docx_filename],
                                capture_output=True, text=True, check=False)
        
        if result.returncode == 0:
            logger.info(f"Pandoc转换成功: {docx_filename}")
            return docx_filename
        else:
            logger.error(f"Pandoc转换失败。返回码: {result.returncode}")
            logger.error(f"Pandoc stdout: {result.stdout}")
            logger.error(f"Pandoc stderr: {result.stderr}")
            return None
    except FileNotFoundError:
        logger.error("Pandoc未找到。请确保pandoc已安装并在系统PATH中。")
        return None
    except Exception as e:
        logger.error(f"Markdown转DOCX时发生未知错误: {e}")
        return None
    finally:
        # 可选：完成后删除临时的md文件
        if os.path.exists(md_filename):
            try:
                os.remove(md_filename)
            except Exception as e:
                logger.warning(f"无法删除临时md文件 {md_filename}: {e}")

# --- 新的辅助函数：按llm_group_id收集文本片段 ---
def _collect_text_fragments_by_group_recursive(node: any, current_path: list, groups_data: dict, current_group_id: str = None):
    """递归辅助函数，用于收集带有llm_group_id的文本片段。"""
    if isinstance(node, dict):
        # 检查当前节点是否定义了新的llm_group_id
        node_group_id = node.get("llm_group_id") if isinstance(node.get("llm_group_id"), str) else current_group_id

        for key, value in node.items():
            if key == "llm_group_id": # 不处理llm_group_id键本身
                continue
            new_path = current_path + [key]
            _collect_text_fragments_by_group_recursive(value, new_path, groups_data, node_group_id)

    elif isinstance(node, list):
        for i, item in enumerate(node):
            new_path = current_path + [i]
            _collect_text_fragments_by_group_recursive(item, new_path, groups_data, current_group_id)

    elif isinstance(node, str) and node.strip(): # 只处理非空字符串
        if current_group_id and _is_value_needing_processing(current_path, node):
            # 将路径转换为字符串key，因为JSON对象的键必须是字符串
            path_key = ".".join(map(str, current_path))
            if current_group_id not in groups_data:
                groups_data[current_group_id] = []
            groups_data[current_group_id].append({
                "path": current_path, # 保留原始路径列表用于后续更新
                "path_key": path_key, # 字符串路径用作LLM输入/输出的键
                "original_text": node
            })

def _collect_text_fragments_by_group(template_data: dict) -> dict:
    """收集模板中所有按llm_group_id分组的、需要LLM处理的文本片段。"""
    groups_data = {} # 例如: {"main_content_block": [{"path": ["sections",0,"content"], "path_key": "sections.0.content", "original_text": "..."}]}
    _collect_text_fragments_by_group_recursive(template_data, [], groups_data)
    logger.debug(f"Collected text fragments by group: {json.dumps({k: len(v) for k, v in groups_data.items()}, indent=2)}")
    return groups_data

# --- 核心合同生成逻辑 ---

def _is_value_needing_processing(key_path: list[str], value: any) -> bool:
    """根据键路径和值判断是否需要LLM处理"""
    if not isinstance(value, str) or not value.strip(): # 只处理非空字符串
        return False

    # 顶层键直接判断
    if len(key_path) == 1:
        key = key_path[0]
        if key in ["template_version", "template_note", "format"]:
            return False
        # 包含占位符的通常需要处理
        if "【" in value or "[" in value: # 简单判断，[ ] 可能需要更精确
            return True
        # contract_type, preamble 等顶层文本字段需要处理
        if key in ["contract_type", "preamble"]:
            return True
        return False # 其他顶层键默认为元数据

    # 多层路径判断 (示例性，需要根据实际JSON结构完善)
    # 例如，不处理 sections[0].section_number, subsections[0].subsection_number
    last_key = key_path[-1]
    if last_key in ["section_number", "subsection_number", "purpose", "format"]:
        return False
    
    # 不处理 key_points 数组内的字符串
    if len(key_path) > 1 and key_path[-2] == "key_points" and isinstance(value, str):
        return False

    # 不处理 formatting_notes 下的内容
    if key_path[0] == "formatting_notes":
        return False

    # metadata 下的特定字段
    if key_path[0] == "metadata":
        if last_key in ["contract_number", "contract_title", "sign_date"]:
            return True
        return False

    # parties 下的特定字段
    if key_path[0] == "parties":
        # parties.party_a.name, parties.party_a.address etc.
        if last_key in ["name", "role", "address", "contact_person", "contact_method"]:
            return True
        return False
    
    # data_description 下的特定字段
    if key_path[0] == "data_description":
        if last_key in ["data_name", "data_content", "data_nature", "exclusions"]:
            return True
        return False

    # sections[i].section_title, sections[i].content
    # sections[i].subsections[j].content
    # sections[i].subsections[j].list_content[k] (如果 list_content 的项是字符串)
    # sections[i].subsections[j].table_content[k][l] (如果 table_content 的项是字符串)
    # 对于 sections/subsections/list_content/table_content 内部的字符串，通常都需要处理
    # 简化：如果父路径包含 sections/subsections 且值是字符串，则处理，除非被前面的特定键名排除
    is_content_like = False
    for p_key in key_path:
        if isinstance(p_key, str) and p_key in ["sections", "subsections", "list_content", "table_content", "content", "section_title", "subsection_title"]:
            is_content_like = True
            break
    if is_content_like and isinstance(value, str):
         # 再次检查是否被特定键名排除了
        if last_key in ["section_number", "subsection_number", "purpose", "format"]: # 确保这些不被处理
            return False
        return True

    # signature_block 下的特定字段
    if key_path[0] == "signature_block":
        if last_key in ["company_seal", "date", "representative"]:
             return True
        return False

    # 默认不处理，以防意外修改了我们不希望LLM碰的结构性字符串或ID等
    logger.debug(f"Value for key path {key_path} with value '{value[:30]}...' WILL NOT be processed by LLM by default.")
    return False

# --- Markdown 组装辅助函数 (骨架) ---
def _build_markdown_from_json(data: dict) -> str:
    markdown_parts = [] 

    # 检查数据格式：如果是 _processed.json 格式，使用简化处理
    if "header" in data and "sections" in data and "metadata" in data:
        # 这是 _processed.json 格式
        return _build_markdown_from_processed_json(data)

    # 处理复杂的 JSON 格式（如 data-confidentiality-agreement-json.json）
    # 1. 处理合同标题
    metadata = data.get("metadata", {})
    if metadata.get("contract_title"):
        markdown_parts.append(f"# {metadata['contract_title']}\n\n")
    
    # 处理合同编号
    if metadata.get("contract_number"):
        markdown_parts.append(f"**{metadata['contract_number']}**\n\n")
    
    # 处理签订日期
    if metadata.get("sign_date"):
        markdown_parts.append(f"**签订时间：{metadata['sign_date']}**\n\n")
    
    # 2. 处理甲乙双方信息
    parties = data.get("parties", {})
    party_a = parties.get("party_a", {})
    party_b = parties.get("party_b", {})
    
    if party_a.get("name"):
        markdown_parts.append(f"**甲方：{party_a['name']}**\n")
        if party_a.get("role"):
            markdown_parts.append(f"（{party_a['role']}）\n")
        markdown_parts.append("\n")
    
    if party_a.get("address"):
        markdown_parts.append(f"联系地址：{party_a['address']}\n\n")
    if party_a.get("contact_person"):
        markdown_parts.append(f"联系人：{party_a['contact_person']}\n\n")
    if party_a.get("contact_method"):
        markdown_parts.append(f"联系方式：{party_a['contact_method']}\n\n")
    
    if party_b.get("name"):
        markdown_parts.append(f"**乙方：{party_b['name']}**\n")
        if party_b.get("role"):
            markdown_parts.append(f"（{party_b['role']}）\n")
        markdown_parts.append("\n")
    
    if party_b.get("address"):
        markdown_parts.append(f"联系地址：{party_b['address']}\n\n")
    if party_b.get("contact_person"):
        markdown_parts.append(f"联系人：{party_b['contact_person']}\n\n")
    if party_b.get("contact_method"):
        markdown_parts.append(f"联系方式：{party_b['contact_method']}\n\n")
    
    markdown_parts.append("甲方和乙方在本合同中单独称为\"一方\"，合称\"双方\"。\n\n")
    
    # 3. 处理序言
    preamble = data.get("preamble")
    if preamble and isinstance(preamble, str):
        markdown_parts.append(f"## 序言\n\n{preamble}\n\n")
    
    # 4. 处理数据描述（如果存在）
    data_desc = data.get("data_description", {})
    if data_desc:
        markdown_parts.append("## 数据描述\n\n")
        if data_desc.get("data_name"):
            markdown_parts.append(f"**数据名称：** {data_desc['data_name']}\n\n")
        if data_desc.get("data_content"):
            markdown_parts.append(f"**数据内容：** {data_desc['data_content']}\n\n")
        if data_desc.get("data_nature"):
            markdown_parts.append(f"**数据性质：** {data_desc['data_nature']}\n\n")
        if data_desc.get("exclusions"):
            markdown_parts.append(f"**排除条款：** {data_desc['exclusions']}\n\n")
    
    # 5. 处理章节
    sections = data.get("sections", [])
    for section in sections:
        if not isinstance(section, dict):
            continue
            
        sec_num = section.get("section_number", "")
        sec_title = section.get("section_title", "")
        
        if sec_num and sec_title:
            markdown_parts.append(f"## {sec_num}、{sec_title}\n\n")
        elif sec_title:
            markdown_parts.append(f"## {sec_title}\n\n")
        
        # 处理章节直接内容
        content = section.get("content")
        if content and isinstance(content, str):
            markdown_parts.append(f"{content}\n\n")
        
        # 处理子章节
        subsections = section.get("subsections", [])
        for subsection in subsections:
            if not isinstance(subsection, dict):
                continue
                
            sub_num = subsection.get("subsection_number", "")
            sub_title = subsection.get("subsection_title", "")
            sub_content = subsection.get("content", "")
            
            if sub_title:
                markdown_parts.append(f"### {sub_num}、{sub_title}\n\n")
            elif sub_num:
                markdown_parts.append(f"**{sub_num}.** ")
            
            if sub_content:
                markdown_parts.append(f"{sub_content}\n\n")
    
    # 6. 处理签署区
    sig_block = data.get("signature_block")
    if sig_block:
        markdown_parts.append("## 签署区\n\n")
        if isinstance(sig_block, dict):
            party_a_sig = sig_block.get("party_a_signature", {})
            party_b_sig = sig_block.get("party_b_signature", {})
            
            if party_a_sig.get("company_seal"):
                markdown_parts.append(f"**{party_a_sig['company_seal']}**\n\n")
            if party_a_sig.get("date"):
                markdown_parts.append(f"{party_a_sig['date']}\n\n")
            
            if party_b_sig.get("company_seal"):
                markdown_parts.append(f"**{party_b_sig['company_seal']}**\n\n")
            if party_b_sig.get("date"):
                markdown_parts.append(f"{party_b_sig['date']}\n\n")
        elif isinstance(sig_block, str):
            markdown_parts.append(f"{sig_block}\n\n")

    return "".join(markdown_parts)



def _build_markdown_from_processed_json(data: dict) -> str:
    """处理 _processed.json 格式的数据"""
    markdown_parts = []
    
    # 1. 处理头部信息
    header = data.get("header", {})
    metadata = data.get("metadata", {})
    
    # 合同标题
    if metadata.get("template_name"):
        markdown_parts.append(f"# {metadata['template_name']}\n\n")
    
    # 合同编号和签订时间
    if header.get("contract_number"):
        markdown_parts.append(f"**合同编号：{header['contract_number']}**\n\n")
    if header.get("signing_date"):
        markdown_parts.append(f"**签订时间：{header['signing_date']}**\n\n")
    
    # 甲乙双方信息
    parties = header.get("parties", {})
    party_a = parties.get("party_a", {})
    party_b = parties.get("party_b", {})
    
    if party_a.get("name"):
        markdown_parts.append(f"**甲方：{party_a['name']}**\n\n")
    if party_a.get("address"):
        markdown_parts.append(f"联系地址：{party_a['address']}\n\n")
    if party_a.get("contact"):
        markdown_parts.append(f"联系方式：{party_a['contact']}\n\n")
    
    if party_b.get("name"):
        markdown_parts.append(f"**乙方：{party_b['name']}**\n\n")
    if party_b.get("address"):
        markdown_parts.append(f"联系地址：{party_b['address']}\n\n")
    if party_b.get("contact"):
        markdown_parts.append(f"联系方式：{party_b['contact']}\n\n")
    
    markdown_parts.append("甲方和乙方在本合同中单独称为\"一方\"，合称\"双方\"。\n\n")
    
    # 2. 处理章节
    sections = data.get("sections", [])
    for section in sections:
        if not isinstance(section, dict):
            continue
            
        title = section.get("title", "")
        content = section.get("content", "")
        
        if title:
            markdown_parts.append(f"## {title}\n\n")
        
        if content and content.strip():
            markdown_parts.append(f"{content}\n\n")
    
    # 3. 处理签署区
    sig_block = data.get("signature_block")
    if sig_block and isinstance(sig_block, str) and sig_block.strip():
        markdown_parts.append("## 签署区\n\n")
        markdown_parts.append(f"{sig_block}\n\n")
    
    return "".join(markdown_parts)

def generate_contract_content(user_input: str) -> tuple[str | None, str | None, list[str] | None, str | None]: # 返回值增加 logs
    """
    根据用户输入生成合同内容。
    返回 (generated_markdown_content, selected_template_filename, accumulated_logs, error_message)
    """
    logs_accumulator = [] # 用于收集处理过程中的日志信息给前端

    # 1. 选择模板
    templates_summary = []
    for t in CONTRACT_TEMPLATES:
        summary = {
            "template_filename": t.get("template_filename"),
            "contract_type": t.get("contract_type"),
            "template_note": t.get("template_note"),
            "contract_title": t.get("metadata", {}).get("contract_title")
        }
        templates_summary.append(summary)
    
    logs_accumulator.append("步骤1: 正在选择合适的合同模板...")
    logger.debug(f"Templates summary for LLM: {json.dumps(templates_summary, ensure_ascii=False, indent=2)}")

    llm_response_selection = get_llm_response(
        prompt_name="template_selection",
        user_input=user_input,
        context_data={"templates_summary_json": json.dumps(templates_summary, ensure_ascii=False)}
    )

    if "error" in llm_response_selection or "selected_template_filename" not in llm_response_selection:
        error_msg = f"模板选择失败: {llm_response_selection.get('error', '未知错误')}"
        logs_accumulator.append(f"错误: {error_msg}")
        logger.error(error_msg)
        return None, None, logs_accumulator, error_msg
    
    selected_template_filename = llm_response_selection["selected_template_filename"]
    logs_accumulator.append(f"步骤1: 成功选择模板 '{selected_template_filename}'")
    logger.info(f"LLM选定模板文件名 '{selected_template_filename}'")

    selected_template_data = next((t for t in CONTRACT_TEMPLATES if t.get("template_filename") == selected_template_filename), None)
    
    if not selected_template_data:
        error_msg = f"未在已加载的CONTRACT_TEMPLATES中找到文件名为 '{selected_template_filename}' 的模板数据。"
        logs_accumulator.append(f"错误: {error_msg}")
        logger.error(error_msg)
        return None, selected_template_filename, logs_accumulator, error_msg
    
    logger.info(f"成功匹配到模板对象: {selected_template_data.get('template_note', selected_template_filename)}")

    import copy
    modifiable_template_data = copy.deepcopy(selected_template_data)

    # 2. 按llm_group_id收集所有需要处理的文本片段
    logs_accumulator.append("步骤2: 正在从模板中收集各逻辑块的文本内容...")
    grouped_text_fragments = _collect_text_fragments_by_group(modifiable_template_data)
    
    if not grouped_text_fragments:
        logs_accumulator.append("警告: 未在模板中找到任何可供LLM处理的文本片段组。将使用原始模板。")
        logger.warning("未在模板中找到任何按llm_group_id分组的可处理文本片段。")
    else:
        logs_accumulator.append(f"步骤2: 成功收集到 {len(grouped_text_fragments)} 个文本逻辑块。")

        # 定义处理顺序
        processing_order = ["metadata_block", "main_content_block", "payment_block", "standard_clauses_block"]
        # 也可以从 grouped_text_fragments.keys() 获取所有实际存在的组，并按预定义顺序排序

        for group_id in processing_order:
            if group_id not in grouped_text_fragments or not grouped_text_fragments[group_id]:
                logger.info(f"跳过空的或不存在的文本组: {group_id}")
                logs_accumulator.append(f"处理 {group_id}: 该块无内容或无需处理。")
                continue

            current_block_fragments = grouped_text_fragments[group_id]
            # 构建给LLM的JSON对象: {"path.key1": "text1", "path.key2": "text2", ...}
            block_to_llm = {item["path_key"]: item["original_text"] for item in current_block_fragments}
            
            log_msg_start = f"步骤2.{processing_order.index(group_id)+1}: 开始处理逻辑块 '{group_id}' ({len(block_to_llm)} 项文本)..."
            logs_accumulator.append(log_msg_start)
            logger.info(log_msg_start)

            llm_response_block = get_llm_response(
                prompt_name="content_processing",
                user_input=user_input, # 用户整体需求
                context_data={"current_text_block_json": json.dumps(block_to_llm, ensure_ascii=False)}
            )

            if "error" in llm_response_block or "processed_block" not in llm_response_block:
                error_msg_block = f"处理逻辑块 '{group_id}' 时LLM出错: {llm_response_block.get('error', '未知错误')}. 该块内容将保持不变。"
                logs_accumulator.append(f"错误: {error_msg_block}")
                logger.error(error_msg_block)
                continue # 继续处理下一个块
            
            processed_results = llm_response_block["processed_block"] # 这是个字典 {"path.key": "new_text_or_nochange"}
            changes_count = 0
            no_changes_count = 0

            # 根据LLM返回的结果更新 modifiable_template_data
            for fragment_info in current_block_fragments:
                original_path = fragment_info["path"]      # list of keys/indices
                path_key_str = fragment_info["path_key"]   # "key1.0.key2"

                if path_key_str in processed_results:
                    llm_output_for_fragment = processed_results[path_key_str]
                    if isinstance(llm_output_for_fragment, str) and llm_output_for_fragment.strip().lower() == "[nochange]":
                        no_changes_count +=1
                    else:
                        # 更新 modifiable_template_data 中的值
                        current_level = modifiable_template_data
                        for i, path_segment in enumerate(original_path):
                            if i == len(original_path) - 1: # 到达最后一个路径段，设置新值
                                if isinstance(current_level, dict) and path_segment in current_level:
                                    current_level[path_segment] = llm_output_for_fragment
                                    changes_count += 1
                                elif isinstance(current_level, list) and isinstance(path_segment, int) and path_segment < len(current_level):
                                    current_level[path_segment] = llm_output_for_fragment
                                    changes_count += 1
                                else:
                                    logger.warning(f"无法在路径 {original_path} 为逻辑块 '{group_id}' 设置值 '{llm_output_for_fragment[:30]}...'. 当前层级类型: {type(current_level)}")
                                    logs_accumulator.append(f"警告: 更新路径 {path_key_str} 失败。")
                            else: # 继续深入
                                if isinstance(current_level, dict) and path_segment in current_level:
                                    current_level = current_level[path_segment]
                                elif isinstance(current_level, list) and isinstance(path_segment, int) and path_segment < len(current_level):
                                    current_level = current_level[path_segment]
                                else:
                                    logger.warning(f"路径 {original_path} 在深入到 {path_segment} 时中断。")
                                    logs_accumulator.append(f"警告: 路径 {path_key_str} 无效。")
                                    break # 跳出内部循环，此片段更新失败
                else:
                    logger.warning(f"LLM响应中未包含路径键 '{path_key_str}' (来自逻辑块 '{group_id}')。该片段将保持不变。")
                    logs_accumulator.append(f"警告: LLM未返回路径 {path_key_str} 的结果。")
            
            log_msg_end = f"步骤2.{processing_order.index(group_id)+1}: 逻辑块 '{group_id}' 处理完成。修改了 {changes_count} 项，{no_changes_count} 项无变化。"
            logs_accumulator.append(log_msg_end)
            logger.info(log_msg_end)

    # 3. 从处理后的JSON组装最终Markdown
    logs_accumulator.append("步骤3: 正在从更新后的模板数据生成最终Markdown文档...")
    logger.info(f"开始从处理后的JSON数据组装Markdown...")
    final_markdown_content = _build_markdown_from_json(modifiable_template_data) # 使用更新后的数据
    logs_accumulator.append("步骤3: Markdown文档生成成功。")
    logger.info(f"INFO: 最终合同Markdown组装完成。")
    # --- DEBUGGING ---
    app.logger.debug("---------- DEBUG: Final Processed Template Data START ----------")
    processed_data_str = json.dumps(modifiable_template_data, ensure_ascii=False, indent=2)
    app.logger.debug(processed_data_str)
    app.logger.debug("---------- DEBUG: Final Processed Template Data END ----------")
    # --- END DEBUGGING ---
    
    return final_markdown_content, selected_template_filename, logs_accumulator, None

# --- API 端点 ---
@app.route('/chat', methods=['POST'])
def chat_handler():
    data = request.json
    user_input = data.get('user_input')

    if not user_input:
        return jsonify({"error": "请求体中未提供 'user_input'"}), 400
    
    logger.info(f"收到 /chat 请求, user_input: {user_input[:100]}...")

    markdown_content, template_name, logs, error = generate_contract_content(user_input) # 接收logs

    if error:
        return jsonify({"error": error, "selected_template_name": template_name, "logs": logs}), 500 # 返回logs
    
    if not markdown_content:
        return jsonify({"error": "未能生成合同内容", "selected_template_name": template_name, "logs": logs}), 500 # 返回logs

    return jsonify({
        "message": "合同已生成", 
        "selected_template_name": template_name,
        "contract_markdown": markdown_content,
        "logs": logs # 返回logs
    }), 200

@app.route('/download_contract', methods=['POST'])
def download_handler():
    data = request.json
    markdown_content = data.get('markdown_content')
    contract_name = data.get('contract_name', 'GeneratedContract') # 客户端可以指定名称

    if not markdown_content:
        return jsonify({"error": "请求体中未提供 'markdown_content'"}), 400
    
    logger.info(f"收到 /download_contract 请求, 合同名称: {contract_name}")

    docx_filepath = markdown_to_docx(markdown_content, contract_name)

    if not docx_filepath:
        return jsonify({"error": "Markdown转DOCX失败"}), 500
    
    try:
        return send_file(docx_filepath, as_attachment=True, download_name=os.path.basename(docx_filepath))
    except Exception as e:
        logger.error(f"发送文件时出错: {e}")
        return jsonify({"error": "发送文件失败"}), 500
    # finally:
        # 可选：发送后删除服务器上的docx文件
        # if os.path.exists(docx_filepath):
        #     try:
        #         os.remove(docx_filepath)
        #     except Exception as e:
        #        logger.warning(f"无法删除已发送的docx文件 {docx_filepath}: {e}")

# --- 新增前端服务路由 ---
@app.route('/')
def serve_index():
    return send_from_directory('static', 'index.html')

@app.route('/static/<path:filename>') # 如果index.html引用了其他css/js文件，也需要路由
def serve_static(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    app.run(debug=True, port=5001) 